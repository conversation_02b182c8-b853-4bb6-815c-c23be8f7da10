Your goal is create the component document located at `apps/docs/docs` which clone the legacy document is written at `apps/www`

Ask for target legacy component

Requirement:

- Before process any modification/ analyzing. You must explore the `apps/www` folder first. To see the content of the legacy component document.
- The Document files must located at `apps/docs/legacy/...`
- Create files of new component document following the convension of another component doc which at the same location.
- All convention must be the same with another component doc in the folder.
- The examples file must import the component from `@design-systems/apollo-ui` only.

- The content structure should be the same with another component document.
- The legacy component will be import from `@design-systems/apollo-ui`
- Must create the files at right location.

Must know:

- Ask if you don't know
