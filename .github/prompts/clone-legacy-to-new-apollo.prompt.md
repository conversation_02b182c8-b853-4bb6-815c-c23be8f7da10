Your goal is the recreate component into `@apollo/ui` (packages/apollo-ui) which clone the legacy component from `@design-systems/apollo-ui` (pacakges/ui).

Ask for target directory of legacy component

Requirement:

- Create a files of new component into under folder `packages/apollo-ui/src/components/<folder_name>
- All convention must be the same with another component in the folder.
- Unit-test will be under `pacakges/apollo-ui/__tests__`. Also need to be written with the same pattern of another test file. So check on #codebase
- Prop should be the same as target component
- Instead using tailwindCSS. the `@apollo/ui` are using ModuleCSS
- Must use `packages/apollo-token/src/generated-files/apolloTheme.ts` as references for CSS variable if need instead of using hard code
- Each component files that being create must use Module CSS and existing package that available on `@apollo/ui`
- Must create the files at right location.
- In case unit-test already existed but not follow new pattern and convention you need to re-create that unit-test instead of reuse the legacy one.

Must know:

- Ask if you don't know
