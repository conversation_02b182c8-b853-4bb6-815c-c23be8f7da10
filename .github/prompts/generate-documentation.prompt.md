Your goal is the documentation
Ask for target directory of legacy component

Requirement:

- You need to run build at `package/apollo-ui` one time.
- The examples of documentation must be import from `@apollo/ui`
- Add documentation of the component at `apps/docs/docs/Components/<related_group>`
- Ask for related group name
- Create documentation files which the pattern and convention should be the same with another component. So please carefully read the #codebase of another documentation.
- Must have examples files for use cases

Must know:

- Ask if you don't know
