## Dev Environment Tip

- Use #playwright mcp as inspector and testing on browser tool
- storybook is running at localhost port 6006
- the existing documentation is running at port 3000 (which we need to move this into storybook)
- Storybook files will be store at `apps/apollo-docs/src/stories`
- There're two group of stories which is `@apollo/ui` and `@design-systems/apollo-ui`
- `@design-systems/apollo-ui` will be consider as legacy design system which source code is located at `packages/ui/*`
- `@apollo/ui` will be the new design system which source code is located at `packages/apollo-ui/*`
- We move the documentation of `@apollo/ui` first then `@design-systems/apollo-ui`
- both of version still being use in differnce projects. So we need stories file for both.

## Main Task

- Migrate all component documentation to be storybook stories via mdx documentation. (Priority in component first)
- Component preview part will be canvas from stories files

## Step

- (If not tracking file) List all components for each version and check box as progress tracking
- (Else) Read the tracking file for progress and pick component to work.
- Read existing component and related example / documentaion files
- Create story file with mdx documentation
- use #playwright mcp to test the stories
- Fix if necessary
