Your goal is to validate if the component was implemented correctly to the rule

Ask for folder of component

**Rules:**

1. Each section of component should have Apollo className at the first of className props. If the target component have `className` prop only

- Syntax: "Apollo<ComponentName>-<ComponentPart>"
- Example: "ApolloButton-root"

2. Each section of component should have its own slot props to be able to custom the base DOM. There are two valid patterns:

   a) For composite components with nested structure:

   - Use a dedicated prop for the DOM element (e.g., rootProps, contentProps)
   - PropName Syntax: "<partName>Props"
   - Example: "rootProps", "contentProps"

   b) For simple single-element components:

   - Props can be spread directly to the element (e.g., {...headerProps})
   - Type should extend the appropriate HTML element attributes
   - Example: export type ModalHeaderProps = PropsWithChildren<HTMLAttributes<HTMLDivElement>>

3. Each component parts should have its own CSS module file and use "<componentName>.module.css" convention

4. Each component parts should have its own index file to export the component
5. Each component must have `index.ts` file at the root of component folder to export all parts using these convention

```
export { ... } from 'componentPart' // for functional & component
export type { ... } from 'componentPart' // for types
```

6. The component should be exported at `packages/apollo-ui/src/components/index.ts`
