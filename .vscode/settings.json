{"editor.defaultFormatter": "esbenp.prettier-vscode", "javascript.validate.enable": false, "eslint.workingDirectories": [{"mode": "auto"}], "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cssVariables.lookupFiles": ["**/_variables*.css", "node_modules/@apollo/ui/dist/_variables*.css"], "cssVariables.languages": ["css"]}