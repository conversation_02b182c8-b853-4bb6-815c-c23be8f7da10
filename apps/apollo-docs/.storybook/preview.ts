import React from "react"
import { ApolloStorefrontToken } from "@apollo/token"
import { ApolloProvider, apolloToken, createThemeV2 } from "@apollo/ui"
import { DocsContainer, DocsPage, Unstyled } from "@storybook/addon-docs/blocks"
import type { Decorator, Preview } from "@storybook/nextjs-vite"
import isChromatic from "chromatic/isChromatic"

import { Footer, StorybookLink, TableOfContents } from "../src/components"

import "../src/app/globals.css"

const fontLoader = async () => ({
  fonts: await document.fonts.ready, // Fixing Chromatic tests flakiness - taking snapshots after fonts are loaded
})

// Toolbar controls to switch theme variant (Apollo/Storefront) and mode (Light/Dark)
export const globalTypes = {
  brand: {
    name: "Theme",
    description: "Design token set",
    defaultValue: "apollo",
    toolbar: {
      icon: "paintbrush",
      items: [
        { value: "apollo", title: "Apollo" },
        { value: "storefront", title: "Storefront" },
      ],
      dynamicTitle: true,
    },
  },
  mode: {
    name: "Mode",
    description: "Theme mode",
    defaultValue: "light",
    toolbar: {
      icon: "mirror",
      items: [
        { value: "light", title: "Light" },
        { value: "dark", title: "Dark" },
      ],
      dynamicTitle: true,
    },
  },
} as const

const withTheme: Decorator = (Story, context) => {
  const selected = (context.globals.mode as "light" | "dark") ?? "light"
  const brand = (context.globals.brand as "apollo" | "storefront") ?? "apollo"

  // Choose token set by brand
  const tokens = brand === "storefront" ? ApolloStorefrontToken : apolloToken
  const themed = createThemeV2({ tokens })

  // Sync global root for both Tailwind dark variants and CSS light-dark() tokens
  if (typeof document !== "undefined") {
    const root = document.documentElement
    root.classList.toggle("dark", selected === "dark")
    root.style.setProperty("color-scheme", selected)
  }

  return React.createElement(
    ApolloProvider as any,
    { themeProps: { theme: themed, mode: selected } },
    React.createElement(Story as any)
  )
}

const preview: Preview = {
  decorators: [withTheme],
  parameters: {
    options: {
      storySort: {
        order: [
          "Introduction",
          "@apollo∕ui",
          ["Components", "Changelogs"],
          "@apollo∕storefront",
          ["Introduction", "Changelogs", "Components"],
          "@design-systems∕apollo-ui",
          ["Components", "Changelogs"],
          "Icons",
        ],
      },
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      liveEdit: {
        isEnabled: true,
      },
      canvas: {
        layout: "fullscreen",
      },
      container: ({ children, context }) => {
        return React.createElement(
          DocsContainer,
          { context },
          React.createElement(Unstyled, null, children),
          React.createElement(Footer, null),
          React.createElement(TableOfContents, null)
        )
      },
      page: DocsPage,
      components: {
        StorybookLink,
      },
    },
    a11y: {
      // 'todo' - show a11y violations in the test UI only
      // 'error' - fail CI on a11y violations
      // 'off' - skip a11y checks entirely
      test: "todo",
    },
  },
  loaders: isChromatic() && document.fonts ? [fontLoader] : [],
}

export default preview
