import React, { ReactNode } from 'react';
import classNames from 'classnames';
import { RECOMMENDED_TITLE, NOT_RECOMMENDED_TITLE } from './constant';
import styles from './component-rule.module.css';
import { Check, Close } from '@design-systems/apollo-icons';

interface ComponentRuleProps {
  component: ReactNode | ReactNode[] ;
  description: ReactNode | ReactNode[];
  isRecommended: boolean;
  className?: string;
  componentContainerClassName?: string;
}

const ComponentRule: React.FC<ComponentRuleProps> = ({
  component,
  description = '',
  isRecommended = false,
  className,
  componentContainerClassName,
}) => {
  const titleIcon = isRecommended ? <Check className={styles.icon} /> : <Close className={styles.icon} />;
  const title = isRecommended ? RECOMMENDED_TITLE : NOT_RECOMMENDED_TITLE;

  return (
    <section
      className={classNames(
        styles.componentRule,
        {
          [styles.recommended]: isRecommended,
          [styles.notRecommended]: !isRecommended,
        },
        className,
      )}
    >
      <figure className={classNames(styles.component, componentContainerClassName)}>{component}</figure>
      <h5 className={styles.title}>
        {titleIcon}
        {title}
      </h5>
      <section className={styles.description}>{description}</section>
    </section>
  );
};

export default ComponentRule;