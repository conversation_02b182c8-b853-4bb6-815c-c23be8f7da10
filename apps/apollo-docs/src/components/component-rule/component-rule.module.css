.componentRule {
    .component {
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        border-radius: 8px;
        background: var(--sb-dark-background-on-secondary-color);

        &>* {
            margin-right: var(--sb-spacing-small);
        }

        &>video {
            object-fit: contain;
            object-position: center;
            width: calc(100% - 10px);
            height: calc(100% - 10px);
        }
    }

    .title {
        font-family: var(--sb-font-family);
        font-size: 1rem;
        line-height: var(--sb-text-line-height);
        color: var(--sb-primary-text-color);
        -webkit-font-smoothing: initial;
        margin-bottom: 8px;
        margin-top: 16px !important;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 12px;
        color: var(--sb-text-color-on-primary);
    }

    .description {
        font-family: var(--sb-font-family);
        font-size: 1rem;
        line-height: var(--sb-text-line-height);
        font-weight: 400;
        color: var(--sb-primary-text-color);
        -webkit-font-smoothing: initial;
    }

    &.recommended {
        .icon {
            background: var(--sb-positive-color);
        }
    }

    &.notRecommended {
        .icon {
            background: var(--sb-negative-color);
        }
    }
}