import React, { ReactNode, useMemo } from 'react';
import classNames from 'classnames';

import ComponentRule from '../component-rule/ComponentRule';
import styles from './component-rules.module.css';

interface ComponentRulesProps {
  rules: {
    positive: {
      component: ReactNode | ReactNode[];
      description: string | ReactNode | ReactNode[];
    };
    negative: {
      component: ReactNode | ReactNode[];
      description: string | ReactNode | ReactNode[];
    };
    className?: string;
    componentContainerClassName?: string;
  }[];
  className?: string;
}

const ComponentRules: React.FC<ComponentRulesProps> = ({ rules = [], className }) => {
  const componentRulesElements = useMemo(
    () =>
      rules.map((rule, index) => {
        const key = `rule-${index}`;

        return (
          <section className={classNames(styles.pair, className)} key={key}>
            <ComponentRule
              component={rule.positive.component}
              description={rule.positive.description}
              className={rule.className}
              componentContainerClassName={rule.componentContainerClassName}
              isRecommended
            />
            <ComponentRule
              component={rule.negative.component}
              description={rule.negative.description}
              className={rule.className}
              componentContainerClassName={rule.componentContainerClassName}
              isRecommended={false}
            />
          </section>
        );
      }),
    [className, rules],
  );

  return <article className={styles.componentRules}>{componentRulesElements}</article>;
};

export default ComponentRules;