import React from "react";
import styles from "./footer.module.css";
import FooterCard from "./FooterCard";
import { Typography } from "@apollo/ui";

export default function Footer() {
  return (
    <div className={styles.footer}>
      <div className={styles.footerContent}>
        <div className={styles.footerTitles}>
          <Typography level="headlineLarge" className={styles.title}>We&apos;re listening.</Typography>
          <p className={styles.subtitle}>
            We&apos;d love to hear your thoughts <br /> about <PERSON>!
          </p>
        </div>
        <div  className={styles.footerCards}>
          <FooterCard
            title="Bug report"
            description="Report a bug on our production"
            href="https://apollo-issue-apollo-issue.vercel.app/issues/new"
            linkText="See more"
          />
          <FooterCard
            title="General contact"
            description="General questions, feature requests, ideas, etc."
            href="https://teams.microsoft.com/l/chat/19:28314b2daa63406494537777d2839d55@thread.v2/conversations?context=%7B%22contextType%22%3A%22chat%22%7D"
            linkText="See more"
          />
        </div>
      </div>
    </div>
  );
}