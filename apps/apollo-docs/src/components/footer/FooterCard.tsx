import React from "react";
import styles from "./footer-card.module.css";
import ArrowIcon from "./ArrowIcon";
import { Typography } from "@apollo/ui";

export default function FooterCard({
  title,
  description,
  linkText,
  href
}: {
  title: string;
  description: string;
  linkText: string;
  href: string;
}) {
  return (
    <a href={href} className={styles.footerCard} target="_blank" rel="noreferrer noopener">
      <div className={styles.content}>
        <Typography  className={styles.title}>{title}</Typography>
        <div className={styles.description}>{description}</div>
      </div>
      <div className={styles.link}>
        {linkText} <ArrowIcon />
      </div>
    </a>
  );
}