.footer {
  padding: 0;
  padding-top: 40px;
  width: 100%;

  .footerContent {
    margin: 0;
  }

  .footerTitles {
    max-width: 320px;
    margin-bottom: 24px;
  }

  .title {
    color: var(--sb-primary-text-color);
    font-family: var(--sb-title-font-family);
    font-size: 32px;
    font-weight: 500;
    line-height: 105%;
    letter-spacing: -1.333px;
    margin: 0;
  }

  .subtitle {
    color: var(--sb-secondary-text-color);
    font-family: var(--sb-title-font-family);
    font-size: 16px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: 0.427px;
    margin: 12px 0 0 0;
  }

  .footerCards {
    width: 100%;
    display: flex;
    align-items: start;
    gap: 16px;
  }
}

@media screen and (max-width: 1200px) {
  .footer .footerContent {
    margin: 0;
  }

  .footer .footerTitles {
    max-width: 100%;
  }
  
}

@media screen and (max-width: 1024px) {
  .footer .footerContent {
    margin: 0;
  }
}