
.toc {
  position: fixed;
  right: 20px;
  top: 85px;
  z-index: 1000;
  border-radius: 12px;
  background-color: var(--sb-primary-background-color);
  border: 1px solid var(--sb-layout-border-color);

  .button {
    border-radius: 8px;

    &.open {
      border: none;
    }

    &:hover {
      background: none !important;
    }
  }

  &:hover {
    width: 250px;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.2);
  }

  .header {
    color: var(--sb-secondary-text-color);
    padding: 4px 8px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    margin-top: 16px;
    margin-left: 12px;
  }

  .list {
    list-style: none;
    padding: 0;
    margin: 0 12px 16px 12px;
  }

  .listItem {
    padding: 4px 8px;
    margin: 4px 0;
    color: var(--sb-secondary-text-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 20px;
    cursor: pointer;

    &.active {
      font-weight: 600;
      background-color: var(--sb-primary-background-hover-color);
      color: var(--sb-primary-text-color);
    }

    &:hover {
      background-color: var(--sb-primary-background-hover-color);
    }
  }
}