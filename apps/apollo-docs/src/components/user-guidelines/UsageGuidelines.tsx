import React, { ReactNode, useMemo } from 'react';
import styles from './usage-guidelines.module.css';

interface UsageGuidelinesProps {
  guidelines: Array<ReactNode | ReactNode[]>;
}

const UsageGuidelines: React.FC<UsageGuidelinesProps> = ({ guidelines = [] }) => {
  const guidelinesElements = useMemo(
    () =>
      guidelines.map((guideline, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <li key={index} className={styles.usageGuideline}>
          {guideline}
        </li>
      )),
    [guidelines],
  );

  return <ul className={styles.usageGuidelines}>{guidelinesElements}</ul>;
};

export default UsageGuidelines;