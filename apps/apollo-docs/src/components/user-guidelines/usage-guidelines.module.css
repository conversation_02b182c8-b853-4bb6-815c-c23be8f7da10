.usageGuidelines {
    margin: 0;
    padding-inline-start: 24px;

    .usageGuideline {
        font-family: var(--sb-font-family);
        font-size: 1rem;
        line-height: var(--sb-text-line-height);
        font-weight: 400;
        color: var(--sb-primary-text-color);
        margin: 16px 0;
        -webkit-font-smoothing: initial;

        &:first-child {
            margin-top: 0;
        }
    }

    code {
        display: inline-block;
        vertical-align: baseline;
        line-height: 1;
        margin: 0px 2px;
        padding: 3px 5px;
        white-space: nowrap;
        border-radius: 3px;
        font-size: 13px;
        color: rgba(46, 52, 56, 0.9);
        border: 1px solid rgb(236, 244, 249);
        background-color: rgb(247, 250, 252)
    }
}