import React from "react"
import { ProductBadge } from "@apollo/storefront"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductBadge displays badges on products to highlight special states like discounts,
 * low stock, monthly offers, or indicate when there are more items available.
 * Built on top of the Apollo UI Badge component with product-specific styling variants.
 */
const meta: Meta<typeof ProductBadge> = {
  title: "@apollo∕storefront/Components/ProductBadge",
  component: ProductBadge,
  parameters: {
    layout: "centered",
  },
  globals: {
    brand: "storefront",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["moreItem", "discount", "monthly", "lowStock"],
      description: "The variant style of the badge",
    },
    label: {
      control: "text",
      description: "The content to display inside the badge",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply",
    },
  },
} satisfies Meta<typeof ProductBadge>

export default meta
type Story = StoryObj<typeof ProductBadge>

export const Default: Story = {
  args: {
    variant: "moreItem",
    label: "+3 more",
  },
}
/**
 * All ProductBadge variants displayed together to show the different styling options.
 * This demonstrates the visual differences between moreItem, discount, monthly, and lowStock variants.
 */
export const AllVariants: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "16px",
        flexWrap: "wrap",
        alignItems: "center",
      }}
    >
      <ProductBadge variant="moreItem" label="+3 more" />
      <ProductBadge variant="discount" label="-20%" />
      <ProductBadge variant="monthly" label="Monthly Deal" />
      <ProductBadge variant="lowStock" label="Only 2 left" />
    </div>
  ),
}
