import React from "react"
import { ProductCardContent } from "@apollo/storefront"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"


/**
 * ProductCardContent displays the textual content of a product card including
 * title, caption, and pricing information. It handles text truncation and layout
 * for product information display.
 */
const meta: Meta<typeof ProductCardContent> = {
  title: "@apollo∕storefront/Components/ProductCardContent",
  component: ProductCardContent,
  parameters: {
    layout: "centered",
  },
  globals: {
    brand: "storefront",
  },
  decorators: [
    (Story) => (
        <div
          style={{
            width: 300,
            padding: 16,
            border: "1px solid #e0e0e0",
            position: "relative",
          }}
        >
          <span
            style={{
              position: "absolute",
              top: "-8px",
              backgroundColor: "black",
              borderRadius: "4px",
              color: "white",
              padding: "2px 6px",
              fontFamily: "Arial, sans-serif",
              fontSize: "8px",
            }}
          >
            this line is added from storybook
          </span>
          <Story />
        </div>
    ),
  ],
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The product title",
    },
    caption: {
      control: "text",
      description: "Additional caption or description for the product",
    },
    titleMaxLines: {
      control: "number",
      description: "Maximum number of lines for the title",
    },
    captionMaxLines: {
      control: "number",
      description: "Maximum number of lines for the caption",
    },
    price: {
      control: "object",
      description: "Price information object",
    },
  },
} satisfies Meta<typeof ProductCardContent>

export default meta
type Story = StoryObj<typeof ProductCardContent>

export const Default: Story = {
  args: {
    title: "Premium Organic Coffee Beans",
    caption:
      "Single origin arabica coffee beans from the highlands of Ethiopia",
    price: {
      price: "299",
      currency: "฿",
      unit: "pack",
    },
  },
}

/**
 * ProductCardContent with a long title that will be truncated based on titleMaxLines.
 */
export const LongTitle: Story = {
  args: {
    title:
      "Premium Organic Single Origin Fair Trade Ethiopian Highland Arabica Coffee Beans with Natural Processing Method",
    caption: "High quality coffee beans",
    titleMaxLines: 2,
    price: {
      price: "450",
      currency: "฿",
    },
  },
}

/**
 * ProductCardContent with both title and long caption that will be truncated.
 */
export const LongCaption: Story = {
  args: {
    title: "Artisan Coffee Blend",
    caption:
      "A carefully crafted blend of premium coffee beans from multiple origins, featuring notes of chocolate, caramel, and citrus. Perfect for both espresso and filter brewing methods. Roasted to perfection daily in small batches.",
    captionMaxLines: 2,
    price: {
      price: "350",
      currency: "฿",
      unit: "bag",
    },
  },
}

/**
 * ProductCardContent with discount pricing to show both regular and discounted prices.
 */
export const WithDiscount: Story = {
  args: {
    title: "House Blend Coffee",
    caption: "Our signature house blend",
    price: {
      price: "199",
      discountPrice: "249",
      currency: "฿",
      unit: "pack",
    },
  },
}

/**
 * ProductCardContent with only title and price, no caption.
 */
export const TitleOnly: Story = {
  args: {
    title: "Espresso Blend",
    price: {
      price: "180",
      currency: "฿",
    },
  },
}

/**
 * ProductCardContent with no price information.
 */
export const NoPrice: Story = {
  args: {
    title: "Coffee Subscription",
    caption: "Monthly delivery of fresh coffee beans",
  },
}

/**
 * Different line limit configurations shown side by side.
 */
export const LineLimits: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Default (3 lines title)
        </h4>
        <ProductCardContent
          title="Very Long Product Title That Should Be Truncated After Three Lines"
          caption="Short caption"
          titleMaxLines={3}
        />
      </div>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Limited (1 line title)
        </h4>
        <ProductCardContent
          title="Very Long Product Title That Should Be Truncated After One Line"
          caption="Short caption"
          titleMaxLines={1}
        />
      </div>
      <div style={{ width: 200, border: "1px solid #e0e0e0", padding: 16 }}>
        <h4 style={{ margin: "0 0 16px 0", fontSize: "14px" }}>
          Extended (2 lines caption)
        </h4>
        <ProductCardContent
          title="Product Title"
          caption="Very long caption that should be truncated after exactly two lines of text content"
          captionMaxLines={2}
        />
      </div>
    </div>
  ),
}
