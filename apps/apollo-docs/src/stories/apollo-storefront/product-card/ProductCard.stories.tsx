import React from "react"
import {
  ProductBadge,
  ProductCard,
} from "@apollo/storefront"
import { Button } from "@apollo/ui"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * ProductCard displays product information in a card format, typically used in
 * product listings, search results, and e-commerce interfaces. It supports
 * product images, titles, captions, pricing, and customizable layouts.
 */
const meta: Meta<typeof ProductCard> = {
  title: "@apollo∕storefront/Components/ProductCard",
  component: ProductCard,
  globals: {
    brand: "storefront",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=2637-6138&m=dev",
    },
  },
  tags: ["autodocs", "new"],
  argTypes: {
    imageSrc: {
      control: "text",
      description: "Source URL for the product image",
    },
    imageAlt: {
      control: "text",
      description: "Alt text for the product image",
    },
    title: {
      control: "text",
      description: "Title of the product",
    },
    caption: {
      control: "text",
      description: "Additional caption or description for the product",
    },
    fill: {
      control: "boolean",
      description: "Whether the card should fill available width",
    },
    titleMaxLines: {
      control: { type: "number", min: 1, max: 5 },
      description: "Maximum number of lines for the title",
    },
    captionMaxLines: {
      control: { type: "number", min: 1, max: 3 },
      description: "Maximum number of lines for the caption",
    },
    price: {
      control: "object",
      description: "Price configuration object",
    },
    renderImage: {
      control: false,
      description: "Custom render function for the product image section",
    },
    renderImageOverlay: {
      control: false,
      description:
        "Custom render function for image overlay content (badges, labels, etc.)",
    },
    renderContent: {
      control: false,
      description: "Custom render function for the product content section",
    },
    renderFooter: {
      control: false,
      description: "Custom render function for the product footer section",
    },
  },
} satisfies Meta<typeof ProductCard>

export default meta
type Story = StoryObj<typeof ProductCard>

// --- STORIES ---

/**
 * Default ProductCard with essential product information - image, title, and price.
 * This shows the most common use case for an e-commerce product card.
 */
export const Default: Story = {
  args: {
    title: "Premium Bluetooth Headphones",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Premium bluetooth headphones product image",
    price: {
      price: "1,599",
      currency: "฿",
    },
    renderFooter: () => (
      <Button fullWidth startDecorator={"+"}>
        Add to Cart
      </Button>
    ),
  },
}

/**
 * ProductCard with complete product information including price and caption.
 */
export const WithPrice: Story = {
  args: {
    title: "Thai Jasmine Rice Premium Quality",
    caption: "Premium organic rice from northern Thailand",
    imageSrc:
      "https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=300&fit=crop",
    imageAlt: "Thai jasmine rice product image",
    price: {
      price: "120",
      unit: "กล่อง",
      currency: "฿",
    },
  },
}

/**
 * ProductCard showing a discounted product with original and sale prices.
 */
export const WithDiscount: Story = {
  args: {
    title: "Organic Coffee Beans",
    caption: "Limited time offer - Premium arabica beans",
    imageSrc:
      "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=300&fit=crop",
    imageAlt: "Organic coffee beans product image",
    price: {
      price: "95",
      discountPrice: "120",
      unit: "ถุง",
      currency: "฿",
    },
  },
}

/**
 * ProductCard with a very long title to demonstrate text truncation.
 */
export const LongTitle: Story = {
  args: {
    title:
      "Ultra Premium Wireless Noise Cancelling Bluetooth Headphones with Advanced Active Noise Control Technology",
    caption: "Professional grade audio equipment",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Premium headphones product image",
    price: {
      price: "2,499",
      currency: "฿",
    },
    titleMaxLines: 2,
  },
}

/**
 * ProductCard that fills the available width (responsive behavior).
 */
export const FillWidth: Story = {
  args: {
    title: "Smart Watch Series 8",
    caption: "Latest generation with health monitoring",
    imageSrc:
      "https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=300&fit=crop",
    imageAlt: "Smart watch product image",
    price: {
      price: "12,900",
      currency: "฿",
    },
    fill: true,
  },
  render: (args) => (
    <div style={{ width: "400px", padding: "16px", background: "#f5f5f5" }}>
      <ProductCard {...args} />
    </div>
  ),
}

/**
 * ProductCard without an image to show text-only layout.
 */
export const NoImage: Story = {
  args: {
    title: "Digital Download Software",
    caption: "Instant download after purchase",
    price: {
      price: "599",
      currency: "฿",
    },
  },
}

/**
 * ProductCard with custom line limits for title and caption.
 */
export const CustomLineLimit: Story = {
  args: {
    title: "Professional DSLR Camera Kit with Multiple Lenses and Accessories",
    caption:
      "Complete photography bundle including camera body, three premium lenses, tripod, camera bag, and professional lighting equipment",
    imageSrc:
      "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=300&h=300&fit=crop",
    imageAlt: "DSLR camera kit product image",
    price: {
      price: "45,999",
      currency: "฿",
    },
    titleMaxLines: 1,
    captionMaxLines: 3,
  },
}

/**
 * Multiple ProductCards in a grid layout to show typical usage.
 */
export const ProductGrid: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(177px, 1fr))",
        gap: "16px",
        padding: "16px",
        background: "#f8f9fa",
      }}
    >
      <ProductCard
        fill
        title="Wireless Mouse"
        imageSrc="https://images.unsplash.com/photo-1527814050087-3793815479db?w=300&h=300&fit=crop"
        imageAlt="Wireless mouse"
        price={{ price: "890", currency: "฿" }}
      />
      <ProductCard
        fill
        title="Mechanical Keyboard"
        caption="RGB backlit"
        imageSrc="https://images.unsplash.com/photo-1587829741301-dc798b83add3?w=300&h=300&fit=crop"
        imageAlt="Mechanical keyboard"
        price={{ price: "2,490", currency: "฿" }}
      />
      <ProductCard
        fill
        title="4K Monitor"
        caption="27-inch display"
        imageSrc="https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=300&h=300&fit=crop"
        imageAlt="4K monitor"
        price={{ price: "8,990", discountPrice: "9,990", currency: "฿" }}
      />
      <ProductCard
        fill
        title="Ergonomic Chair"
        imageSrc="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop"
        imageAlt="Ergonomic office chair"
        price={{ price: "12,500", currency: "฿" }}
      />
    </div>
  ),
}

/**
 * Mobile responsive layout showing different card behaviors.
 */
export const OnMobile: Story = {
  args: {
    title: "Wireless Headphones",
    caption: "Premium sound quality",
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    imageAlt: "Wireless headphones",
    price: {
      price: "1,299",
      currency: "฿",
    },
  },
  render: (args) => (
    <div
      style={{
        width: "375px", // iPhone viewport width
        display: "flex",
        flexDirection: "column",
        background: "#f0f0f0",
        padding: "16px",
        gap: "16px",
      }}
    >
      <h3 style={{ margin: 0, fontSize: "16px", fontWeight: "600" }}>
        Mobile Layout Examples
      </h3>

      {/* Fixed width cards */}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "start",
          alignItems: "start",
          gap: "12px",
          overflowX: "auto",
          paddingBottom: "8px",
        }}
      >
        <ProductCard {...args} />
        <ProductCard
          {...args}
          title="Bluetooth Speaker"
          imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=300&h=300&fit=crop"
        />
        <ProductCard
          {...args}
          title="Smart Watch"
          imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=300&fit=crop"
        />
      </div>

      {/* Full width card */}
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px", color: "#666" }}>
          Full Width Card:
        </h4>
        <ProductCard {...args} fill />
      </div>
    </div>
  ),
}

// --- RENDER FUNCTION STORIES ---

/**
 * ProductCard with custom image rendering including badges and overlays.
 */
export const CustomImageRender: Story = {
  render: () => (
    <ProductCard
      title="Limited Edition Sneakers"
      caption="Only 100 pairs available"
      price={{
        price: "4,999",
        currency: "฿",
      }}
      renderImage={({ imageSrc, imageAlt }) => (
        <div style={{ position: "relative", width: "100%", height: "100%" }}>
          <img
            src="https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=300&fit=crop"
            alt="Limited edition sneakers"
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              borderRadius: "8px",
            }}
          />
          {/* Sale badge */}
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "#ff4444",
              color: "white",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "bold",
            }}
          >
            LIMITED
          </div>
          {/* Stock indicator */}
          <div
            style={{
              position: "absolute",
              bottom: "8px",
              right: "8px",
              background: "rgba(0,0,0,0.7)",
              color: "white",
              padding: "2px 6px",
              borderRadius: "12px",
              fontSize: "10px",
            }}
          >
            • 12 left
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom content rendering including star ratings and custom layouts.
 */
export const CustomContentRender: Story = {
  render: () => (
    <ProductCard
      title="Premium Coffee Maker"
      imageSrc="https://images.unsplash.com/photo-1517256064527-09c73fc73e38?w=300&h=300&fit=crop"
      imageAlt="Premium coffee maker"
      renderContent={({ title, price }) => (
        <div
          style={{
            padding: "12px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {/* Title */}
          <h3
            style={{
              margin: 0,
              fontSize: "14px",
              fontWeight: "600",
              lineHeight: "1.4",
            }}
          >
            {title}
          </h3>

          {/* Star rating */}
          <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <div style={{ display: "flex", gap: "1px" }}>
              {"★★★★☆".split("").map((star, i) => (
                <span
                  key={i}
                  style={{
                    color: i < 4 ? "#ffd700" : "#ddd",
                    fontSize: "12px",
                  }}
                >
                  {star}
                </span>
              ))}
            </div>
            <span style={{ fontSize: "11px", color: "#666" }}>
              (124 reviews)
            </span>
          </div>

          {/* Features list */}
          <ul
            style={{
              margin: 0,
              padding: 0,
              listStyle: "none",
              fontSize: "11px",
              color: "#666",
            }}
          >
            <li>• Programmable brewing</li>
            <li>• Built-in grinder</li>
            <li>• 12-cup capacity</li>
          </ul>

          {/* Price with custom styling */}
          <div
            style={{
              display: "flex",
              alignItems: "baseline",
              gap: "8px",
              marginTop: "4px",
            }}
          >
            <span
              style={{
                fontSize: "16px",
                fontWeight: "bold",
                color: "#2d5a27",
              }}
            >
              ฿8,999
            </span>
            <span
              style={{
                fontSize: "12px",
                color: "#999",
                textDecoration: "line-through",
              }}
            >
              ฿12,999
            </span>
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom footer rendering including multiple action buttons.
 */
export const CustomFooterRender: Story = {
  render: () => (
    <ProductCard
      title="Gaming Laptop"
      caption="High-performance gaming machine"
      imageSrc="https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=300&h=300&fit=crop"
      imageAlt="Gaming laptop"
      price={{
        price: "45,999",
        currency: "฿",
      }}
      renderFooter={() => (
        <div
          style={{
            padding: "12px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {/* Primary action */}
          <button
            style={{
              width: "100%",
              padding: "8px 16px",
              background: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "6px",
              fontSize: "14px",
              fontWeight: "500",
              cursor: "pointer",
            }}
          >
            🛒 Add to Cart
          </button>

          {/* Secondary actions */}
          <div style={{ display: "flex", gap: "8px" }}>
            <button
              style={{
                flex: 1,
                padding: "6px 12px",
                background: "transparent",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              ❤️ Wishlist
            </button>
            <button
              style={{
                flex: 1,
                padding: "6px 12px",
                background: "transparent",
                color: "#666",
                border: "1px solid #ddd",
                borderRadius: "6px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              📋 Compare
            </button>
          </div>
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with custom image overlay for badges, labels, and promotional content.
 */
export const CustomImageOverlay: Story = {
  render: () => (
    <ProductCard
      title="Wireless Gaming Controller"
      caption="Premium wireless gaming experience"
      imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
      imageAlt="Wireless gaming controller"
      price={{
        price: "2,299",
        discountPrice: "2,899",
        currency: "฿",
      }}
      renderImageOverlay={() => (
        <div
          style={{
            position: "absolute",
            top: "8px",
            left: "8px",
            borderRadius: "16px",
            fontSize: "11px",
            fontWeight: "bold",
            display: "flex",
            alignItems: "center",
            gap: "4px",
          }}
        >
          <ProductBadge variant="discount" label="20%" />
        </div>
      )}
    />
  ),
}

/**
 * ProductCard with multiple overlay variants showing different badge styles.
 */
export const OverlayVariants: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
        gap: "16px",
        padding: "16px",
        background: "#f8f9fa",
      }}
    >
      {/* Hot Deal Badge */}
      <ProductCard
        title="Smart TV 55'"
        imageSrc="https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=300&h=300&fit=crop"
        imageAlt="Smart TV"
        price={{ price: "15,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "#ff4757",
              color: "white",
              padding: "4px 12px",
              borderRadius: "20px",
              fontSize: "11px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            🔥 Hot Deal
          </div>
        )}
      />

      {/* New Arrival Badge */}
      <ProductCard
        title="Smartphone Pro"
        imageSrc="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop"
        imageAlt="Smartphone"
        price={{ price: "25,900", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              right: "8px",
              background: "#3742fa",
              color: "white",
              padding: "4px 10px",
              borderRadius: "4px",
              fontSize: "10px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            ✨ New
          </div>
        )}
      />

      {/* Best Seller Badge */}
      <ProductCard
        title="Laptop Gaming"
        imageSrc="https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?w=300&h=300&fit=crop"
        imageAlt="Gaming laptop"
        price={{ price: "45,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "50%",
              transform: "translateX(-50%)",
              background: "#ffa502",
              color: "white",
              padding: "4px 12px",
              borderRadius: "0 0 12px 12px",
              fontSize: "10px",
              fontWeight: "bold",
              textTransform: "uppercase",
            }}
          >
            👑 Best Seller
          </div>
        )}
      />

      {/* Limited Edition Badge */}
      <ProductCard
        title="Headphones Pro"
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop"
        imageAlt="Pro headphones"
        price={{ price: "8,999", currency: "฿" }}
        renderImageOverlay={() => (
          <div
            style={{
              position: "absolute",
              top: "8px",
              left: "8px",
              background: "linear-gradient(45deg, #667eea, #764ba2)",
              color: "white",
              padding: "4px 8px",
              borderRadius: "8px",
              fontSize: "9px",
              fontWeight: "bold",
              textTransform: "uppercase",
              border: "1px solid rgba(255,255,255,0.3)",
            }}
          >
            💎 Limited
          </div>
        )}
      />
    </div>
  ),
}
