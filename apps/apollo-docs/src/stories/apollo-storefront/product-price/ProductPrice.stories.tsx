import React from "react"
import { ProductPrice } from "@apollo/storefront"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"


/**
 * ProductPrice displays pricing information for products, including support for
 * units, discount pricing, and different currencies. It handles the visual
 * representation of both regular and discounted prices.
 */
const meta: Meta<typeof ProductPrice> = {
  title: "@apollo∕storefront/Components/ProductPrice",
  component: ProductPrice,
  parameters: {
    layout: "centered",
  },
  globals: {
    brand: "storefront",
  },
  tags: ["autodocs"],
  argTypes: {
    price: {
      control: "text",
      description: "The main price value",
    },
    unit: {
      control: "text",
      description: "The unit of measurement (e.g., 'kg', 'pack', 'piece')",
    },
    discountPrice: {
      control: "text",
      description: "The original price before discount (crossed out)",
    },
    currency: {
      control: "text",
      description: "The currency symbol",
    },
  },
} satisfies Meta<typeof ProductPrice>

export default meta
type Story = StoryObj<typeof ProductPrice>

export const Default: Story = {
  args: {
    price: "299",
    currency: "฿",
  },
}

/**
 * ProductPrice with a unit of measurement displayed.
 */
export const WithUnit: Story = {
  args: {
    price: "85",
    unit: "kg",
    currency: "฿",
  },
}

/**
 * ProductPrice showing a discounted price with the original price crossed out.
 */
export const WithDiscount: Story = {
  args: {
    price: "199",
    discountPrice: "249",
    currency: "฿",
  },
}

/**
 * ProductPrice with both unit and discount pricing.
 */
export const WithUnitAndDiscount: Story = {
  args: {
    price: "159",
    unit: "pack",
    discountPrice: "199",
    currency: "฿",
  },
}

/**
 * ProductPrice using different currency symbols.
 */
export const DifferentCurrencies: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "center",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Thai Baht (฿)</h4>
        <ProductPrice price="299" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>US Dollar ($)</h4>
        <ProductPrice price="8.50" currency="$" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Euro (€)</h4>
        <ProductPrice price="7.90" currency="€" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          British Pound (£)
        </h4>
        <ProductPrice price="6.75" currency="£" />
      </div>
    </div>
  ),
}

/**
 * Various pricing scenarios commonly used in e-commerce.
 */
export const PricingScenarios: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Simple Price</h4>
        <ProductPrice price="50" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Price per Unit
        </h4>
        <ProductPrice price="25" unit="piece" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Bulk Pricing</h4>
        <ProductPrice price="450" unit="dozen" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Weight-based</h4>
        <ProductPrice price="120" unit="kg" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Volume-based</h4>
        <ProductPrice price="35" unit="liter" currency="฿" />
      </div>
    </div>
  ),
}

/**
 * Discount pricing examples showing different discount amounts.
 */
export const DiscountExamples: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Small Discount
        </h4>
        <ProductPrice price="95" discountPrice="99" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Medium Discount
        </h4>
        <ProductPrice price="199" discountPrice="249" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Large Discount
        </h4>
        <ProductPrice price="299" discountPrice="499" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>With Unit</h4>
        <ProductPrice price="89" unit="pack" discountPrice="119" currency="฿" />
      </div>
    </div>
  ),
}

/**
 * High-value product pricing to test layout with larger numbers.
 */
export const HighValuePricing: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "24px",
        flexWrap: "wrap",
        alignItems: "flex-start",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Thousands</h4>
        <ProductPrice price="2,499" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>With Discount</h4>
        <ProductPrice price="8,999" discountPrice="12,999" currency="฿" />
      </div>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Per Unit</h4>
        <ProductPrice price="1,250" unit="set" currency="฿" />
      </div>
    </div>
  ),
}
