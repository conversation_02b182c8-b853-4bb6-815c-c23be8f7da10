import React from "react"
import { TabWithImage } from "@apollo/storefront"
import type { <PERSON>a, StoryObj } from "@storybook/react"


/**
 * TabWithImage provides a tabbed interface that supports images within tabs.
 * It's built on top of the base Tabs component from @apollo/ui and adds
 * image support for enhanced visual navigation.
 *
 * ## Subcomponents
 *
 * ### TabWithImage.Root
 * The root container for the tab component.
 * - `defaultValue?: string` - The default active tab value
 * - `value?: string` - The controlled active tab value
 * - `onValueChange?: (value: string) => void` - Callback when tab changes
 * - `fullWidth?: boolean` - Whether tabs should take full width
 *
 * ### TabWithImage.List
 * Container for the tab buttons with horizontal scrolling support.
 * - `className?: string` - Additional CSS classes
 * - Automatically handles overflow with smooth scrolling
 *
 * ### TabWithImage.Tab
 * Individual tab button that can contain images and text.
 * - `value: string` - Unique identifier for the tab
 * - `disabled?: boolean` - Whether the tab is disabled
 * - `className?: string` - Additional CSS classes
 *
 * ### TabWithImage.Image
 * Image container within a tab, supports both images and icons.
 * - `src?: string` - Image source URL
 * - `alt?: string` - Alternative text for images
 * - `children?: ReactNode` - For SVG icons or other content
 *
 * ### TabWithImage.Text
 * Text content with automatic line-clamp functionality.
 * - `children: ReactNode` - Text content
 * - `className?: string` - Additional CSS classes
 * - Automatically truncates to 2 lines with ellipsis
 *
 * ### TabWithImage.Indicator
 * Visual indicator showing the active tab (automatically positioned).
 * - Automatically tracks active tab position
 * - Handles scrollable containers correctly
 *
 * ### TabWithImage.Panel
 * Content panel associated with each tab.
 * - `value: string` - Must match the corresponding tab value
 * - `children: ReactNode` - Panel content
 * - `className?: string` - Additional CSS classes
 */
const meta: Meta<typeof TabWithImage.Root> = {
  title: "@apollo∕storefront/Components/TabWithImage",
  component: TabWithImage.Root,
  subcomponents: {
    "TabWithImage.Root": TabWithImage.Root,
    "TabWithImage.List": TabWithImage.List,
    "TabWithImage.Tab": TabWithImage.Tab,
    "TabWithImage.Image": TabWithImage.Image,
    "TabWithImage.Text": TabWithImage.Text,
    "TabWithImage.Indicator": TabWithImage.Indicator,
    "TabWithImage.Panel": TabWithImage.Panel,
  },
  parameters: {
    layout: "centered",
  },
  globals: {
    brand: "storefront",
  },
  decorators: [
    (Story) => (
        <div style={{ maxWidth: "100vw" }}>
          <Story />
        </div>
    ),
  ],
  tags: ["autodocs"],
  argTypes: {
    // TabWithImage.Root props only
    defaultValue: {
      description: "The default active tab value",
      control: "text",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "undefined" },
      },
    },
    value: {
      description: "The controlled active tab value",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    onValueChange: {
      description: "Callback fired when the active tab changes",
      control: false,
      table: {
        type: { summary: "(value: string) => void" },
      },
    },
    fullWidth: {
      description: "Whether the tabs should take full width",
      control: "boolean",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof TabWithImage.Root>

/**
 * Comprehensive example showing all subcomponent props and usage patterns.
 * This story demonstrates the complete API surface of TabWithImage.
 */
export const ComponentAPI: Story = {
  render: () => (
    <div style={{ maxWidth: "600px" }}>
      <h3 style={{ marginBottom: "16px" }}>
        TabWithImage Component API
      </h3>

      {/* Example with all subcomponent features */}
      <TabWithImage.Root defaultValue="api-demo" fullWidth>
        <TabWithImage.List className="custom-tab-list">
          {/* Tab with image */}
          <TabWithImage.Tab value="api-demo">
            <TabWithImage.Image
              src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
              alt="API Demo"
            />
            <TabWithImage.Text>API Demo</TabWithImage.Text>
          </TabWithImage.Tab>

          {/* Tab with icon */}
          <TabWithImage.Tab value="icon-demo">
            <TabWithImage.Image>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </TabWithImage.Image>
            <TabWithImage.Text>Icon Demo</TabWithImage.Text>
          </TabWithImage.Tab>

          {/* Disabled tab */}
          <TabWithImage.Tab value="disabled-demo" disabled>
            <TabWithImage.Image
              src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
              alt="Disabled"
            />
            <TabWithImage.Text>Disabled</TabWithImage.Text>
          </TabWithImage.Tab>


          {/* Long text with truncation */}
          <TabWithImage.Tab value="long-text">
            <TabWithImage.Image
              src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
              alt="Long text"
            />
            <TabWithImage.Text>Very Long Text That Will Be Truncated</TabWithImage.Text>
          </TabWithImage.Tab>

          {/* Indicator automatically positions itself */}
          <TabWithImage.Indicator />
        </TabWithImage.List>

        {/* Panels for each tab */}
        <TabWithImage.Panel value="api-demo">
          <div style={{ padding: "20px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
            <h4 >API Demo Panel</h4>
            <p>
              This panel demonstrates the TabWithImage.Panel component with custom styling.
            </p>
            <ul style={{ marginTop: "12px" }}>
              <li><strong>value="api-demo"</strong> - Must match the tab value</li>
              <li><strong>children</strong> - Any React content</li>
              <li><strong>className</strong> - Optional custom CSS classes</li>
            </ul>
          </div>
        </TabWithImage.Panel>

        <TabWithImage.Panel value="icon-demo">
          <div style={{ padding: "20px", backgroundColor: "#e3f2fd", borderRadius: "8px" }}>
            <h4 >Icon Demo Panel</h4>
            <p>
              This tab uses an SVG icon instead of an image. The TabWithImage.Image component
              can contain either an img element (with src/alt props) or any React children like SVG icons.
            </p>
          </div>
        </TabWithImage.Panel>

        <TabWithImage.Panel value="disabled-demo">
          <div style={{ padding: "20px", backgroundColor: "#ffebee", borderRadius: "8px" }}>
            <h4 >Disabled Tab Panel</h4>
            <p>
              This panel won't be accessible because the tab is disabled.
            </p>
          </div>
        </TabWithImage.Panel>

        <TabWithImage.Panel value="long-text">
          <div style={{ padding: "20px", backgroundColor: "#e8f5e8", borderRadius: "8px" }}>
            <h4 >Long Text Panel</h4>
            <p>
              The tab text is automatically truncated to 2 lines with ellipsis using CSS line-clamp.
              This ensures consistent layout across different text lengths.
            </p>
          </div>
        </TabWithImage.Panel>
      </TabWithImage.Root>

      {/* Props documentation */}
      <div style={{ marginTop: "32px", padding: "16px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
        <h4 >Key Features:</h4>
        <ul style={{ marginTop: "8px" }}>
          <li><strong>Scrollable:</strong> Automatically handles overflow with smooth horizontal scrolling</li>
          <li><strong>Responsive:</strong> Optimized for mobile with proper touch targets</li>
          <li><strong>Accessible:</strong> Full keyboard navigation and screen reader support</li>
          <li><strong>Flexible:</strong> Supports images, icons, text-only, and mixed content</li>
          <li><strong>Line-clamp:</strong> Automatic text truncation for consistent layout</li>
          <li><strong>Indicator:</strong> Smart positioning that works with scrollable containers</li>
        </ul>
      </div>
    </div>
  ),
}


/**
 * Basic example showing the recommended usage patterns.
 * Demonstrates both image and icon usage with the new TabWithImage.Text component.
 */
export const Basic: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="home">
      <TabWithImage.List>
        <TabWithImage.Tab value="home">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Home"
          />
          <TabWithImage.Text>Home</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="favorites">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Favorites</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="profile">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Profile</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="home">
        <div style={{ padding: "20px" }}>
          <h3>🏠 Home</h3>
          <p>Welcome to our homepage</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="favorites">
        <div style={{ padding: "20px" }}>
          <h3>❤️ Favorites</h3>
          <p>Your favorite items and saved products</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="profile">
        <div style={{ padding: "20px" }}>
          <h3>👤 Profile</h3>
          <p>Personal information and account settings</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * TabWithImage featuring tabs with images for visual navigation.
 * Images are displayed above text, with text truncated to 2 lines maximum using TabWithImage.Text.
 */
export const WithImages: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="product1">
      <TabWithImage.List>
        <TabWithImage.Tab value="product1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Headphones"
          />
          <TabWithImage.Text>Promotions</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="product2">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            alt="Smart Watch"
          />
          <TabWithImage.Text>Beverages</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="product3">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
            alt="Speaker"
          />
          <TabWithImage.Text>Snacks & Cakes</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="product4">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
            alt="Home"
          />
          <TabWithImage.Text>Health Care...</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="product5">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
            alt="Sports"
          />
          <TabWithImage.Text>Home Goods</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="product6">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=80&h=80&fit=crop"
            alt="Gallery"
          />
          <TabWithImage.Text>Pet Supplies</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="product1">
        <div style={{ padding: "20px" }}>
          <h3>Promotions</h3>
          <p>Special discounts up to 50% off on quality products and featured items</p>
          <img
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=300&h=200&fit=crop"
            alt="Promotion products"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="product2">
        <div style={{ padding: "20px" }}>
          <h3>Beverages</h3>
          <p>Fresh drinks and 100% natural fruit juices with no preservatives</p>
          <img
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=300&h=200&fit=crop"
            alt="Beverages"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="product3">
        <div style={{ padding: "20px" }}>
          <h3>Snacks & Cakes</h3>
          <p>Delicious sweets and fresh cakes made daily with high-quality ingredients</p>
          <img
            src="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=300&h=200&fit=crop"
            alt="Desserts and cakes"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="product4">
        <div style={{ padding: "20px" }}>
          <h3>Health Care</h3>
          <p>Dietary supplements, vitamins, and health care equipment</p>
          <img
            src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=300&h=200&fit=crop"
            alt="Health products"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="product5">
        <div style={{ padding: "20px" }}>
          <h3>Home Goods</h3>
          <p>Home appliances and household items with good quality at affordable prices</p>
          <img
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop"
            alt="Home appliances"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="product6">
        <div style={{ padding: "20px" }}>
          <h3>Pet Supplies</h3>
          <p>Pet food, toys, and complete pet care equipment</p>
          <img
            src="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=300&h=200&fit=crop"
            alt="Pet products"
            style={{ width: "100%", maxWidth: "300px", borderRadius: "8px", marginTop: "16px" }}
          />
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * TabWithImage with image-only tabs (no text labels).
 * Demonstrates clean icon-based navigation.
 */
export const ImageOnly: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="category1">
      <TabWithImage.List>
        <TabWithImage.Tab value="category1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=80&h=80&fit=crop"
            alt="Electronics category"
          />
        </TabWithImage.Tab>
        <TabWithImage.Tab value="category2">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
            alt="Fashion category"
          />
        </TabWithImage.Tab>
        <TabWithImage.Tab value="category3">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
            alt="Home & Garden category"
          />
        </TabWithImage.Tab>
        <TabWithImage.Tab value="category4">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
            alt="Sports category"
          />
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="category1">
        <div style={{ padding: "20px" }}>
          <h3>Electronics</h3>
          <p>Discover the latest in technology and electronics.</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="category2">
        <div style={{ padding: "20px" }}>
          <h3>Fashion</h3>
          <p>Explore trendy clothing and accessories.</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="category3">
        <div style={{ padding: "20px" }}>
          <h3>Home & Garden</h3>
          <p>Everything you need for your home and garden.</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="category4">
        <div style={{ padding: "20px" }}>
          <h3>Sports</h3>
          <p>Gear up for your favorite sports and activities.</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * TabWithImage demonstrating text truncation when text exceeds 2 lines.
 * Long text will be automatically truncated with ellipsis using TabWithImage.Text.
 */
export const TextTruncation: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="long1">
      <TabWithImage.List>
        <TabWithImage.Tab value="long1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Electronics"
          />
          <TabWithImage.Text>Electronics and Modern Technology Products</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="long2">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            alt="Fashion"
          />
          <TabWithImage.Text>Fashion Clothing for All Ages at Reasonable Prices</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="long3">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
            alt="Home"
          />
          <TabWithImage.Text>High Quality Home Goods and Appliances</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="short">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
            alt="Sports"
          />
          <TabWithImage.Text>Sports</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="long1">
        <div style={{ padding: "20px" }}>
          <h3>Electronics and Technology</h3>
          <p>Latest electronics and modern technology products with good prices and warranty</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="long2">
        <div style={{ padding: "20px" }}>
          <h3>Fashion Clothing</h3>
          <p>Fashion clothing for all ages with modern designs, good quality at reasonable prices</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="long3">
        <div style={{ padding: "20px" }}>
          <h3>Home Goods</h3>
          <p>High-quality home appliances and household items that are energy-efficient and easy to use</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="short">
        <div style={{ padding: "20px" }}>
          <h3>Sports</h3>
          <p>Sports equipment and fitness gear for all types of sports</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * TabWithImage demonstrating scrollable behavior with many tabs.
 * Perfect for mobile screens where horizontal scrolling is needed.
 */
export const ScrollableMobile: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="cat1">
      <TabWithImage.List>
        <TabWithImage.Tab value="cat1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Electronics"
          />
          <TabWithImage.Text>Promotions</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat2">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            alt="Beverages"
          />
          <TabWithImage.Text>Beverages</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat3">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
            alt="Snacks"
          />
          <TabWithImage.Text>Snacks</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat4">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
            alt="Health"
          />
          <TabWithImage.Text>Health Care</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat5">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
            alt="Home"
          />
          <TabWithImage.Text>Home Goods</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat6">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=80&h=80&fit=crop"
            alt="Pets"
          />
          <TabWithImage.Text>Pet Supplies</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat7">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=80&h=80&fit=crop"
            alt="Electronics"
          />
          <TabWithImage.Text>Electronics</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat8">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
            alt="Fashion"
          />
          <TabWithImage.Text>Fashion</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat9">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=80&h=80&fit=crop"
            alt="Books"
          />
          <TabWithImage.Text>Books</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="cat10">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?w=80&h=80&fit=crop"
            alt="Sports"
          />
          <TabWithImage.Text>Sports & Fitness</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="cat1">
        <div style={{ padding: "20px" }}>
          <h3>Promotions</h3>
          <p>Special discounts up to 50% off</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat2">
        <div style={{ padding: "20px" }}>
          <h3>Beverages</h3>
          <p>Fresh drinks and natural fruit juices</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat3">
        <div style={{ padding: "20px" }}>
          <h3>Snacks</h3>
          <p>Sweet and savory snacks in various flavors</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat4">
        <div style={{ padding: "20px" }}>
          <h3>Health Care</h3>
          <p>Dietary supplements and health equipment</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat5">
        <div style={{ padding: "20px" }}>
          <h3>Home Goods</h3>
          <p>Home appliances and household items</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat6">
        <div style={{ padding: "20px" }}>
          <h3>Pet Supplies</h3>
          <p>Food and equipment for pets</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat7">
        <div style={{ padding: "20px" }}>
          <h3>Electronics</h3>
          <p>Technology and electronic devices</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat8">
        <div style={{ padding: "20px" }}>
          <h3>Fashion</h3>
          <p>Clothing and fashion accessories</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat9">
        <div style={{ padding: "20px" }}>
          <h3>Books</h3>
          <p>Books and learning materials</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat10">
        <div style={{ padding: "20px" }}>
          <h3>Sports & Fitness</h3>
          <p>Sports and fitness equipment</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'iphone5',
    },
  },
}

/**
 * TabWithImage in a mobile container to demonstrate responsive behavior.
 * Shows how the component adapts to smaller screen sizes.
 */
export const MobileContainer: Story = {
  render: () => (
    <div style={{
      width: '375px',
      height: '600px',
      border: '1px solid #ccc',
      borderRadius: '12px',
      overflow: 'hidden',
      backgroundColor: '#f8f9fa'
    }}>
      <div style={{ padding: '16px', backgroundColor: 'white', borderBottom: '1px solid #eee' }}>
        <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>Product Categories</h3>
      </div>
      <div style={{ padding: '16px' }}>
        <TabWithImage.Root defaultValue="mobile1">
          <TabWithImage.List>
            <TabWithImage.Tab value="mobile1">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
                alt="Promotion"
              />
              <TabWithImage.Text>Promotions</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Tab value="mobile2">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
                alt="Beverages"
              />
              <TabWithImage.Text>Beverages</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Tab value="mobile3">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
                alt="Snacks"
              />
              <TabWithImage.Text>Snacks</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Tab value="mobile4">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
                alt="Health"
              />
              <TabWithImage.Text>Health Care</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Tab value="mobile5">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
                alt="Home"
              />
              <TabWithImage.Text>Home Goods</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Tab value="mobile6">
              <TabWithImage.Image
                src="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=80&h=80&fit=crop"
                alt="Pets"
              />
              <TabWithImage.Text>Pet Supplies</TabWithImage.Text>
            </TabWithImage.Tab>
            <TabWithImage.Indicator />
          </TabWithImage.List>
          <TabWithImage.Panel value="mobile1">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#e74c3c' }}>🔥 Special Promotions</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Up to 50% off on quality products</p>
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="mobile2">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#3498db' }}>💧 Beverages</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Fresh drinks and 100% natural fruit juices</p>
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="mobile3">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#f39c12' }}>🍿 Snacks</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Sweet and savory snacks in various flavors</p>
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="mobile4">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#27ae60' }}>💊 Health Care</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Dietary supplements and health equipment</p>
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="mobile5">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#9b59b6' }}>🏠 Home Goods</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Home appliances and household items</p>
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="mobile6">
            <div style={{ padding: "16px", backgroundColor: 'white', borderRadius: '8px', marginTop: '16px' }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#e67e22' }}>🐕 Pet Supplies</h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>Food and equipment for pets</p>
            </div>
          </TabWithImage.Panel>
        </TabWithImage.Root>
      </div>
    </div>
  ),
  parameters: {
    layout: 'centered',
  },
}

/**
 * TabWithImage using icons instead of images.
 * Demonstrates how to use SVG icons with TabWithImage.Text for proper text truncation.
 */
export const WithIcons: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="icon1">
      <TabWithImage.List>
        <TabWithImage.Tab value="icon1">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Promotions</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="icon2">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Shopping</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="icon3">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Reviews</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="icon4">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Contact</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="icon5">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Success</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="icon1">
        <div style={{ padding: "20px" }}>
          <h3>❤️ Promotions</h3>
          <p>Special discounts and amazing promotional deals</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="icon2">
        <div style={{ padding: "20px" }}>
          <h3>🛒 Shopping</h3>
          <p>Various products at great prices</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="icon3">
        <div style={{ padding: "20px" }}>
          <h3>⭐ Reviews</h3>
          <p>Reviews and ratings from real customers</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="icon4">
        <div style={{ padding: "20px" }}>
          <h3>📧 Contact</h3>
          <p>Contact us for inquiries and customer support</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="icon5">
        <div style={{ padding: "20px" }}>
          <h3>✅ Success</h3>
          <p>Successful orders and completed transactions</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * Demonstrates the new TabWithImage.Text component for proper text handling.
 * Shows different text lengths and how they are handled with line-clamp.
 */
export const WithTextComponent: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="text1">
      <TabWithImage.List>
        <TabWithImage.Tab value="text1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Short text"
          />
          <TabWithImage.Text>Short</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="text2">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            alt="Medium text"
          />
          <TabWithImage.Text>Medium Text</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="text3">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
            alt="Long text"
          />
          <TabWithImage.Text>Very Long Text That Will Be Truncated to Two Lines</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="text4">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Icon with Long Text That Will Be Truncated</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="text5">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>⭐</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="text1">
        <div style={{ padding: "20px" }}>
          <h3>Short Text</h3>
          <p>Example of short text that is not truncated</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="text2">
        <div style={{ padding: "20px" }}>
          <h3>Medium Text</h3>
          <p>Example of medium-length text that may fit or slightly exceed one line</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="text3">
        <div style={{ padding: "20px" }}>
          <h3>Long Text</h3>
          <p>Example of long text that will be truncated to two lines with line-clamp and show ellipsis (...) at the end</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="text4">
        <div style={{ padding: "20px" }}>
          <h3>Icon with Text</h3>
          <p>Example of using an icon with long text that will be truncated</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="text5">
        <div style={{ padding: "20px" }}>
          <h3>Icon with Emoji</h3>
          <p>Example of using an icon with emoji or short symbols</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/**
 * TabWithImage demonstrating disabled state.
 * Shows how tabs appear and behave when disabled.
 */
export const WithDisabled: Story = {
  render: () => (
    <TabWithImage.Root defaultValue="enabled1">
      <TabWithImage.List>
        <TabWithImage.Tab value="enabled1">
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-*************-5e560c06d30e?w=80&h=80&fit=crop"
            alt="Available"
          />
          <TabWithImage.Text>Available</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="disabled1" disabled>
          <TabWithImage.Image
            src="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            alt="Disabled"
          />
          <TabWithImage.Text>Disabled</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="enabled2">
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Favorites</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Tab value="disabled2" disabled>
          <TabWithImage.Image>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </TabWithImage.Image>
          <TabWithImage.Text>Unavailable</TabWithImage.Text>
        </TabWithImage.Tab>
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="enabled1">
        <div style={{ padding: "20px" }}>
          <h3>✅ Available</h3>
          <p>This tab can be clicked and used normally</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="disabled1">
        <div style={{ padding: "20px" }}>
          <h3>❌ Disabled</h3>
          <p>This tab is disabled and cannot be clicked</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="enabled2">
        <div style={{ padding: "20px" }}>
          <h3>❤️ Favorites</h3>
          <p>Tab with icon that can be used</p>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="disabled2">
        <div style={{ padding: "20px" }}>
          <h3>🚫 Unavailable</h3>
          <p>Tab with icon but disabled</p>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}