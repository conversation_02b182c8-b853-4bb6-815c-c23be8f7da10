import React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>utt<PERSON>, Typo<PERSON> } from "@apollo/ui"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

/**
 * A breadcrumb is a list of links that helps visualize a page's location
 * within a site's hierarchical structure. It allows navigation up to any of the ancestors.
 */
const meta: Meta<typeof Breadcrumbs> = {
  title: "@apollo∕ui/Components/Navigation/Breadcrumbs",
  component: Breadcrumbs,
  tags: ["autodocs"],
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
  },
  argTypes: {
    children: {
      control: false,
      description:
        "The content of the component, typically links and typography.",
    },
    separator: {
      control: "text",
      description:
        "Custom separator node. For example, a string or a component.",
    },
    maxItems: {
      control: "number",
      description:
        "Specifies the maximum number of breadcrumbs to display before collapsing.",
    },
    itemsBeforeCollapse: {
      control: "number",
      description:
        "If maxItems is exceeded, the number of items to show before the ellipsis.",
    },
    itemsAfterCollapse: {
      control: "number",
      description:
        "If maxItems is exceeded, the number of items to show after the ellipsis.",
    },
  },
} satisfies Meta<typeof Breadcrumbs>

export default meta
type Story = StoryObj<typeof Breadcrumbs>

// --- STORIES ---

/**
 * This is the default breadcrumbs component. The last item is typically the current page
 * and is represented by a `Typography` component rather than a link.
 */
export const Default: Story = {
  args: {
    "aria-label": "breadcrumb",
    children: [
      <a key="1" href="/">
        Apollo
      </a>,
      <a key="2" href="/material-ui/getting-started/installation/">
        Core
      </a>,
      <Typography key="3">Breadcrumbs</Typography>,
    ],
  },
}

/**
 * When the number of items exceeds the `maxItems` prop, the breadcrumbs will collapse,
 * showing an ellipsis. You can control how many items are visible before and after the
 * ellipsis using `itemsBeforeCollapse` and `itemsAfterCollapse`.
 */
export const Ellipsis: Story = {
  name: "Collapsed with Ellipsis",
  args: {
    maxItems: 3,
    itemsBeforeCollapse: 1,
    itemsAfterCollapse: 1,
    "aria-label": "breadcrumb with ellipsis",
    children: [
      <a key="1" href="/">
        Home
      </a>,
      <a key="2" href="/level-1">
        Products
      </a>,
      <a key="3" href="/level-2">
        Electronics
      </a>,
      <a key="4" href="/level-3">
        Mobile Phones
      </a>,
      <Typography key="5">Accessories</Typography>,
    ],
  },
}

/**
 * An example of a breadcrumb with multiple "active" items, represented by non-interactive
 * `Typography` elements. This is an uncommon use case but is supported.
 */
export const MultipleActive: Story = {
  args: {
    "aria-label": "breadcrumb with multiple active items",
    children: [
      <a key="1" href="/">
        Apollo
      </a>,
      <Typography key="2" color="primary">
        Core
      </Typography>,
      <Typography key="3">Breadcrumbs</Typography>,
    ],
  },
}

/**
 * You can customize the separator between items by passing a string or a React node
 * to the `separator` prop.
 */
export const CustomSeparator: Story = {
  args: {
    separator: "—",
    "aria-label": "breadcrumb with custom separator",
    children: [
      <a key="1" href="/">
        Apollo
      </a>,
      <a key="2" href="/material-ui/getting-started/installation/">
        Core
      </a>,
      <Typography key="3">Breadcrumbs</Typography>,
    ],
  },
}

/**
 * This is the disabled item in breadcrumbs component.
 */
export const Disabled: Story = {
  args: {
    "aria-label": "breadcrumb",
    children: [
      <a key="1" href="/" aria-disabled>
        Apollo
      </a>,
      <a key="2" href="/material-ui/getting-started/installation/">
        Core
      </a>,
      <IconButton key="3" disabled>o_o</IconButton>,
      <Typography key="4" aria-disabled>
        Breadcrumbs
      </Typography>,
      <Typography key="5">Breadcrumbs</Typography>,
    ],
  },
}
