import React, { use<PERSON><PERSON>back, useState } from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { FloatButton, Typography } from "@apollo/ui"
import {
  Download,
  Edit,
  Heart,
  Info,
  Plus,
  Search,
  Smile,
  Star,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * FloatButton component
 *
 * The FloatButton component is a control button designed to float on the screen. 
 * It features an icon and label that can be expanded/collapsed, commonly used for 
 * primary actions or quick access features.
 *
 * Notes:
 * - Default iconSide is "start";
 * - Default isExpanded is false;
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/FloatButton",
  component: FloatButton,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2564-28711&m=dev",
    },
    docs: {
      description: {
        component:
          "The FloatButton component renders a floating action button with Apollo design system styling. It supports expansion/collapse states, multiple variants, sizes, colors, and can be rendered as a link when href is provided.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { FloatButton } from "@apollo/ui"`} language="tsx" />
          <h2 id="floatbutton-props">Props</h2>
          <ArgTypes />
          <h2 id="floatbutton-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use FloatButton for primary actions that should be easily accessible from anywhere on the page",
              "Place FloatButton in a consistent position (typically bottom-right corner)",
              "Use clear, recognizable icons that represent the action",
              "Keep labels concise and action-oriented",
              "Use the expanded state to provide context when the icon alone might be unclear",
              "Consider using the collapsed state to save screen space while maintaining accessibility",
            ]}
          />
          <h2 id="floatbutton-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive labels that clearly indicate the action
                that will be performed when the button is activated.
              </>,
              <>
                Use the <code>disabled</code> prop to disable buttons that are
                not currently actionable, ensuring they are not focusable.
              </>,
              <>
                Ensure the icon provides sufficient visual context for the action,
                or use the expanded state to show the label.
              </>,
              <>
                For link FloatButtons using <code>href</code>, ensure the link 
                destination is clear from the button label or provide additional context.
              </>
            ]}
          />
          <h2 id="floatbutton-examples">Examples</h2>
          <Stories title="" />
          <h2 id="floatbutton-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <FloatButton icon={<Plus />} label="Add Item" isExpanded />
                      <FloatButton icon={<Edit />} label="Edit" />
                    </div>
                  ),
                  description: "Use clear, action-oriented labels and recognizable icons",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
                      <FloatButton icon={<Plus />} label="Click Here" isExpanded />
                      <FloatButton icon={<Star />} label="Button" />
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't describe the specific action",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ position: "relative", width: 200, height: 120, border: "1px dashed #ccc", borderRadius: 8 }}>
                      <FloatButton 
                        icon={<Heart />} 
                        label="Favorite" 
                        style={{ position: "absolute", bottom: 16, right: 16 }}
                      />
                    </div>
                  ),
                  description: "Position FloatButton consistently (typically bottom-right)",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                      <FloatButton icon={<Plus />} label="Add" />
                      <FloatButton icon={<Edit />} label="Edit" />
                      <FloatButton icon={<Heart />} label="Like" />
                      <FloatButton icon={<Star />} label="Star" />
                    </div>
                  ),
                  description:
                    "Avoid using multiple FloatButtons that compete for attention",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    icon: {
      control: false,
      description: "The icon to be displayed inside the button.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    label: {
      control: { type: "text" },
      description: "The label to be displayed inside the button.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    isExpanded: {
      control: { type: "boolean" },
      description: "Whether the button is expanded to show the label. Default is false.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    iconSide: {
      control: { type: "radio" },
      options: ["start", "end"],
      description: "The side of the icon relative to the label. Default is 'start'.",
      table: {
        type: { summary: '"start" | "end"' },
        defaultValue: { summary: "start" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the button is disabled.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    href: {
      control: { type: "text" },
      description: "If provided, the button will be rendered as a link.",
      table: {
        type: { summary: "string" },
      },
    },
    onClick: {
      control: false,
      description: "Callback fired when the button is clicked.",
      table: {
        type: {
          summary: "(event: React.MouseEvent<HTMLButtonElement>) => void",
        },
      },
    },
  },
  args: {
    icon: <Smile />,
    label: "FloatButton",
    isExpanded: false,
    iconSide: "start",
    disabled: false,
  },
} satisfies Meta<typeof FloatButton>

export default meta

type Story = StoryObj<typeof FloatButton>

/** Default FloatButton (demonstrates default filled variant) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview FloatButton with default settings. The component defaults to variant 'filled', size 'large', color 'primary', and iconSide 'start'.",
      },
    },
  },
  args: {
    icon: <Smile />,
    label: "Hello",
  },
}

/** FloatButton with different icon positions (start, end) */
export const IconPositions: Story = {
  parameters: {
    docs: {
      description: {
        story: "FloatButton with icon positioned at start or end relative to the label.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <FloatButton {...args} iconSide="start" isExpanded>
        Icon Start
      </FloatButton>
      <FloatButton {...args} iconSide="end" isExpanded>
        Icon End
      </FloatButton>
    </div>
  ),
  args: {
    icon: <Search />,
    label: "Search",
  },
}

/** FloatButton expansion states */
export const ExpansionStates: Story = {
  parameters: {
    docs: {
      description: {
        story: "FloatButton in both collapsed and expanded states.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <FloatButton {...args} isExpanded={false}>
        Collapsed
      </FloatButton>
      <FloatButton {...args} isExpanded={true}>
        Expanded
      </FloatButton>
    </div>
  ),
  args: {
    icon: <Plus />,
    label: "Add Item",
  },
}

/** FloatButton disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "FloatButtons in disabled state across all variants and colors.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 16,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <FloatButton disabled icon={<Edit />} label="Disabled Filled" isExpanded />
      <FloatButton disabled icon={<Search />} label="Disabled Collapsed" />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of FloatButton states including default, hover, focus, active, and disabled states across different variants.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "flex",
          gap: 48,
          alignItems: "center",
          flexWrap: "wrap",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Expanded States</Typography>
          <FloatButton icon={<Plus />} label="Default" isExpanded />
          <FloatButton icon={<Plus />} label="Disabled Default" disabled isExpanded />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Typography level="bodyLarge">Collapsed States</Typography>
          <FloatButton icon={<Plus />} label="Default" />
          <FloatButton icon={<Plus />} label="Disabled" disabled />
        </div>
      </div>
    )
  },
}

/** FloatButton as links */
export const AsLinks: Story = {
  parameters: {
    docs: {
      description: {
        story: "FloatButtons rendered as links when href is provided.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
      <FloatButton
        href="https://example.com"
        icon={<Download />}
        label="Download"
        isExpanded
      />
      <FloatButton
        href="https://example.com"
        icon={<Info />}
        label="Learn More"
        variant="outline"
        isExpanded
      />
      <FloatButton
        href="https://example.com"
        icon={<Search />}
        label="Search"
        variant="text"
        isExpanded
      />
    </div>
  ),
}

/** Interactive FloatButton with toggle functionality */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story: "Interactive FloatButton that toggles between expanded and collapsed states",
      },
    },
  },
  render: () => {
    function InteractiveDemo() {
      const [isExpanded, setIsExpanded] = useState(false)
      const [isLiked, setIsLiked] = useState(false)

      const handleToggleExpansion = useCallback(() => {
        setIsExpanded(prev => !prev)
      }, [])

      const handleToggleLike = useCallback(() => {
        setIsLiked(prev => !prev)
      }, [])

      return (
        <div style={{ display: "flex", gap: 16, alignItems: "center", flexWrap: "wrap" }}>
          <FloatButton
            icon={<Plus />}
            label={isExpanded ? "Collapse" : "Expand"}
            isExpanded={isExpanded}
            onClick={handleToggleExpansion}
          />
        </div>
      )
    }
    return <InteractiveDemo />
  },
}

/** FloatButton in mobile inbox interface */
export const MobileInboxExample: Story = {
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        story: "FloatButton in a realistic mobile inbox interface, demonstrating typical usage for compose/add actions in messaging apps.",
      },
    },
  },
  render: () => {
    function MobileInboxDemo() {
      const [isExpanded, setIsExpanded] = useState(false)

    const mockMessages = [
      {
        id: 1,
        sender: "Ali Connors",
        subject: "Brunch this weekend?",
        preview: "I'll be in your neighborhood doing errands this weekend. Do you want to grab brunch?",
        time: "2m",
        avatar: "👩‍💼"
      },
      {
        id: 2,
        sender: "Alex, Scott, Jennifer",
        subject: "Summer BBQ",
        preview: "Wish I could come, but I'm out of town this weekend.",
        time: "6m",
        avatar: "👨‍💻"
      },
      {
        id: 3,
        sender: "Sandra Adams",
        subject: "Oui Oui",
        preview: "Do you have Paris recommendations? Have you ever been?",
        time: "12m",
        avatar: "👩‍🎨"
      },
      {
        id: 4,
        sender: "Trevor Hansen",
        subject: "Birthday Gift",
        preview: "Have any ideas about what we should get Heidi for her birthday?",
        time: "18m",
        avatar: "👨‍🔬"
      },
      {
        id: 5,
        sender: "Britta Holt",
        subject: "Recipe to try",
        preview: "We should eat this: grated squash. Corn and tomatillo tacos.",
        time: "24m",
        avatar: "👩‍🍳"
      },
      {
        id: 6,
        sender: "David Park",
        subject: "Giants game",
        preview: "Any interest in seeing the Giants game tomorrow?",
        time: "1h",
        avatar: "⚾"
      }
    ]

    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          maxWidth: 400,
          height: 600,
          margin: "0 auto",
          background: "#ffffff",
          borderRadius: 12,
          overflow: "hidden",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
          fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        }}
      >
        {/* Header */}
        <div
          style={{
            background: "linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)",
            color: "white",
            padding: "16px 20px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
            <div style={{ fontSize: 20, cursor: "pointer" }}>☰</div>
            <h1 style={{ margin: 0, fontSize: 20, fontWeight: 500 }}>Inbox</h1>
          </div>
          <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
            <div style={{ fontSize: 20, cursor: "pointer" }}>⋮</div>
          </div>
        </div>

        {/* Today Section */}
        <div style={{ padding: "16px 20px 8px", borderBottom: "1px solid #f0f0f0" }}>
          <h2 style={{ margin: 0, fontSize: 16, fontWeight: 500, color: "#374151" }}>Today</h2>
        </div>

        {/* Messages List */}
        <div style={{ flex: 1, overflow: "auto" }}>
          {mockMessages.map((message, index) => (
            <button
              key={message.id}
              style={{
                padding: "16px 20px",
                borderBottom: index < mockMessages.length - 1 ? "1px solid #f0f0f0" : "none",
                display: "flex",
                alignItems: "flex-start",
                gap: 12,
                cursor: "pointer",
                transition: "background-color 0.2s",
                border: "none",
                background: "transparent",
                width: "100%",
                textAlign: "left",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#f9fafb"
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent"
              }}
              onClick={() => alert(`Opening message: ${message.subject}`)}
            >
              {/* Avatar */}
              <div
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  background: "#e5e7eb",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontSize: 18,
                  flexShrink: 0,
                }}
              >
                {message.avatar}
              </div>

              {/* Message Content */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 4 }}>
                  <h3 style={{ margin: 0, fontSize: 14, fontWeight: 500, color: "#111827" }}>
                    {message.sender}
                  </h3>
                  <span style={{ fontSize: 12, color: "#6b7280" }}>{message.time}</span>
                </div>
                <h4 style={{ margin: "0 0 4px 0", fontSize: 14, fontWeight: 400, color: "#374151" }}>
                  {message.subject}
                </h4>
                <p
                  style={{
                    margin: 0,
                    fontSize: 13,
                    color: "#6b7280",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {message.preview}
                </p>
              </div>
            </button>
          ))}
        </div>

        {/* FloatButton for Compose */}
        <FloatButton
          icon={<Plus />}
          label="Compose"
          isExpanded={isExpanded}
          style={{
            position: "absolute",
            bottom: 20,
            right: 20,
            zIndex: 10,
          }}
          onClick={() => {
            setIsExpanded(!isExpanded)
            setTimeout(() => alert("Compose new message"), 100)
          }}
          onMouseEnter={() => setIsExpanded(true)}
          onMouseLeave={() => setIsExpanded(false)}
        />
      </div>
    )
    }
    return <MobileInboxDemo />
  },
}