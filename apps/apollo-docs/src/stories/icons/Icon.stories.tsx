import React from "react"
import { <PERSON>po<PERSON> } from "@apollo/ui"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Heart } from "@design-systems/apollo-icons"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

import { IconGalleryComponent } from "./components/IconGalleryComponent"

/**
 *
 * The Apollo Icons package provides a comprehensive collection of SVG icons designed specifically
 * for the Apollo Design System. All icons are optimized for performance, accessibility, and
 * consistent visual design.
 *
 */
const meta = {
  title: "Icons/Icons Library",
  component: Heart,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2199-18748&m=dev",
    },
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle>
            A comprehensive collection of 178+ icons for the Apollo Design
            System
          </Subtitle>
          <Description />
          <h2 id="icon-installation">Installation</h2>
          <p>
            To use Apollo Icons in your project, you need to install the package
            and configure your registry:
          </p>

          <h3>1. Update .npmrc</h3>
          <Source
            code={`@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>`}
            language="text"
          />
          <h3>2. Install the Package</h3>
          <Source
            code={`# Using npm
npm install @design-systems/apollo-icons

# Using yarn  
yarn add @design-systems/apollo-icons

# Using pnpm
pnpm add @design-systems/apollo-icons`}
            language="bash"
          />
          <Primary />
          <h2 id="icon-props">Props</h2>
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              marginBottom: "24px",
            }}
          >
            <thead>
              <tr style={{ borderBottom: "2px solid #e5e7eb" }}>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px",
                    fontWeight: "600",
                  }}
                >
                  Prop
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px",
                    fontWeight: "600",
                  }}
                >
                  Type
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px",
                    fontWeight: "600",
                  }}
                >
                  Default
                </th>
                <th
                  style={{
                    textAlign: "left",
                    padding: "12px",
                    fontWeight: "600",
                  }}
                >
                  Description
                </th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                <td
                  style={{
                    padding: "12px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  size
                </td>
                <td style={{ padding: "12px" }}>string | number</td>
                <td style={{ padding: "12px" }}>24</td>
                <td style={{ padding: "12px" }}>Size of the icon in pixels</td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                <td
                  style={{
                    padding: "12px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  color
                </td>
                <td style={{ padding: "12px" }}>string</td>
                <td style={{ padding: "12px" }}>currentColor</td>
                <td style={{ padding: "12px" }}>
                  Color of the icon (CSS color value)
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                <td
                  style={{
                    padding: "12px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  className
                </td>
                <td style={{ padding: "12px" }}>string</td>
                <td style={{ padding: "12px" }}>-</td>
                <td style={{ padding: "12px" }}>Additional CSS class names</td>
              </tr>
              <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                <td
                  style={{
                    padding: "12px",
                    fontFamily: "monospace",
                    fontSize: "14px",
                  }}
                >
                  style
                </td>
                <td style={{ padding: "12px" }}>React.CSSProperties</td>
                <td style={{ padding: "12px" }}>-</td>
                <td style={{ padding: "12px" }}>Inline styles object</td>
              </tr>
            </tbody>
          </table>
          <h2 id="examples">Examples</h2>
          <Stories title=""/>
        </>
      ),
    },
  },
} satisfies Meta<typeof Heart>

export default meta
type Story = StoryObj<typeof meta>

/**
 * Overview examples showing different sizes and styling options.
 */
export const Overview: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "24px", flexWrap: "wrap" }}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Default</Typography>
        <CheckCircle size={24} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Success</Typography>
        <CheckCircle
          size={24}
          style={{ color: "var(--apl-alias-color-success-success)" }}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Warning</Typography>
        <Alert
          size={24}
          style={{ color: "var(--apl-alias-color-warning-warning)" }}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Error</Typography>
        <Alert
          size={24}
          style={{ color: "var(--apl-alias-color-error-error)" }}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Icons inherit the current text color by default and can be styled with CSS color properties.",
      },
      source: {
        code: `<CheckCircle size={24} />
        <CheckCircle size={24} style={{ color: "var(--apl-alias-color-success-success)" }} />
        <Alert size={24} style={{ color: "var(--apl-alias-color-warning-warning)" }} /> 
        <Alert size={24} style={{ color: "var(--apl-alias-color-error-error)" }} />`,
      },
    },
  },
}

/**
 * The main icon gallery showcasing all available icons with search functionality.
 * Click on any icon to copy its import statement to your clipboard.
 */
export const IconLibrary: Story = {
  render: () => <IconGalleryComponent />,
  parameters: {
    docs: {
      description: {
        story:
          "Interactive gallery of all available Apollo icons with search and copy functionality.",
      },
    },
  },
}
