"use client"

import React, { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { Button, Input, Typography } from "@apollo/ui"
import { CheckCircle, icons, Search } from "@design-systems/apollo-icons"

interface IconCategory {
  name: string
  icons: string[]
  description: string
}

// Categorize icons based on their names and common usage patterns
const iconCategories: IconCategory[] = [
  {
    name: "Navigation",
    description: "Icons for navigation, arrows, and directional elements",
    icons: [
      "ArrowUp",
      "ArrowDown",
      "ArrowLeft",
      "ArrowRight",
      "ArrowsAlt",
      "CaretUp",
      "CaretDown",
      "CaretLeft",
      "CaretRight",
      "DoubleLeft",
      "DoubleRight",
      "Up",
      "Down",
      "Left",
      "Right",
      "UpCircle",
      "DownCircle",
      "LeftCircle",
      "RightCircle",
      "ToTop",
      "Menu",
      "Bars",
    ],
  },
  {
    name: "Actions",
    description: "Icons for user actions and interactions",
    icons: [
      "Check",
      "CheckCircle",
      "Close",
      "CloseCircle",
      "Plus",
      "PlusCircle",
      "MinusCircle",
      "MinusSquare",
      "Edit",
      "Delete",
      "DeleteOutlined",
      "Copy",
      "Save",
      "Send",
      "Upload",
      "Download",
      "Export",
      "Import",
      "Sync",
      "Redo",
      "Undo",
      "Refresh",
      "RotateLeft",
      "RotateRight",
    ],
  },
  {
    name: "Communication",
    description: "Icons for messaging, notifications, and communication",
    icons: [
      "Mail",
      "Message",
      "Comment",
      "Bell",
      "Notification",
      "Phone",
      "Mobile",
      "Wifi",
      "Sound",
      "Audio",
      "AudioMuted",
      "VideoCamera",
      "VideoCameraAdd",
    ],
  },
  {
    name: "Files & Documents",
    description: "Icons for files, folders, and document management",
    icons: [
      "File",
      "FileAdd",
      "FileDone",
      "FileDownload",
      "FileExcel",
      "FileExclamation",
      "FileImage",
      "FileSearch",
      "FileSync",
      "FileText",
      "Folder",
      "FolderAdd",
      "FolderOpen",
      "PaperClip",
      "Snippets",
    ],
  },
  {
    name: "User & Profile",
    description: "Icons for users, profiles, and account management",
    icons: [
      "User",
      "UserAdd",
      "UserDelete",
      "UserSwitch",
      "Team",
      "Contacts",
      "Profile2",
      "Idcard",
      "Login",
      "Logout",
      "Lock",
      "Unlock",
    ],
  },
  {
    name: "Status & Feedback",
    description: "Icons for status indicators, alerts, and feedback",
    icons: [
      "Alert",
      "Warning",
      "Info",
      "InfoCircle",
      "Question",
      "QuestionCircle",
      "Exclamation",
      "ExclamationCircle",
      "CheckCircle",
      "CloseCircle",
      "Loading",
      "Loading3Quarters",
      "Exception",
      "Safety",
    ],
  },
  {
    name: "Commerce & Shopping",
    description: "Icons for e-commerce, shopping, and financial transactions",
    icons: [
      "Shop",
      "Shopping",
      "ShoppingCart",
      "CreditCard",
      "Wallet",
      "Bank",
      "Gift",
      "Tag",
      "Tags",
      "Barcode",
      "Qrcode",
    ],
  },
  {
    name: "Charts & Analytics",
    description: "Icons for data visualization and analytics",
    icons: [
      "AreaChart",
      "BarChart",
      "LineChart",
      "PieChart",
      "DotChart",
      "Fund",
      "FundView",
      "Dashboard",
      "Count",
      "SortAscending",
      "SortDescending",
    ],
  },
  {
    name: "Media & Content",
    description: "Icons for media, images, and content management",
    icons: [
      "Picture",
      "Camera",
      "Eye",
      "EyeInvisible",
      "VideoCamera",
      "VideoCameraAdd",
      "Audio",
      "AudioMuted",
      "Fire",
      "Star",
      "Heart",
      "Like",
    ],
  },
  {
    name: "Tools & Settings",
    description: "Icons for tools, settings, and configuration",
    icons: [
      "Setting",
      "Tool",
      "Filter",
      "Search",
      "Scan",
      "Compress",
      "Expand",
      "ExpandAlt",
      "Fullscreen",
      "FullscreenExit",
      "Shrink",
      "ZoomIn",
      "ZoomOut",
      "Scissor",
      "Form",
      "Container",
    ],
  },
  {
    name: "Time & Calendar",
    description: "Icons for time, dates, and scheduling",
    icons: ["Calendar", "ClockCircle", "Schedule", "History", "Hourglass"],
  },
  {
    name: "Miscellaneous",
    description: "Other useful icons",
    icons: [
      "Home",
      "Car",
      "Environment",
      "Flag",
      "Bulb",
      "Smile",
      "Rest",
      "Shake",
      "Solution",
      "Transfer",
      "Carry",
      "CarryOut",
      "Pushpin",
      "Link",
      "Diff",
      "Disconnect",
      "Poweroff",
      "Appstore",
      "Audit",
      "Clear",
      "CloudDownload",
      "CloudSync",
      "CloudUpload",
      "Dash",
      "SmallDash",
      "Ellipsis",
      "More",
      "Read",
      "Receive",
      "Retweet",
      "Stop",
      "Inbox",
    ],
  },
]

export interface IconGalleryComponentProps {
  readonly showCategories?: boolean
  readonly searchPlaceholder?: string
}

export function IconGalleryComponent({
  showCategories = true,
  searchPlaceholder = "Search icons...",
}: IconGalleryComponentProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("All")
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null)

  // Get all available icons
  const allIcons = useMemo(() => Object.entries(icons), [])

  // Filter icons based on search term and category
  const filteredIcons = useMemo(() => {
    let filtered = allIcons

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(([name]) =>
        name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== "All") {
      const category = iconCategories.find(
        (cat) => cat.name === selectedCategory
      )
      if (category) {
        filtered = filtered.filter(([name]) => category.icons.includes(name))
      }
    }

    return filtered
  }, [allIcons, searchTerm, selectedCategory])

  const handleCopy = useCallback((iconName: string) => {
    const importStatement = `import { ${iconName} } from "@design-systems/apollo-icons"`
    navigator.clipboard.writeText(importStatement)

    setCopiedIcon(iconName)
    setTimeout(() => {
      setCopiedIcon(null)
    }, 2000)
  }, [])

  const categoryOptions = ["All", ...iconCategories.map((cat) => cat.name)]

  return (
      <div style={{ padding: "24px" }}>
        {/* Search and Filter Controls */}
        <div
          style={{
            marginBottom: "24px",
            display: "flex",
            flexDirection: "column",
            gap: "16px",
          }}
        >
          <Input
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startDecorator={<Search size={20} />}
          />

          {showCategories && (
            <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
              {categoryOptions.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "filled" : "outline"}
                  size="small"
                  onClick={() => setSelectedCategory(category)}
                  style={{ padding: "8px 12px", borderRadius: "8px" }}
                >
                  {category}
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Icon Grid */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fill, minmax(140px, 1fr))",
            gap: "16px",
            marginBottom: "24px",
          }}
        >
          {filteredIcons.map(([iconName, Icon]) => (
            <button
              key={iconName}
              onClick={() => handleCopy(iconName)}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault()
                  handleCopy(iconName)
                }
              }}
              aria-label={`Copy ${iconName} icon import statement`}
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                padding: "16px",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                cursor: "pointer",
                position: "relative",
                transition: "all 0.2s ease",
                backgroundColor: "#ffffff",
                outline: "none",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#f3f4f6"
                e.currentTarget.style.borderColor = "#d1d5db"
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#ffffff"
                e.currentTarget.style.borderColor = "#e5e7eb"
              }}
              onFocus={(e) => {
                e.currentTarget.style.backgroundColor = "#f3f4f6"
                e.currentTarget.style.borderColor = "#3b82f6"
                e.currentTarget.style.boxShadow =
                  "0 0 0 2px rgba(59, 130, 246, 0.2)"
              }}
              onBlur={(e) => {
                e.currentTarget.style.backgroundColor = "#ffffff"
                e.currentTarget.style.borderColor = "#e5e7eb"
                e.currentTarget.style.boxShadow = "none"
              }}
            >
              <div style={{ marginBottom: "8px" }}>
                {React.createElement(Icon as any, { size: 32 })}
              </div>
              <Typography
                level="caption"
                style={{
                  textAlign: "center",
                  fontSize: "12px",
                  lineHeight: "1.2",
                  wordBreak: "break-word",
                }}
              >
                {iconName}
              </Typography>
              {copiedIcon === iconName && (
                <div
                  style={{
                    position: "absolute",
                    top: "4px",
                    right: "4px",
                    display: "flex",
                    alignItems: "center",
                    gap: "4px",
                    padding: "4px 8px",
                    backgroundColor: "#10b981",
                    color: "white",
                    borderRadius: "4px",
                    fontSize: "11px",
                    fontWeight: "500",
                  }}
                >
                  <CheckCircle size={12} />
                  Copied
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Empty State */}
        {filteredIcons.length === 0 && (
            <Typography level="bodyLarge" align="center">
              No icons found
            </Typography>
        )}

        {/* Results Count */}
        <div style={{ textAlign: "right", marginTop: "16px" }}>
          <Typography level="labelMedium" style={{ color: "#6b7280" }}>
            Showing {filteredIcons.length} of {allIcons.length} icons
          </Typography>
        </div>
      </div>
  )
}
