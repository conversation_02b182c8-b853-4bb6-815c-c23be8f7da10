import { useState } from "react"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  createTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Data Display/Accordion",
  component: Accordion,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Accordion provides collapsible content sections. Users can expand and collapse sections to show or hide content, making it ideal for organizing information hierarchically.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    header: {
      control: { type: "text" },
      description: "Accordion header content",
    },
    expanded: {
      control: { type: "boolean" },
      description: "Whether accordion is initially expanded",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether accordion is disabled",
    },
    borderless: {
      control: { type: "boolean" },
      description: "Remove border styling",
    },
    hasDivider: {
      control: { type: "boolean" },
      description: "Add divider line",
    },
    iconPosition: {
      control: { type: "select" },
      options: ["start", "end"],
      description: "Position of the expand/collapse icon",
    },
    variant: {
      control: { type: "select" },
      options: ["default", "error"],
      description: "Accordion visual variant",
    },
  },
} satisfies Meta<typeof Accordion>

export default meta
type Story = StoryObj<typeof meta>

// Basic accordion
export const Basic: Story = {
  args: {
    header: "Basic Accordion",
    children: (
      <Typography>
        This is the content inside the accordion. Click the header to expand and
        collapse this section.
      </Typography>
    ),
    expanded: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Basic accordion with simple text content.",
      },
    },
  },
}

// Multiple accordions
export const MultipleAccordions: Story = {
  args: { header: "Multiple Accordions" },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        width: "600px",
      }}
    >
      <Accordion header="Getting Started" expanded={true}>
        <Typography gutterBottom>
          Welcome to our platform! This section covers the basics of getting
          started.
        </Typography>
        <ul style={{ marginLeft: "20px" }}>
          <li>
            <Typography>Create an account</Typography>
          </li>
          <li>
            <Typography>Verify your email</Typography>
          </li>
          <li>
            <Typography>Complete your profile</Typography>
          </li>
          <li>
            <Typography>Explore features</Typography>
          </li>
        </ul>
      </Accordion>

      <Accordion header="Account Settings" expanded={false}>
        <Typography gutterBottom>
          Manage your account preferences and settings here.
        </Typography>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            marginTop: "12px",
          }}
        >
          <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <input type="checkbox" defaultChecked />
            Email notifications
          </label>
          <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <input type="checkbox" />
            SMS notifications
          </label>
          <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <input type="checkbox" defaultChecked />
            Newsletter subscription
          </label>
        </div>
      </Accordion>

      <Accordion header="Billing Information" expanded={false}>
        <Typography gutterBottom>
          View and manage your billing information and payment methods.
        </Typography>
        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "6px",
            marginTop: "12px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Current Plan: Pro
          </Typography>
          <Typography level="body-2" style={{ color: "#666" }}>
            Next billing date: January 15, 2025
          </Typography>
        </div>
      </Accordion>

      <Accordion header="Privacy & Security" expanded={false}>
        <Typography gutterBottom>
          Control your privacy settings and security preferences.
        </Typography>
        <div style={{ marginTop: "12px" }}>
          <Typography level="h5" gutterBottom>
            Privacy Settings:
          </Typography>
          <ul style={{ marginLeft: "20px" }}>
            <li>
              <Typography level="body-2">Profile visibility: Public</Typography>
            </li>
            <li>
              <Typography level="body-2">Data sharing: Enabled</Typography>
            </li>
            <li>
              <Typography level="body-2">Analytics: Enabled</Typography>
            </li>
          </ul>
        </div>
      </Accordion>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Multiple accordions with different content types and initial states.",
      },
    },
  },
}

// Controlled accordion
export const Controlled: Story = {
  args: { header: "Controlled Accordion" },
  render: () => {
    const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
      {
        section1: true,
        section2: false,
        section3: false,
      }
    )

    const handleToggle = (key: string, expanded: boolean) => {
      setExpandedItems((prev) => ({ ...prev, [key]: expanded }))
    }

    const expandAll = () => {
      setExpandedItems({ section1: true, section2: true, section3: true })
    }

    const collapseAll = () => {
      setExpandedItems({ section1: false, section2: false, section3: false })
    }

    return (
      <div style={{ width: "600px" }}>
        <div
          style={{
            display: "flex",
            gap: "12px",
            marginBottom: "16px",
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "6px",
          }}
        >
          <Button onClick={expandAll} size="sm">
            Expand All
          </Button>
          <Button onClick={collapseAll} size="sm" variant="outline">
            Collapse All
          </Button>
          <Typography
            level="caption"
            style={{
              alignSelf: "center",
              marginLeft: "auto",
              color: "#666",
            }}
          >
            Expanded: {Object.values(expandedItems).filter(Boolean).length}/3
          </Typography>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <Accordion
            header="Project Overview"
            expanded={expandedItems.section1}
            onStateChange={(expanded) => handleToggle("section1", expanded)}
          >
            <Typography gutterBottom>
              This project aims to improve user experience through better design
              and functionality.
            </Typography>
            <div style={{ marginTop: "12px" }}>
              <Typography level="h5" gutterBottom>
                Key Objectives:
              </Typography>
              <ul style={{ marginLeft: "20px" }}>
                <li>
                  <Typography>Enhance user interface design</Typography>
                </li>
                <li>
                  <Typography>Improve performance metrics</Typography>
                </li>
                <li>
                  <Typography>Increase user engagement</Typography>
                </li>
                <li>
                  <Typography>Reduce support tickets</Typography>
                </li>
              </ul>
            </div>
          </Accordion>

          <Accordion
            header="Team Members"
            expanded={expandedItems.section2}
            onStateChange={(expanded) => handleToggle("section2", expanded)}
          >
            <Typography gutterBottom>
              Meet the talented team working on this project.
            </Typography>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
                gap: "12px",
                marginTop: "12px",
              }}
            >
              <div
                style={{
                  padding: "8px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "4px",
                }}
              >
                <Typography level="h5">Sarah Johnson</Typography>
                <Typography level="caption">Project Manager</Typography>
              </div>
              <div
                style={{
                  padding: "8px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "4px",
                }}
              >
                <Typography level="h5">Mike Chen</Typography>
                <Typography level="caption">Lead Developer</Typography>
              </div>
              <div
                style={{
                  padding: "8px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "4px",
                }}
              >
                <Typography level="h5">Emily Davis</Typography>
                <Typography level="caption">UX Designer</Typography>
              </div>
              <div
                style={{
                  padding: "8px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "4px",
                }}
              >
                <Typography level="h5">Alex Rivera</Typography>
                <Typography level="caption">QA Engineer</Typography>
              </div>
            </div>
          </Accordion>

          <Accordion
            header="Timeline & Milestones"
            expanded={expandedItems.section3}
            onStateChange={(expanded) => handleToggle("section3", expanded)}
          >
            <Typography gutterBottom>
              Project timeline with key milestones and deliverables.
            </Typography>
            <div style={{ marginTop: "12px" }}>
              <div
                style={{ display: "flex", flexDirection: "column", gap: "8px" }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography level="body-2">✅ Phase 1: Planning</Typography>
                  <Typography level="caption" style={{ color: "#666" }}>
                    Completed
                  </Typography>
                </div>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography level="body-2">
                    🔄 Phase 2: Development
                  </Typography>
                  <Typography level="caption" style={{ color: "#666" }}>
                    In Progress
                  </Typography>
                </div>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography level="body-2">⏳ Phase 3: Testing</Typography>
                  <Typography level="caption" style={{ color: "#666" }}>
                    Upcoming
                  </Typography>
                </div>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography level="body-2">📦 Phase 4: Deployment</Typography>
                  <Typography level="caption" style={{ color: "#666" }}>
                    Pending
                  </Typography>
                </div>
              </div>
            </div>
          </Accordion>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled accordions with expand/collapse all functionality and state tracking.",
      },
    },
  },
}

// Different variants and styling
export const VariantsAndStyling: Story = {
  args: { header: "Variants and Styling" },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Default Variant
        </Typography>
        <Accordion header="Standard Accordion" expanded={true}>
          <Typography>
            This is the default accordion variant with standard styling and
            border.
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Error Variant
        </Typography>
        <Accordion header="Error Information" variant="error" expanded={true}>
          <Typography>
            This accordion uses the error variant, typically used to display
            error messages or warnings.
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Borderless Style
        </Typography>
        <Accordion header="Borderless Accordion" borderless expanded={true}>
          <Typography>
            This accordion has no border, creating a cleaner look for certain
            layouts.
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          With Divider
        </Typography>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <Accordion header="First Section" hasDivider expanded={false}>
            <Typography>Content for the first section.</Typography>
          </Accordion>
          <Accordion header="Second Section" hasDivider expanded={false}>
            <Typography>Content for the second section.</Typography>
          </Accordion>
          <Accordion header="Third Section" hasDivider expanded={false}>
            <Typography>Content for the third section.</Typography>
          </Accordion>
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Disabled State
        </Typography>
        <Accordion header="Disabled Accordion" disabled expanded={false}>
          <Typography>
            This content is not accessible because the accordion is disabled.
          </Typography>
        </Accordion>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different accordion variants and styling options: default, error, borderless, with dividers, and disabled.",
      },
    },
  },
}

// Icon positioning
export const IconPositioning: Story = {
  args: { header: "Icon Positioning" },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "500px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Icon at End (Default)
        </Typography>
        <Accordion
          header="Settings & Preferences"
          iconPosition="end"
          expanded={false}
        >
          <Typography>
            The expand/collapse icon appears at the end of the header (right
            side).
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Icon at Start
        </Typography>
        <Accordion header="User Profile" iconPosition="start" expanded={false}>
          <Typography>
            The expand/collapse icon appears at the start of the header (left
            side).
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Custom Icon
        </Typography>
        <Accordion
          header="Custom Icon Example"
          icon={<span style={{ fontSize: "16px" }}>🔧</span>}
          expanded={false}
        >
          <Typography>
            This accordion uses a custom icon instead of the default arrow.
          </Typography>
        </Accordion>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          No Icon
        </Typography>
        <Accordion header="No Icon Accordion" icon={null} expanded={false}>
          <Typography>
            This accordion has no icon at all, creating a minimal appearance.
          </Typography>
        </Accordion>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Different icon positioning options and custom icon examples.",
      },
    },
  },
}

// Complex content examples
export const ComplexContent: Story = {
  args: { header: "Complex Content" },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "700px",
      }}
    >
      <Accordion
        header={
          <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
            <span style={{ fontSize: "20px" }}>📊</span>
            <div>
              <Typography level="h5" style={{ margin: 0 }}>
                Analytics Dashboard
              </Typography>
              <Typography level="caption" style={{ margin: 0, color: "#666" }}>
                Last updated: 2 hours ago
              </Typography>
            </div>
          </div>
        }
        expanded={true}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
            gap: "16px",
          }}
        >
          <div
            style={{
              padding: "16px",
              background: "#e3f2fd",
              borderRadius: "8px",
              textAlign: "center",
            }}
          >
            <Typography level="h4" style={{ margin: "0 0 4px 0" }}>
              1,234
            </Typography>
            <Typography level="caption">Total Users</Typography>
          </div>
          <div
            style={{
              padding: "16px",
              background: "#e8f5e8",
              borderRadius: "8px",
              textAlign: "center",
            }}
          >
            <Typography level="h4" style={{ margin: "0 0 4px 0" }}>
              $12,345
            </Typography>
            <Typography level="caption">Revenue</Typography>
          </div>
          <div
            style={{
              padding: "16px",
              background: "#fff3e0",
              borderRadius: "8px",
              textAlign: "center",
            }}
          >
            <Typography level="h4" style={{ margin: "0 0 4px 0" }}>
              98.5%
            </Typography>
            <Typography level="caption">Uptime</Typography>
          </div>
        </div>
      </Accordion>

      <Accordion header="Form Builder" expanded={false}>
        <Typography gutterBottom>
          Create and customize forms with our drag-and-drop builder.
        </Typography>
        <div
          style={{
            border: "2px dashed #ddd",
            padding: "40px",
            textAlign: "center",
            borderRadius: "8px",
            marginTop: "12px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Drag components here
          </Typography>
          <div
            style={{
              display: "flex",
              gap: "8px",
              justifyContent: "center",
              marginTop: "16px",
            }}
          >
            <Button size="sm" variant="outline">
              + Text Input
            </Button>
            <Button size="sm" variant="outline">
              + Checkbox
            </Button>
            <Button size="sm" variant="outline">
              + Button
            </Button>
          </div>
        </div>
      </Accordion>

      <Accordion header="File Manager" expanded={false}>
        <Typography gutterBottom>
          Manage your uploaded files and documents.
        </Typography>
        <div style={{ marginTop: "12px" }}>
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead>
              <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                <th style={{ padding: "8px", textAlign: "left" }}>Name</th>
                <th style={{ padding: "8px", textAlign: "left" }}>Size</th>
                <th style={{ padding: "8px", textAlign: "left" }}>Modified</th>
                <th style={{ padding: "8px", textAlign: "left" }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ borderBottom: "1px solid #f0f0f0" }}>
                <td style={{ padding: "8px" }}>📄 document.pdf</td>
                <td style={{ padding: "8px" }}>2.3 MB</td>
                <td style={{ padding: "8px" }}>2 hours ago</td>
                <td style={{ padding: "8px" }}>
                  <Button size="sm" variant="plain">
                    Download
                  </Button>
                </td>
              </tr>
              <tr style={{ borderBottom: "1px solid #f0f0f0" }}>
                <td style={{ padding: "8px" }}>🖼️ image.jpg</td>
                <td style={{ padding: "8px" }}>856 KB</td>
                <td style={{ padding: "8px" }}>1 day ago</td>
                <td style={{ padding: "8px" }}>
                  <Button size="sm" variant="plain">
                    Download
                  </Button>
                </td>
              </tr>
              <tr>
                <td style={{ padding: "8px" }}>📊 spreadsheet.xlsx</td>
                <td style={{ padding: "8px" }}>1.1 MB</td>
                <td style={{ padding: "8px" }}>3 days ago</td>
                <td style={{ padding: "8px" }}>
                  <Button size="sm" variant="plain">
                    Download
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </Accordion>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Accordions with complex content including custom headers, dashboards, forms, and file managers.",
      },
    },
  },
}

// FAQ example
export const FAQExample: Story = {
  args: { header: "FAQ Example" },
  render: () => (
    <div style={{ width: "650px" }}>
      <Typography level="h3" gutterBottom style={{ marginBottom: "24px" }}>
        Frequently Asked Questions
      </Typography>

      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Accordion
          header="How do I create an account?"
          expanded={true}
          hasDivider
        >
          <Typography gutterBottom>
            Creating an account is simple! Follow these steps:
          </Typography>
          <ol style={{ marginLeft: "20px" }}>
            <li>
              <Typography>
                Click the "Sign Up" button in the top right corner
              </Typography>
            </li>
            <li>
              <Typography>
                Fill in your email address and create a password
              </Typography>
            </li>
            <li>
              <Typography>
                Verify your email address by clicking the link we send you
              </Typography>
            </li>
            <li>
              <Typography>Complete your profile information</Typography>
            </li>
          </ol>
          <Typography style={{ marginTop: "12px" }}>
            If you encounter any issues, please contact our support team.
          </Typography>
        </Accordion>

        <Accordion
          header="What payment methods do you accept?"
          expanded={false}
          hasDivider
        >
          <Typography gutterBottom>
            We accept all major payment methods to make it convenient for you:
          </Typography>
          <ul style={{ marginLeft: "20px" }}>
            <li>
              <Typography>
                Credit cards (Visa, MasterCard, American Express)
              </Typography>
            </li>
            <li>
              <Typography>Debit cards</Typography>
            </li>
            <li>
              <Typography>PayPal</Typography>
            </li>
            <li>
              <Typography>Bank transfers</Typography>
            </li>
            <li>
              <Typography>Apple Pay and Google Pay</Typography>
            </li>
          </ul>
          <Typography style={{ marginTop: "12px" }}>
            All payments are processed securely through encrypted connections.
          </Typography>
        </Accordion>

        <Accordion
          header="Can I cancel my subscription anytime?"
          expanded={false}
          hasDivider
        >
          <Typography gutterBottom>
            Yes, you can cancel your subscription at any time with no penalties
            or fees.
          </Typography>
          <Typography gutterBottom>To cancel your subscription:</Typography>
          <ol style={{ marginLeft: "20px" }}>
            <li>
              <Typography>Go to your Account Settings</Typography>
            </li>
            <li>
              <Typography>Click on "Billing & Subscription"</Typography>
            </li>
            <li>
              <Typography>Select "Cancel Subscription"</Typography>
            </li>
            <li>
              <Typography>Confirm your cancellation</Typography>
            </li>
          </ol>
          <Typography style={{ marginTop: "12px" }}>
            You'll retain access to all features until the end of your current
            billing period.
          </Typography>
        </Accordion>

        <Accordion header="Is my data secure?" expanded={false} hasDivider>
          <Typography gutterBottom>
            Absolutely! We take data security very seriously and implement
            multiple layers of protection:
          </Typography>
          <ul style={{ marginLeft: "20px" }}>
            <li>
              <Typography>
                256-bit SSL encryption for all data transmission
              </Typography>
            </li>
            <li>
              <Typography>
                Regular security audits and penetration testing
              </Typography>
            </li>
            <li>
              <Typography>SOC 2 Type II compliance</Typography>
            </li>
            <li>
              <Typography>GDPR and CCPA compliant data handling</Typography>
            </li>
            <li>
              <Typography>24/7 security monitoring</Typography>
            </li>
          </ul>
          <Typography style={{ marginTop: "12px" }}>
            Your data is stored in secure, encrypted databases with regular
            backups.
          </Typography>
        </Accordion>

        <Accordion
          header="How do I contact support?"
          expanded={false}
          hasDivider
        >
          <Typography gutterBottom>
            We offer multiple ways to get help when you need it:
          </Typography>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(2, 1fr)",
              gap: "12px",
              marginTop: "12px",
            }}
          >
            <div
              style={{
                padding: "12px",
                border: "1px solid #e0e0e0",
                borderRadius: "6px",
              }}
            >
              <Typography level="h5" gutterBottom>
                💬 Live Chat
              </Typography>
              <Typography level="body-2">
                Available 24/7 for immediate assistance
              </Typography>
            </div>
            <div
              style={{
                padding: "12px",
                border: "1px solid #e0e0e0",
                borderRadius: "6px",
              }}
            >
              <Typography level="h5" gutterBottom>
                📧 Email
              </Typography>
              <Typography level="body-2">
                <EMAIL> - Response within 2 hours
              </Typography>
            </div>
            <div
              style={{
                padding: "12px",
                border: "1px solid #e0e0e0",
                borderRadius: "6px",
              }}
            >
              <Typography level="h5" gutterBottom>
                📞 Phone
              </Typography>
              <Typography level="body-2">
                1-800-SUPPORT (Mon-Fri, 9AM-6PM EST)
              </Typography>
            </div>
            <div
              style={{
                padding: "12px",
                border: "1px solid #e0e0e0",
                borderRadius: "6px",
              }}
            >
              <Typography level="h5" gutterBottom>
                📚 Help Center
              </Typography>
              <Typography level="body-2">
                Self-service articles and tutorials
              </Typography>
            </div>
          </div>
        </Accordion>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Real-world FAQ implementation using accordions with dividers and comprehensive content.",
      },
    },
  },
}
