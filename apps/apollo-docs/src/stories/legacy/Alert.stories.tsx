import { useState } from "react"
import { <PERSON><PERSON>, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Feedback/Alert",
  component: Alert,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "<PERSON><PERSON> displays brief, important messages to users. It supports different color variants for various message types, custom icons, titles, descriptions, and dismissible functionality.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: { type: "select" },
      options: ["success", "info", "warning", "error"],
      description: "Color variant of the alert",
    },
    title: {
      control: { type: "text" },
      description: "Alert title",
    },
    description: {
      control: { type: "text" },
      description: "Alert description",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the alert takes full width",
    },
    onClose: {
      action: "closed",
      description: "Close handler - shows close button when provided",
    },
  },
} satisfies Meta<typeof Alert>

export default meta
type Story = StoryObj<typeof meta>

// Basic alert
export const Basic: Story = {
  args: {
    color: "info",
    title: "Information",
    description: "This is a basic information alert.",
  },
  parameters: {
    docs: {
      description: {
        story: "Basic alert with title and description.",
      },
    },
  },
}

// All color variants
export const ColorVariants: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "400px",
      }}
    >
      <Alert
        color="success"
        title="Success"
        description="Your action was completed successfully."
      />

      <Alert
        color="info"
        title="Information"
        description="Here's some helpful information for you."
      />

      <Alert
        color="warning"
        title="Warning"
        description="Please be careful with this action."
      />

      <Alert
        color="error"
        title="Error"
        description="Something went wrong. Please try again."
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different alert color variants: success, info, warning, and error.",
      },
    },
  },
}

// Simple alerts without description
export const TitleOnly: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        width: "300px",
      }}
    >
      <Alert color="success" title="File saved successfully" />
      <Alert color="info" title="Update available" />
      <Alert color="warning" title="Storage almost full" />
      <Alert color="error" title="Connection failed" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Simple alerts with only titles, no descriptions.",
      },
    },
  },
}

// Dismissible alerts
export const Dismissible: Story = {
  render: () => {
    const [alerts, setAlerts] = useState([
      {
        id: 1,
        color: "success" as const,
        title: "Upload Complete",
        description: "Your files have been uploaded successfully.",
      },
      {
        id: 2,
        color: "warning" as const,
        title: "Session Expiring",
        description: "Your session will expire in 5 minutes.",
      },
      {
        id: 3,
        color: "info" as const,
        title: "New Feature",
        description: "Check out our new dashboard design!",
      },
    ])

    const handleClose = (id: number) => {
      setAlerts((prev) => prev.filter((alert) => alert.id !== id))
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "12px",
          width: "400px",
        }}
      >
        {alerts.map((alert) => (
          <Alert
            key={alert.id}
            color={alert.color}
            title={alert.title}
            description={alert.description}
            onClose={() => handleClose(alert.id)}
          />
        ))}

        {alerts.length === 0 && (
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
              border: "2px dashed #ddd",
              borderRadius: "8px",
            }}
          >
            All alerts dismissed!
            <br />
            <button
              onClick={() =>
                setAlerts([
                  {
                    id: Date.now() + 1,
                    color: "info",
                    title: "Welcome Back",
                    description: "Alerts have been restored.",
                  },
                ])
              }
              style={{
                marginTop: "8px",
                padding: "4px 8px",
                background: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Add Alert
            </button>
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Dismissible alerts with close button functionality.",
      },
    },
  },
}

// Full width alerts
export const FullWidth: Story = {
  render: () => (
    <div
      style={{
        width: "100%",
        maxWidth: "600px",
        display: "flex",
        flexDirection: "column",
        gap: "12px",
      }}
    >
      <Alert
        fullWidth
        color="info"
        title="System Maintenance"
        description="We'll be performing scheduled maintenance tonight from 11 PM to 1 AM EST. During this time, some features may be unavailable."
      />

      <Alert
        fullWidth
        color="warning"
        title="Important Update"
        description="Please review the new terms of service before your next login."
        onClose={() => {}}
      />

      <Alert
        fullWidth
        color="success"
        title="Account Verified"
        description="Your email address has been successfully verified. You now have access to all features."
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Full width alerts that expand to their container width.",
      },
    },
  },
}

// Alerts with custom content
export const CustomContent: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "450px",
      }}
    >
      <Alert
        color="info"
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            📋 <span>Survey Available</span>
          </div>
        }
        description={
          <div>
            <p style={{ margin: "4px 0 8px 0" }}>
              Help us improve by taking a quick survey.
            </p>
            <button
              style={{
                padding: "4px 12px",
                background: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              Take Survey
            </button>
          </div>
        }
        onClose={() => {}}
      />

      <Alert
        color="warning"
        title="Storage Limit"
        description={
          <div>
            <p style={{ margin: "4px 0 8px 0" }}>
              You're using 95% of your storage space.
            </p>
            <div style={{ display: "flex", gap: "8px" }}>
              <button
                style={{
                  padding: "4px 12px",
                  background: "#ffc107",
                  color: "black",
                  border: "none",
                  borderRadius: "4px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                Upgrade Plan
              </button>
              <button
                style={{
                  padding: "4px 12px",
                  background: "transparent",
                  color: "#666",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                Manage Files
              </button>
            </div>
          </div>
        }
      />

      <Alert
        color="success"
        title="🎉 Congratulations!"
        description={
          <div>
            <p style={{ margin: "4px 0 8px 0" }}>
              You've completed all your tasks for today.
            </p>
            <div
              style={{
                padding: "8px",
                background: "rgba(255,255,255,0.2)",
                borderRadius: "4px",
                fontSize: "12px",
              }}
            >
              <strong>Daily Stats:</strong> 8 tasks completed, 2 hours saved
            </div>
          </div>
        }
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Alerts with custom title and description content including buttons and formatted text.",
      },
    },
  },
}

// Alerts with custom decorators
export const CustomDecorators: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "400px",
      }}
    >
      <Alert
        color="info"
        title="Download Progress"
        description="Installing updates... 65% complete"
        startDecorator={<div style={{ fontSize: "24px" }}>⬇️</div>}
        endDecorator={
          <div
            style={{
              width: "40px",
              height: "6px",
              background: "#e0e0e0",
              borderRadius: "3px",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                width: "65%",
                height: "100%",
                background: "#007bff",
                transition: "width 0.3s ease",
              }}
            />
          </div>
        }
      />

      <Alert
        color="warning"
        title="Security Alert"
        description="Unusual login detected from new device"
        startDecorator={<div style={{ fontSize: "20px" }}>🔒</div>}
        endDecorator={
          <div style={{ display: "flex", gap: "4px" }}>
            <button
              style={{
                padding: "2px 8px",
                background: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "3px",
                fontSize: "11px",
                cursor: "pointer",
              }}
            >
              ✓
            </button>
            <button
              style={{
                padding: "2px 8px",
                background: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "3px",
                fontSize: "11px",
                cursor: "pointer",
              }}
            >
              ✗
            </button>
          </div>
        }
      />

      <Alert
        color="success"
        title="Backup Complete"
        description="All files backed up to cloud storage"
        startDecorator={<div style={{ fontSize: "20px" }}>☁️</div>}
        endDecorator={
          <div style={{ fontSize: "12px", color: "#666" }}>2:34 PM</div>
        }
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Alerts with custom start and end decorators including progress bars, action buttons, and timestamps.",
      },
    },
  },
}

// Real-world use cases
export const UseCases: Story = {
  render: () => {
    const [showSuccess, setShowSuccess] = useState(false)
    const [showError, setShowError] = useState(false)

    const handleSave = () => {
      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 3000)
    }

    const handleError = () => {
      setShowError(true)
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          width: "500px",
        }}
      >
        <div>
          <h4 style={{ margin: "0 0 12px 0", fontSize: "14px" }}>
            Form Validation
          </h4>
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <input
              placeholder="Enter email address"
              style={{
                padding: "8px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                borderColor: showError ? "#dc3545" : "#ddd",
              }}
            />
            {showError && (
              <Alert
                color="error"
                title="Invalid Email"
                description="Please enter a valid email address."
                onClose={() => setShowError(false)}
              />
            )}
          </div>

          <div style={{ marginTop: "12px", display: "flex", gap: "8px" }}>
            <button
              onClick={handleSave}
              style={{
                padding: "8px 16px",
                background: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Save
            </button>
            <button
              onClick={handleError}
              style={{
                padding: "8px 16px",
                background: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Trigger Error
            </button>
          </div>

          {showSuccess && (
            <div style={{ marginTop: "12px" }}>
              <Alert
                color="success"
                title="Saved Successfully"
                description="Your changes have been saved."
                onClose={() => setShowSuccess(false)}
              />
            </div>
          )}
        </div>

        <div>
          <h4 style={{ margin: "0 0 12px 0", fontSize: "14px" }}>
            System Notifications
          </h4>
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <Alert
              color="info"
              title="System Update"
              description="A new version is available. Restart to apply updates."
              onClose={() => {}}
            />

            <Alert
              color="warning"
              title="Maintenance Window"
              description="Scheduled maintenance starts in 30 minutes."
            />
          </div>
        </div>

        <div>
          <h4 style={{ margin: "0 0 12px 0", fontSize: "14px" }}>
            Feature Announcements
          </h4>
          <Alert
            fullWidth
            color="success"
            title="🎉 New Feature Available"
            description={
              <div>
                <p style={{ margin: "4px 0 8px 0" }}>
                  Introducing dark mode! Switch themes in your profile settings.
                </p>
                <button
                  style={{
                    padding: "4px 12px",
                    background: "#28a745",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    fontSize: "12px",
                    cursor: "pointer",
                  }}
                >
                  Try Dark Mode
                </button>
              </div>
            }
            onClose={() => {}}
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world use cases: form validation, system notifications, and feature announcements.",
      },
    },
  },
}
