import { useState } from "react"
import {
  Autocomplete,
  createTheme,
  ThemeProvider,
  type AutocompleteOption,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Autocomplete",
  component: Autocomplete,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Autocomplete component provides a text input enhanced with a panel of suggested options. It supports single and multiple selection, search filtering, async loading, and extensive customization.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text for the input",
    },
    label: {
      control: { type: "text" },
      description: "Label for the autocomplete field",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the autocomplete",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the autocomplete is disabled",
    },
    multiple: {
      control: { type: "boolean" },
      description: "Whether multiple selections are allowed",
    },
    loading: {
      control: { type: "boolean" },
      description: "Whether the autocomplete is in loading state",
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the autocomplete is required",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the autocomplete should take full width",
    },
  },
} satisfies Meta<typeof Autocomplete>

export default meta
type Story = StoryObj<typeof meta>

// Sample data
const countries: AutocompleteOption[] = [
  { label: "🇺🇸 United States", value: "US" },
  { label: "🇨🇦 Canada", value: "CA" },
  { label: "🇬🇧 United Kingdom", value: "UK" },
  { label: "🇩🇪 Germany", value: "DE" },
  { label: "🇫🇷 France", value: "FR" },
  { label: "🇮🇹 Italy", value: "IT" },
  { label: "🇪🇸 Spain", value: "ES" },
  { label: "🇯🇵 Japan", value: "JP" },
  { label: "🇦🇺 Australia", value: "AU" },
  { label: "🇧🇷 Brazil", value: "BR" },
  { label: "🇮🇳 India", value: "IN" },
  { label: "🇨🇳 China", value: "CN" },
]

const programmingLanguages: AutocompleteOption[] = [
  { label: "JavaScript", value: "javascript" },
  { label: "TypeScript", value: "typescript" },
  { label: "Python", value: "python" },
  { label: "Java", value: "java" },
  { label: "C#", value: "csharp" },
  { label: "C++", value: "cpp" },
  { label: "Go", value: "go" },
  { label: "Rust", value: "rust" },
  { label: "Swift", value: "swift" },
  { label: "Kotlin", value: "kotlin" },
  { label: "PHP", value: "php" },
  { label: "Ruby", value: "ruby" },
]

// Basic autocomplete
export const Basic: Story = {
  args: { options: [] },
  render: () => (
    <Autocomplete
      options={countries}
      placeholder="Search for a country..."
      label="Country"
      helperText="Type to search or click to see all options"
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Basic autocomplete with search functionality and predefined options.",
      },
    },
  },
}

// With default value
export const WithDefaultValue: Story = {
  args: { options: [] },
  render: () => (
    <Autocomplete
      options={programmingLanguages}
      placeholder="Search programming languages..."
      label="Favorite Programming Language"
      helperText="Your current selection"
      defaultValue={{ label: "JavaScript", value: "javascript" }}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: "Autocomplete with a pre-selected default value.",
      },
    },
  },
}

// States demonstration
export const States: Story = {
  args: { options: [] },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Normal State</h4>
        <Autocomplete
          options={countries}
          placeholder="Search countries..."
          label="Country"
          helperText="Normal state autocomplete"
        />
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Disabled State
        </h4>
        <Autocomplete
          options={countries}
          placeholder="Cannot search..."
          label="Disabled"
          helperText="This autocomplete is disabled"
          disabled
          defaultValue={{ label: "🇺🇸 United States", value: "US" }}
        />
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Loading State</h4>
        <Autocomplete
          options={countries}
          placeholder="Loading options..."
          label="Loading"
          helperText="Loading state with spinner"
          loading
        />
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Required State
        </h4>
        <Autocomplete
          options={countries}
          placeholder="Required selection..."
          label="Required Field"
          helperText="This field is required"
          required
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Different states of the autocomplete component.",
      },
    },
  },
}

// Controlled autocomplete
export const Controlled: Story = {
  args: { options: [] },
  render: () => {
    const [value, setValue] = useState<AutocompleteOption | null>(null)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Autocomplete
          options={programmingLanguages}
          placeholder="Search programming languages..."
          label="Programming Language"
          value={value}
          onChange={(_, newValue) => setValue(newValue)}
          helperText={
            value ? `Selected: ${value.label}` : "No language selected"
          }
        />

        <div
          style={{ padding: "8px", background: "#f5f5f5", borderRadius: "4px" }}
        >
          <strong>Current selection:</strong> {value ? value.label : "None"}
        </div>

        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <button
            onClick={() =>
              setValue({ label: "JavaScript", value: "javascript" })
            }
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            JavaScript
          </button>
          <button
            onClick={() =>
              setValue({ label: "TypeScript", value: "typescript" })
            }
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            TypeScript
          </button>
          <button
            onClick={() => setValue({ label: "Python", value: "python" })}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Python
          </button>
          <button
            onClick={() => setValue(null)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Clear
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Controlled autocomplete with external state management.",
      },
    },
  },
}

// Multiple selection
export const MultipleSelection: Story = {
  args: { options: [] },
  render: () => {
    const [selectedSkills, setSelectedSkills] = useState<AutocompleteOption[]>([
      { label: "JavaScript", value: "javascript" },
      { label: "TypeScript", value: "typescript" },
    ])

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Autocomplete
          multiple
          options={programmingLanguages}
          placeholder="Search and select skills..."
          label="Technical Skills"
          value={selectedSkills}
          onChange={(_, newValue) =>
            setSelectedSkills(newValue as AutocompleteOption[])
          }
          helperText={`Selected ${selectedSkills.length} skill${selectedSkills.length !== 1 ? "s" : ""}`}
          limitTags={3}
        />

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
          }}
        >
          <strong>Selected Skills:</strong>
          {selectedSkills.length > 0 ? (
            <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
              {selectedSkills.map((skill) => (
                <li key={skill.value}>{skill.label}</li>
              ))}
            </ul>
          ) : (
            <p style={{ margin: "4px 0 0 0", fontStyle: "italic" }}>
              No skills selected
            </p>
          )}
        </div>

        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <button
            onClick={() =>
              setSelectedSkills([
                { label: "JavaScript", value: "javascript" },
                { label: "TypeScript", value: "typescript" },
                { label: "React", value: "react" },
              ])
            }
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Frontend Stack
          </button>
          <button
            onClick={() =>
              setSelectedSkills([
                { label: "Python", value: "python" },
                { label: "Java", value: "java" },
                { label: "Go", value: "go" },
              ])
            }
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Backend Stack
          </button>
          <button
            onClick={() => setSelectedSkills([])}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Clear All
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Multiple selection autocomplete with tag display and limit.",
      },
    },
  },
}

// Async/Search simulation
export const AsyncSearch: Story = {
  args: { options: [] },
  render: () => {
    const [options, setOptions] = useState<AutocompleteOption[]>([])
    const [loading, setLoading] = useState(false)
    const [inputValue, setInputValue] = useState("")

    // Simulate async search
    const handleInputChange = (value: string) => {
      setInputValue(value)

      if (value.length < 2) {
        setOptions([])
        return
      }

      setLoading(true)

      // Simulate API call delay
      setTimeout(() => {
        const filtered = countries.filter((country) =>
          country.label.toLowerCase().includes(value.toLowerCase())
        )
        setOptions(filtered)
        setLoading(false)
      }, 500)
    }

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Autocomplete
          options={options}
          placeholder="Type at least 2 characters..."
          label="Country Search"
          loading={loading}
          onInputChange={(_, value) => handleInputChange(value)}
          helperText="Simulated async search with 500ms delay"
          noItemLabel={
            inputValue.length < 2
              ? "Type at least 2 characters"
              : "No countries found"
          }
        />

        <div
          style={{
            padding: "8px",
            background: "#f0f8ff",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Search status:</strong>{" "}
          {loading ? "🔄 Searching..." : `📍 Found ${options.length} results`}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Simulated async search functionality with loading states.",
      },
    },
  },
}

// Full width example
export const FullWidth: Story = {
  args: { options: [] },
  render: () => (
    <div style={{ width: "100%", maxWidth: "600px" }}>
      <Autocomplete
        options={countries}
        placeholder="Search for your country..."
        label="Country/Region"
        helperText="Full width autocomplete"
        fullWidth
        defaultValue={{ label: "🇺🇸 United States", value: "US" }}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Full width autocomplete that adapts to its container.",
      },
    },
  },
}

// Form integration example
export const FormIntegration: Story = {
  args: { options: [] },
  render: () => {
    const [formData, setFormData] = useState({
      country: null as AutocompleteOption | null,
      languages: [] as AutocompleteOption[],
      experience: null as AutocompleteOption | null,
    })

    const [submitted, setSubmitted] = useState(false)

    const experienceLevels: AutocompleteOption[] = [
      { label: "👶 Beginner (0-1 years)", value: "beginner" },
      { label: "🌱 Junior (1-3 years)", value: "junior" },
      { label: "💼 Mid-level (3-5 years)", value: "mid" },
      { label: "🎯 Senior (5-8 years)", value: "senior" },
      { label: "🚀 Expert (8+ years)", value: "expert" },
    ]

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      setSubmitted(true)
      setTimeout(() => setSubmitted(false), 3000)
    }

    const isValid =
      formData.country && formData.languages.length > 0 && formData.experience

    return (
      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "400px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Developer Profile</h3>

        <Autocomplete
          options={countries}
          placeholder="Search for your country..."
          label="Country"
          required
          value={formData.country}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, country: value }))
          }
          helperText={
            !formData.country && submitted
              ? "Country is required"
              : "Where are you located?"
          }
        />

        <Autocomplete
          multiple
          options={programmingLanguages}
          placeholder="Select programming languages..."
          label="Programming Languages"
          required
          value={formData.languages}
          onChange={(_, value) =>
            setFormData((prev) => ({
              ...prev,
              languages: value as AutocompleteOption[],
            }))
          }
          helperText={
            !formData.languages.length && submitted
              ? "At least one language is required"
              : `Selected ${formData.languages.length} language${formData.languages.length !== 1 ? "s" : ""}`
          }
          limitTags={2}
        />

        <Autocomplete
          options={experienceLevels}
          placeholder="Select your experience level..."
          label="Experience Level"
          required
          value={formData.experience}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, experience: value }))
          }
          helperText={
            !formData.experience && submitted
              ? "Experience level is required"
              : "Your overall experience level"
          }
        />

        <button
          type="submit"
          disabled={!isValid}
          style={{
            padding: "12px 24px",
            background: isValid ? "#007bff" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: isValid ? "pointer" : "not-allowed",
            fontSize: "16px",
            marginTop: "8px",
          }}
        >
          Save Profile
        </button>

        {submitted && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Profile saved successfully!
          </div>
        )}
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete form with validation using multiple autocomplete fields.",
      },
    },
  },
}

// Large dataset simulation
export const LargeDataset: Story = {
  args: { options: [] },
  render: () => {
    // Generate a large dataset
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      label: `Item ${i + 1}: ${Math.random().toString(36).substring(7)}`,
      value: `item-${i + 1}`,
    }))

    const [value, setValue] = useState<AutocompleteOption | null>(null)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Autocomplete
          options={largeDataset}
          placeholder="Search through 1000 items..."
          label="Large Dataset Search"
          value={value}
          onChange={(_, newValue) => setValue(newValue)}
          helperText="Performance test with 1000 options"
        />

        {value && (
          <div
            style={{
              padding: "8px",
              background: "#f5f5f5",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            <strong>Selected:</strong> {value.label}
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Performance demonstration with a large dataset of 1000 items.",
      },
    },
  },
}
