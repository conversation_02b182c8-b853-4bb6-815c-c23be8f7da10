import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  create<PERSON>heme,
  ThemeProvider,
  Typo<PERSON>,
} from "@apollo/ui/legacy"
import type { <PERSON>a, StoryObj } from "@storybook/react"
import { ChevronRight, FileText, Home, Settings, User } from "lucide-react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/Breadcrumbs",
  component: Breadcrumbs,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Breadcrumbs provide navigation that shows a user's location within a website hierarchy and allows navigation up to any of the ancestors.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: false,
      description: "Navigation items to display",
    },
    maxItems: {
      control: { type: "number", min: 1, max: 20 },
      description: "Maximum number of items to display before collapsing",
    },
    itemsBeforeCollapse: {
      control: { type: "number", min: 1, max: 5 },
      description: "Number of items to show before ellipsis",
    },
    itemsAfterCollapse: {
      control: { type: "number", min: 1, max: 5 },
      description: "Number of items to show after ellipsis",
    },
    separator: {
      control: false,
      description: "Custom separator element",
    },
    color: {
      control: { type: "select" },
      options: ["primary", "danger"],
      description: "Color theme for the breadcrumbs",
    },
  },
} satisfies Meta<typeof Breadcrumbs>

export default meta
type Story = StoryObj<typeof meta>

// Basic breadcrumbs
export const Basic: Story = {
  args: {
    children: [
      <a key="home" href="/" style={{ textDecoration: "none" }}>
        Home
      </a>,
      <a key="category" href="/category" style={{ textDecoration: "none" }}>
        Category
      </a>,
      <Typography key="current">Current Page</Typography>,
    ],
  },
  parameters: {
    docs: {
      description: {
        story: "Basic breadcrumb navigation with links and current page.",
      },
    },
  },
}

// With icons
export const WithIcons: Story = {
  render: () => (
    <Breadcrumbs aria-label="Navigation breadcrumb">
      <a
        href="/"
        style={{
          display: "flex",
          alignItems: "center",
          gap: "4px",
          textDecoration: "none",
        }}
      >
        <Home size={16} />
        <span>Home</span>
      </a>
      <a
        href="/profile"
        style={{
          display: "flex",
          alignItems: "center",
          gap: "4px",
          textDecoration: "none",
        }}
      >
        <User size={16} />
        <span>Profile</span>
      </a>
      <a
        href="/profile/settings"
        style={{
          display: "flex",
          alignItems: "center",
          gap: "4px",
          textDecoration: "none",
        }}
      >
        <Settings size={16} />
        <span>Settings</span>
      </a>
      <Typography style={{ display: "flex", alignItems: "center", gap: "4px" }}>
        <FileText size={16} />
        <span>Account Details</span>
      </Typography>
    </Breadcrumbs>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Breadcrumbs with icons for better visual hierarchy and recognition.",
      },
    },
  },
}

// Collapsed with ellipsis
export const CollapsedWithEllipsis: Story = {
  render: () => (
    <div style={{ width: "600px" }}>
      <Typography level="h5" gutterBottom>
        Collapsed Breadcrumbs (maxItems: 4)
      </Typography>
      <Breadcrumbs
        maxItems={4}
        itemsBeforeCollapse={1}
        itemsAfterCollapse={1}
        aria-label="Navigation breadcrumb"
      >
        <a href="/" style={{ textDecoration: "none" }}>
          Home
        </a>
        <a href="/electronics" style={{ textDecoration: "none" }}>
          Electronics
        </a>
        <a href="/electronics/computers" style={{ textDecoration: "none" }}>
          Computers
        </a>
        <a
          href="/electronics/computers/laptops"
          style={{ textDecoration: "none" }}
        >
          Laptops
        </a>
        <a
          href="/electronics/computers/laptops/gaming"
          style={{ textDecoration: "none" }}
        >
          Gaming
        </a>
        <a
          href="/electronics/computers/laptops/gaming/asus"
          style={{ textDecoration: "none" }}
        >
          ASUS
        </a>
        <Typography>ROG Strix G15</Typography>
      </Breadcrumbs>

      <Typography level="body-2" style={{ marginTop: "16px", color: "#666" }}>
        Click the "..." to expand and see all breadcrumbs
      </Typography>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "When there are many breadcrumb items, they collapse with an ellipsis that can be clicked to expand.",
      },
    },
  },
}

// Custom separators
export const CustomSeparators: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Arrow Separator
        </Typography>
        <Breadcrumbs
          separator={<ChevronRight size={16} style={{ color: "#666" }} />}
          aria-label="Navigation breadcrumb"
        >
          <a href="/" style={{ textDecoration: "none" }}>
            Home
          </a>
          <a href="/products" style={{ textDecoration: "none" }}>
            Products
          </a>
          <a href="/products/smartphones" style={{ textDecoration: "none" }}>
            Smartphones
          </a>
          <Typography>iPhone 15</Typography>
        </Breadcrumbs>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Slash Separator
        </Typography>
        <Breadcrumbs
          separator={<span style={{ color: "#666", margin: "0 8px" }}>/</span>}
          aria-label="Navigation breadcrumb"
        >
          <a href="/" style={{ textDecoration: "none" }}>
            Home
          </a>
          <a href="/docs" style={{ textDecoration: "none" }}>
            Documentation
          </a>
          <a href="/docs/components" style={{ textDecoration: "none" }}>
            Components
          </a>
          <Typography>Breadcrumbs</Typography>
        </Breadcrumbs>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Bullet Separator
        </Typography>
        <Breadcrumbs
          separator={<span style={{ color: "#666", margin: "0 8px" }}>•</span>}
          aria-label="Navigation breadcrumb"
        >
          <a href="/" style={{ textDecoration: "none" }}>
            Apollo
          </a>
          <a href="/design-system" style={{ textDecoration: "none" }}>
            Design System
          </a>
          <Typography>Navigation</Typography>
        </Breadcrumbs>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different separator styles: arrows, slashes, bullets, or any custom element.",
      },
    },
  },
}

// Different content types
export const DifferentContentTypes: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "700px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          With Buttons
        </Typography>
        <Breadcrumbs aria-label="Navigation breadcrumb">
          <Button variant="plain" size="sm" style={{ padding: "4px 8px" }}>
            Dashboard
          </Button>
          <Button variant="plain" size="sm" style={{ padding: "4px 8px" }}>
            Projects
          </Button>
          <Button variant="plain" size="sm" style={{ padding: "4px 8px" }}>
            Apollo UI
          </Button>
          <Typography>Settings</Typography>
        </Breadcrumbs>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Mixed Content
        </Typography>
        <Breadcrumbs aria-label="Navigation breadcrumb">
          <a href="/" style={{ textDecoration: "none", fontWeight: "bold" }}>
            🏠 Home
          </a>
          <Button variant="plain" size="sm">
            📁 Projects
          </Button>
          <a
            href="/projects/apollo"
            style={{
              textDecoration: "none",
              padding: "4px 8px",
              background: "#f0f0f0",
              borderRadius: "4px",
            }}
          >
            Apollo Design System
          </a>
          <Typography
            style={{
              padding: "4px 8px",
              background: "#e3f2fd",
              borderRadius: "4px",
              fontWeight: "medium",
            }}
          >
            Components Library
          </Typography>
        </Breadcrumbs>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Long Text Handling
        </Typography>
        <Breadcrumbs aria-label="Navigation breadcrumb">
          <a href="/" style={{ textDecoration: "none" }}>
            Home
          </a>
          <a
            href="/very-long-category-name"
            style={{
              textDecoration: "none",
              maxWidth: "120px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              display: "inline-block",
            }}
          >
            Very Long Category Name That Might Overflow
          </a>
          <a
            href="/another-long-name"
            style={{
              textDecoration: "none",
              maxWidth: "120px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              display: "inline-block",
            }}
          >
            Another Long Subcategory Name
          </a>
          <Typography style={{ fontWeight: "medium" }}>Current Page</Typography>
        </Breadcrumbs>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Breadcrumbs with different content types: buttons, mixed elements, and long text handling.",
      },
    },
  },
}

// Color variants
export const ColorVariants: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "500px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Primary Theme (Default)
        </Typography>
        <Breadcrumbs color="primary" aria-label="Navigation breadcrumb">
          <a href="/" style={{ textDecoration: "none" }}>
            Home
          </a>
          <a href="/products" style={{ textDecoration: "none" }}>
            Products
          </a>
          <a href="/products/electronics" style={{ textDecoration: "none" }}>
            Electronics
          </a>
          <Typography>Smartphones</Typography>
        </Breadcrumbs>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Danger Theme
        </Typography>
        <Breadcrumbs color="danger" aria-label="Navigation breadcrumb">
          <a href="/" style={{ textDecoration: "none" }}>
            Home
          </a>
          <a href="/admin" style={{ textDecoration: "none" }}>
            Admin
          </a>
          <a href="/admin/system" style={{ textDecoration: "none" }}>
            System
          </a>
          <Typography>Error Logs</Typography>
        </Breadcrumbs>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different color themes for breadcrumbs: primary (default) and danger.",
      },
    },
  },
}

// Real-world ecommerce example
export const EcommerceExample: Story = {
  render: () => (
    <div style={{ width: "800px" }}>
      <div
        style={{
          padding: "16px",
          background: "#f8f9fa",
          borderRadius: "8px",
          marginBottom: "20px",
        }}
      >
        <Typography level="h4" gutterBottom>
          Product Detail Page
        </Typography>
        <Breadcrumbs
          maxItems={6}
          itemsBeforeCollapse={2}
          itemsAfterCollapse={2}
          aria-label="Product navigation"
        >
          <a
            href="/"
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
              textDecoration: "none",
              fontWeight: "500",
            }}
          >
            <Home size={16} />
            <span>CJ Express</span>
          </a>
          <a href="/categories" style={{ textDecoration: "none" }}>
            All Categories
          </a>
          <a href="/categories/electronics" style={{ textDecoration: "none" }}>
            Electronics
          </a>
          <a
            href="/categories/electronics/computers"
            style={{ textDecoration: "none" }}
          >
            Computers & Accessories
          </a>
          <a
            href="/categories/electronics/computers/laptops"
            style={{ textDecoration: "none" }}
          >
            Laptops
          </a>
          <a
            href="/categories/electronics/computers/laptops/gaming"
            style={{ textDecoration: "none" }}
          >
            Gaming Laptops
          </a>
          <a
            href="/categories/electronics/computers/laptops/gaming/brands"
            style={{ textDecoration: "none" }}
          >
            Brands
          </a>
          <a
            href="/categories/electronics/computers/laptops/gaming/brands/asus"
            style={{ textDecoration: "none" }}
          >
            ASUS
          </a>
          <Typography style={{ fontWeight: "600", color: "#1976d2" }}>
            ASUS ROG Strix G15 Gaming Laptop
          </Typography>
        </Breadcrumbs>
      </div>

      <div
        style={{
          padding: "16px",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "12px",
            marginBottom: "12px",
          }}
        >
          <img
            src="https://via.placeholder.com/60x60?text=ASUS"
            alt="ASUS ROG Strix G15"
            style={{ borderRadius: "4px" }}
          />
          <div>
            <Typography level="h5" style={{ margin: 0 }}>
              ASUS ROG Strix G15 Gaming Laptop
            </Typography>
            <Typography
              level="body-2"
              style={{ color: "#666", margin: "4px 0 0 0" }}
            >
              15.6" FHD 144Hz, AMD Ryzen 7, RTX 3060, 16GB RAM, 512GB SSD
            </Typography>
          </div>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
          <Typography level="h4" style={{ color: "#d32f2f", margin: 0 }}>
            $1,299.99
          </Typography>
          <Typography
            level="body-2"
            style={{
              textDecoration: "line-through",
              color: "#999",
              margin: 0,
            }}
          >
            $1,599.99
          </Typography>
          <span
            style={{
              background: "#e8f5e8",
              color: "#2e7d32",
              padding: "2px 8px",
              borderRadius: "12px",
              fontSize: "12px",
              fontWeight: "500",
            }}
          >
            19% OFF
          </span>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Real-world ecommerce example showing product navigation with collapsible breadcrumbs.",
      },
    },
  },
}

// Documentation example
export const DocumentationExample: Story = {
  render: () => (
    <div style={{ width: "700px" }}>
      <div
        style={{
          padding: "20px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          borderRadius: "8px",
          color: "white",
          marginBottom: "20px",
        }}
      >
        <Typography level="h3" style={{ color: "white", margin: "0 0 8px 0" }}>
          Apollo Design System
        </Typography>
        <Typography
          level="body-1"
          style={{ color: "rgba(255,255,255,0.9)", margin: 0 }}
        >
          Component Documentation
        </Typography>
      </div>

      <div style={{ marginBottom: "24px" }}>
        <Breadcrumbs
          separator={<span style={{ color: "#666", margin: "0 8px" }}>/</span>}
          aria-label="Documentation navigation"
        >
          <a
            href="/docs"
            style={{
              textDecoration: "none",
              color: "#1976d2",
              fontWeight: "500",
            }}
          >
            📚 Documentation
          </a>
          <a
            href="/docs/getting-started"
            style={{
              textDecoration: "none",
              color: "#1976d2",
            }}
          >
            Getting Started
          </a>
          <a
            href="/docs/getting-started/installation"
            style={{
              textDecoration: "none",
              color: "#1976d2",
            }}
          >
            Installation
          </a>
          <Typography style={{ fontWeight: "600" }}>Package Setup</Typography>
        </Breadcrumbs>
      </div>

      <div
        style={{
          padding: "20px",
          background: "#f8f9fa",
          borderRadius: "8px",
          border: "1px solid #e0e0e0",
        }}
      >
        <Typography level="h4" gutterBottom>
          Package Installation Guide
        </Typography>
        <Typography gutterBottom>
          Follow these steps to install and configure the Apollo Design System
          in your project.
        </Typography>
        <div
          style={{
            background: "#2d3748",
            color: "#e2e8f0",
            padding: "16px",
            borderRadius: "6px",
            fontFamily: "monospace",
            marginTop: "16px",
          }}
        >
          <div>npm install @apollo/ui</div>
          <div style={{ marginTop: "8px" }}>
            npm install @design-systems/apollo-ui
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Documentation site example with breadcrumb navigation for help and guides.",
      },
    },
  },
}
