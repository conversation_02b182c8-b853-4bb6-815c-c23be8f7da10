import { But<PERSON>, createTheme, Theme<PERSON><PERSON><PERSON> } from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Button",
  component: Button,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <Story />
      </ThemeProvider>
    ),
  ],
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    color: {
      control: { type: "select" },
      options: ["primary", "danger"],
    },
    variant: {
      control: { type: "select" },
      options: ["solid", "outline", "plain"],
    },
    size: {
      control: { type: "select" },
      options: ["md", "lg"],
    },
    disabled: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    loadingPosition: {
      control: { type: "select" },
      options: ["start", "end"],
    },
    fullWidth: {
      control: { type: "boolean" },
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: () => {} },
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof meta>

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default: Story = {
  args: {
    children: "Button",
  },
}

export const Primary: Story = {
  args: {
    color: "primary",
    children: "Primary Button",
  },
}

export const Danger: Story = {
  args: {
    color: "danger",
    children: "Danger Button",
  },
}

export const Outline: Story = {
  args: {
    variant: "outline",
    children: "Outline Button",
  },
}

export const OutlineDanger: Story = {
  args: {
    variant: "outline",
    color: "danger",
    children: "Outline Danger",
  },
}

export const Plain: Story = {
  args: {
    variant: "plain",
    children: "Plain Button",
  },
}

export const PlainDanger: Story = {
  args: {
    variant: "plain",
    color: "danger",
    children: "Plain Danger",
  },
}

export const Small: Story = {
  args: {
    size: "md",
    children: "Small Button",
  },
}

export const Large: Story = {
  args: {
    size: "lg",
    children: "Large Button",
  },
}

export const Disabled: Story = {
  args: {
    disabled: true,
    children: "Disabled Button",
  },
}

export const Loading: Story = {
  args: {
    loading: true,
    children: "Loading Button",
  },
}

export const LoadingEnd: Story = {
  args: {
    loading: true,
    loadingPosition: "end",
    children: "Loading End",
  },
}

export const FullWidth: Story = {
  args: {
    fullWidth: true,
    children: "Full Width Button",
  },
  parameters: {
    layout: "padded",
  },
}

export const WithStartDecorator: Story = {
  args: {
    children: "With Icon",
    startDecorator: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
          fill="currentColor"
        />
      </svg>
    ),
  },
}

export const WithEndDecorator: Story = {
  args: {
    children: "With Arrow",
    endDecorator: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"
          fill="currentColor"
        />
      </svg>
    ),
  },
}

export const CustomLoadingIndicator: Story = {
  args: {
    loading: true,
    children: "Custom Loading",
    loadingIndicator: (
      <div className="w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin mr-2" />
    ),
  },
}

// Showcase all variants in a grid
export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4">
      <div className="space-y-2">
        <h3 className="font-semibold">Filled</h3>
        <Button variant="filled" color="primary">
          Primary
        </Button>
        <Button variant="filled" color="negative">
          Negative
        </Button>
      </div>
      <div className="space-y-2">
        <h3 className="font-semibold">Outline</h3>
        <Button variant="outline" color="primary">
          Primary
        </Button>
        <Button variant="outline" color="negative">
          Negative
        </Button>
      </div>
      <div className="space-y-2">
        <h3 className="font-semibold">Text</h3>
        <Button variant="text" color="primary">
          Primary
        </Button>
        <Button variant="text" color="negative">
          Negative
        </Button>
      </div>
    </div>
  ),
  parameters: {
    layout: "padded",
  },
}

// Showcase different sizes
export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
    </div>
  ),
}

// Showcase loading states
export const LoadingStates: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Button loading loadingPosition="start">
          Loading Start
        </Button>
        <Button loading loadingPosition="end">
          Loading End
        </Button>
      </div>
      <div className="flex gap-4">
        <Button loading variant="outline">
          Outline Loading
        </Button>
        <Button loading variant="plain">
          Plain Loading
        </Button>
      </div>
    </div>
  ),
}
