import { useState } from "react"
import { Checkbox, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Checkbox",
  component: Checkbox,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Checkbox component is used for selecting single or multiple options. It supports various states including checked, unchecked, indeterminate, and disabled states.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The label content for the checkbox",
    },
    labelPlacement: {
      control: { type: "select" },
      options: ["top", "left", "right", "bottom"],
      description: "The placement of the label relative to the checkbox",
    },
    checked: {
      control: { type: "boolean" },
      description: "If true, the checkbox will be checked (controlled)",
    },
    defaultChecked: {
      control: { type: "boolean" },
      description: "The default checked state (uncontrolled)",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the checkbox is disabled",
    },
    indeterminate: {
      control: { type: "boolean" },
      description: "If true, the checkbox will be in an indeterminate state",
    },
    activeLabel: {
      control: { type: "boolean" },
      description: "Whether the label is interactive",
    },
    value: {
      control: { type: "text" },
      description: "The value of the checkbox when submitted in a form",
    },
  },
} satisfies Meta<typeof Checkbox>

export default meta
type Story = StoryObj<typeof meta>

// Default story
export const Default: Story = {
  args: {},
}

// With label
export const WithLabel: Story = {
  args: {
    label: "Accept terms and conditions",
  },
}

// Checked state
export const Checked: Story = {
  args: {
    label: "Pre-checked option",
    defaultChecked: true,
  },
}

// Indeterminate state
export const Indeterminate: Story = {
  args: {
    label: "Indeterminate state",
    indeterminate: true,
  },
}

// Disabled states
export const Disabled: Story = {
  args: {
    label: "Disabled checkbox",
    disabled: true,
  },
}

export const DisabledChecked: Story = {
  args: {
    label: "Disabled and checked",
    defaultChecked: true,
    disabled: true,
  },
}

// Label placement variations
export const LabelTop: Story = {
  args: {
    label: "Label on top",
    labelPlacement: "top",
  },
}

export const LabelLeft: Story = {
  args: {
    label: "Label on left",
    labelPlacement: "left",
  },
}

export const LabelBottom: Story = {
  args: {
    label: "Label on bottom",
    labelPlacement: "bottom",
  },
}

export const LabelRight: Story = {
  args: {
    label: "Label on right (default)",
    labelPlacement: "right",
  },
}

// Interactive controlled example
export const ControlledExample: Story = {
  render: () => {
    const [checked, setChecked] = useState(false)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Checkbox
          label={`Controlled checkbox (${checked ? "checked" : "unchecked"})`}
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
        />
        <button
          onClick={() => setChecked(!checked)}
          style={{
            padding: "8px 16px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            background: "white",
            cursor: "pointer",
          }}
        >
          Toggle programmatically
        </button>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "An example showing controlled checkbox behavior with external state management.",
      },
    },
  },
}

// Select all pattern
export const SelectAllPattern: Story = {
  render: () => {
    const [checkedItems, setCheckedItems] = useState([false, false, false])

    const parentChecked = checkedItems.every(Boolean)
    const parentIndeterminate = checkedItems.some(Boolean) && !parentChecked

    const handleParentChange = () => {
      setCheckedItems(checkedItems.map(() => !parentChecked))
    }

    const handleChildChange = (index: number) => {
      setCheckedItems(checkedItems.map((c, i) => (i === index ? !c : c)))
    }

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
        <Checkbox
          label="Select All"
          checked={parentChecked}
          indeterminate={parentIndeterminate}
          onChange={handleParentChange}
        />
        <div
          style={{
            paddingLeft: "24px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {checkedItems.map((checked, index) => (
            <Checkbox
              key={index}
              label={`Option ${index + 1}`}
              checked={checked}
              onChange={() => handleChildChange(index)}
            />
          ))}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "A common pattern showing a parent checkbox that controls multiple child checkboxes, with indeterminate state when only some children are selected.",
      },
    },
  },
}

// Different checkbox states demo
export const AllStates: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Checkbox />
        <span>Unchecked</span>
      </div>
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Checkbox defaultChecked />
        <span>Checked</span>
      </div>
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Checkbox indeterminate />
        <span>Indeterminate</span>
      </div>
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Checkbox disabled />
        <span>Disabled</span>
      </div>
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Checkbox defaultChecked disabled />
        <span>Disabled & Checked</span>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "All possible states of the checkbox component displayed together for easy comparison.",
      },
    },
  },
}

// Form usage example
export const FormUsage: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      newsletter: false,
      terms: false,
      marketing: false,
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      alert(`Form submitted with: ${JSON.stringify(formData, null, 2)}`)
    }

    return (
      <form
        onSubmit={handleSubmit}
        style={{ display: "flex", flexDirection: "column", gap: "16px" }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>
          Subscription Preferences
        </h3>

        <Checkbox
          label="Subscribe to newsletter"
          checked={formData.newsletter}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, newsletter: e.target.checked }))
          }
          value="newsletter"
        />

        <Checkbox
          label="Accept terms and conditions"
          checked={formData.terms}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, terms: e.target.checked }))
          }
          value="terms"
        />

        <Checkbox
          label="Allow marketing communications"
          checked={formData.marketing}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, marketing: e.target.checked }))
          }
          value="marketing"
        />

        <button
          type="submit"
          style={{
            padding: "8px 16px",
            border: "1px solid #007bff",
            borderRadius: "4px",
            background: "#007bff",
            color: "white",
            cursor: "pointer",
            marginTop: "8px",
          }}
        >
          Submit
        </button>
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing how to use checkboxes in a form with controlled state management.",
      },
    },
  },
}
