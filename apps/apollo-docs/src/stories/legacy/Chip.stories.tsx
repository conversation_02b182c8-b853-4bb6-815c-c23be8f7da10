import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>,
  createTheme,
  ThemeP<PERSON>ider,
  <PERSON>po<PERSON>,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import {
  AlertCircle,
  Award,
  Bookmark,
  Calendar,
  CheckCircle,
  Clock,
  Heart,
  MapPin,
  Shield,
  Star,
  Tag,
  TrendingUp,
  User,
  XCircle,
  Zap,
} from "lucide-react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Data Display/Chip",
  component: Chip,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Chips are compact elements that represent an input, attribute, or action. They allow users to enter information, make selections, filter content, or trigger actions.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Content to display in the chip",
    },
    color: {
      control: { type: "select" },
      options: [
        "default",
        "primary",
        "success",
        "warning",
        "danger",
        "process",
      ],
      description: "Color theme of the chip",
    },
    variant: {
      control: { type: "select" },
      options: ["fill", "outline"],
      description: "Visual variant of the chip",
    },
    rounded: {
      control: { type: "select" },
      options: ["default", "sm", "lg", "xl", "xxl", "none", "full"],
      description: "Border radius of the chip",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the chip is disabled",
    },
    truncatedTextWidth: {
      control: { type: "number", min: 50, max: 300 },
      description: "Maximum width for text truncation",
    },
    startDecorator: {
      control: false,
      description: "Element to display before the label",
    },
    deleteIcon: {
      control: false,
      description: "Custom delete icon",
    },
    onDelete: {
      control: false,
      description: "Function called when delete button is clicked",
    },
  },
} satisfies Meta<typeof Chip>

export default meta
type Story = StoryObj<typeof meta>

// Basic chips
export const Basic: Story = {
  args: {
    label: "Basic Chip",
    color: "default",
    variant: "outline",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Basic chip with default styling and no additional functionality.",
      },
    },
  },
}

// Color variants
export const ColorVariants: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "24px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Fill Variant
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Default" color="default" variant="fill" />
          <Chip label="Primary" color="primary" variant="fill" />
          <Chip label="Success" color="success" variant="fill" />
          <Chip label="Warning" color="warning" variant="fill" />
          <Chip label="Danger" color="danger" variant="fill" />
          <Chip label="Process" color="process" variant="fill" />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Outline Variant
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Default" color="default" variant="outline" />
          <Chip label="Primary" color="primary" variant="outline" />
          <Chip label="Success" color="success" variant="outline" />
          <Chip label="Warning" color="warning" variant="outline" />
          <Chip label="Danger" color="danger" variant="outline" />
          <Chip label="Process" color="process" variant="outline" />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different color themes available for chips in both fill and outline variants.",
      },
    },
  },
}

// Border radius options
export const BorderRadius: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Fill Variant
        </Typography>
        <div
          style={{
            display: "flex",
            gap: "8px",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          <Chip label="None" color="primary" variant="fill" rounded="none" />
          <Chip label="Small" color="primary" variant="fill" rounded="sm" />
          <Chip
            label="Default"
            color="primary"
            variant="fill"
            rounded="default"
          />
          <Chip label="Large" color="primary" variant="fill" rounded="lg" />
          <Chip label="XL" color="primary" variant="fill" rounded="xl" />
          <Chip label="XXL" color="primary" variant="fill" rounded="xxl" />
          <Chip label="Full" color="primary" variant="fill" rounded="full" />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Outline Variant
        </Typography>
        <div
          style={{
            display: "flex",
            gap: "8px",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          <Chip label="None" color="success" variant="outline" rounded="none" />
          <Chip label="Small" color="success" variant="outline" rounded="sm" />
          <Chip
            label="Default"
            color="success"
            variant="outline"
            rounded="default"
          />
          <Chip label="Large" color="success" variant="outline" rounded="lg" />
          <Chip label="XL" color="success" variant="outline" rounded="xl" />
          <Chip label="XXL" color="success" variant="outline" rounded="xxl" />
          <Chip label="Full" color="success" variant="outline" rounded="full" />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different border radius options from sharp corners to fully rounded pills.",
      },
    },
  },
}

// With icons and decorators
export const WithDecorators: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Start Decorators
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip
            label="User Profile"
            color="primary"
            variant="fill"
            startDecorator={<User size={14} />}
          />
          <Chip
            label="Favorites"
            color="danger"
            variant="outline"
            startDecorator={<Heart size={14} />}
          />
          <Chip
            label="Tagged"
            color="warning"
            variant="fill"
            startDecorator={<Tag size={14} />}
          />
          <Chip
            label="Location"
            color="success"
            variant="outline"
            startDecorator={<MapPin size={14} />}
          />
          <Chip
            label="Premium"
            color="process"
            variant="fill"
            startDecorator={<Star size={14} />}
            rounded="full"
          />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Status Indicators
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip
            label="Completed"
            color="success"
            variant="fill"
            startDecorator={<CheckCircle size={14} />}
            rounded="full"
          />
          <Chip
            label="In Progress"
            color="process"
            variant="outline"
            startDecorator={<Clock size={14} />}
          />
          <Chip
            label="Warning"
            color="warning"
            variant="fill"
            startDecorator={<AlertCircle size={14} />}
          />
          <Chip
            label="Error"
            color="danger"
            variant="outline"
            startDecorator={<XCircle size={14} />}
          />
          <Chip
            label="High Priority"
            color="danger"
            variant="fill"
            startDecorator={<Zap size={14} />}
            rounded="sm"
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Chips with start decorators including icons for enhanced visual communication.",
      },
    },
  },
}

// Deletable chips
export const DeletableChips: Story = {
  render: () => {
    const [chips, setChips] = useState([
      { id: 1, label: "React", color: "primary" as const },
      { id: 2, label: "TypeScript", color: "process" as const },
      { id: 3, label: "JavaScript", color: "warning" as const },
      { id: 4, label: "Node.js", color: "success" as const },
      { id: 5, label: "MongoDB", color: "default" as const },
      { id: 6, label: "Express", color: "danger" as const },
    ])

    const removeChip = (id: number) => {
      setChips(chips.filter((chip) => chip.id !== id))
    }

    const resetChips = () => {
      setChips([
        { id: 1, label: "React", color: "primary" },
        { id: 2, label: "TypeScript", color: "process" },
        { id: 3, label: "JavaScript", color: "warning" },
        { id: 4, label: "Node.js", color: "success" },
        { id: 5, label: "MongoDB", color: "default" },
        { id: 6, label: "Express", color: "danger" },
      ])
    }

    return (
      <div style={{ width: "500px" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <Typography level="h5">
            Selected Technologies ({chips.length})
          </Typography>
          <Button size="sm" variant="outline" onClick={resetChips}>
            Reset All
          </Button>
        </div>

        <div
          style={{
            display: "flex",
            gap: "8px",
            flexWrap: "wrap",
            minHeight: "40px",
            padding: "12px",
            border: "1px dashed #ccc",
            borderRadius: "8px",
            background: chips.length === 0 ? "#f9f9f9" : "transparent",
          }}
        >
          {chips.length === 0 ? (
            <Typography
              level="body-2"
              style={{
                color: "#666",
                alignSelf: "center",
                fontStyle: "italic",
              }}
            >
              No technologies selected
            </Typography>
          ) : (
            chips.map((chip) => (
              <Chip
                key={chip.id}
                label={chip.label}
                color={chip.color}
                variant="fill"
                rounded="full"
                onDelete={() => removeChip(chip.id)}
              />
            ))
          )}
        </div>

        <Typography
          level="caption"
          style={{
            marginTop: "8px",
            color: "#666",
            display: "block",
          }}
        >
          Click the × button to remove technologies from your selection
        </Typography>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive deletable chips with state management for dynamic content removal.",
      },
    },
  },
}

// Text truncation
export const TextTruncation: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Normal Text (No Truncation)
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Short" color="primary" variant="outline" />
          <Chip label="Medium length text" color="primary" variant="outline" />
          <Chip
            label="This is a very long text that will flow naturally"
            color="primary"
            variant="outline"
          />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Truncated Text (150px width)
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip
            label="Short"
            color="success"
            variant="fill"
            truncatedTextWidth={150}
          />
          <Chip
            label="Medium length text here"
            color="success"
            variant="fill"
            truncatedTextWidth={150}
          />
          <Chip
            label="This is a very long text that will be truncated with ellipsis"
            color="success"
            variant="fill"
            truncatedTextWidth={150}
          />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Very Narrow Truncation (80px width)
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip
            label="Constrained width example"
            color="warning"
            variant="outline"
            truncatedTextWidth={80}
            startDecorator={<Tag size={14} />}
          />
          <Chip
            label="Another long text example for demonstration"
            color="danger"
            variant="fill"
            truncatedTextWidth={80}
            onDelete={() => {}}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Text truncation behavior with different width constraints and ellipsis display.",
      },
    },
  },
}

// Disabled states
export const DisabledStates: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Fill Variant - Disabled
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Default" color="default" variant="fill" disabled />
          <Chip label="Primary" color="primary" variant="fill" disabled />
          <Chip label="Success" color="success" variant="fill" disabled />
          <Chip label="Warning" color="warning" variant="fill" disabled />
          <Chip label="Danger" color="danger" variant="fill" disabled />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Outline Variant - Disabled
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Default" color="default" variant="outline" disabled />
          <Chip label="Primary" color="primary" variant="outline" disabled />
          <Chip label="Success" color="success" variant="outline" disabled />
          <Chip label="Warning" color="warning" variant="outline" disabled />
          <Chip label="Danger" color="danger" variant="outline" disabled />
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          With Decorators - Disabled
        </Typography>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip
            label="With Icon"
            color="primary"
            variant="fill"
            disabled
            startDecorator={<User size={14} />}
          />
          <Chip
            label="Deletable"
            color="success"
            variant="outline"
            disabled
            onDelete={() => {}}
          />
          <Chip
            label="Both Decorators"
            color="warning"
            variant="fill"
            disabled
            startDecorator={<Star size={14} />}
            onDelete={() => {}}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Disabled states for all chip variants, colors, and decorator combinations.",
      },
    },
  },
}

// Real-world filter example
export const FilterExample: Story = {
  render: () => {
    const [selectedFilters, setSelectedFilters] = useState<string[]>([
      "featured",
      "new",
    ])

    const filterOptions = [
      {
        id: "featured",
        label: "Featured",
        color: "primary" as const,
        icon: <Star size={14} />,
      },
      {
        id: "new",
        label: "New Arrivals",
        color: "success" as const,
        icon: <Zap size={14} />,
      },
      {
        id: "sale",
        label: "On Sale",
        color: "danger" as const,
        icon: <Tag size={14} />,
      },
      {
        id: "premium",
        label: "Premium",
        color: "warning" as const,
        icon: <Award size={14} />,
      },
      {
        id: "trending",
        label: "Trending",
        color: "process" as const,
        icon: <TrendingUp size={14} />,
      },
      {
        id: "bestseller",
        label: "Best Seller",
        color: "default" as const,
        icon: <Shield size={14} />,
      },
    ]

    const toggleFilter = (filterId: string) => {
      setSelectedFilters((prev) =>
        prev.includes(filterId)
          ? prev.filter((id) => id !== filterId)
          : [...prev, filterId]
      )
    }

    const clearAllFilters = () => {
      setSelectedFilters([])
    }

    return (
      <div style={{ width: "700px" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <Typography level="h5">Product Filters</Typography>
          <Button
            size="sm"
            variant="outline"
            onClick={clearAllFilters}
            disabled={selectedFilters.length === 0}
          >
            Clear All ({selectedFilters.length})
          </Button>
        </div>

        <div
          style={{
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
            marginBottom: "20px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Available Filters
          </Typography>
          <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
            {filterOptions.map((filter) => (
              <button
                key={filter.id}
                onClick={() => toggleFilter(filter.id)}
                style={{
                  background: "none",
                  border: "none",
                  padding: 0,
                  cursor: "pointer",
                }}
              >
                <Chip
                  label={filter.label}
                  color={
                    selectedFilters.includes(filter.id)
                      ? filter.color
                      : "default"
                  }
                  variant={
                    selectedFilters.includes(filter.id) ? "fill" : "outline"
                  }
                  startDecorator={filter.icon}
                  rounded="full"
                />
              </button>
            ))}
          </div>
        </div>

        <div
          style={{
            padding: "16px",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Active Filters
          </Typography>
          {selectedFilters.length === 0 ? (
            <Typography
              level="body-2"
              style={{ color: "#666", fontStyle: "italic" }}
            >
              No filters selected. Click on the filters above to add them.
            </Typography>
          ) : (
            <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
              {selectedFilters.map((filterId) => {
                const filter = filterOptions.find((f) => f.id === filterId)
                return filter ? (
                  <Chip
                    key={filterId}
                    label={filter.label}
                    color={filter.color}
                    variant="fill"
                    startDecorator={filter.icon}
                    rounded="full"
                    onDelete={() => toggleFilter(filterId)}
                  />
                ) : null
              })}
            </div>
          )}
        </div>

        <Typography
          level="caption"
          style={{
            marginTop: "12px",
            color: "#666",
            display: "block",
          }}
        >
          Click filters to toggle selection, or use × to remove active filters
        </Typography>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world filtering interface with toggleable chips and active filter management.",
      },
    },
  },
}

// Tag management example
export const TagManagementExample: Story = {
  render: () => {
    const [inputValue, setInputValue] = useState("")
    const [tags, setTags] = useState([
      "React",
      "TypeScript",
      "Design System",
      "Component Library",
    ])

    const addTag = () => {
      if (inputValue.trim() && !tags.includes(inputValue.trim())) {
        setTags([...tags, inputValue.trim()])
        setInputValue("")
      }
    }

    const removeTag = (tagToRemove: string) => {
      setTags(tags.filter((tag) => tag !== tagToRemove))
    }

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        e.preventDefault()
        addTag()
      }
    }

    const suggestedTags = [
      "JavaScript",
      "CSS",
      "HTML",
      "Node.js",
      "Express",
      "MongoDB",
      "PostgreSQL",
      "REST API",
      "GraphQL",
      "Jest",
      "Cypress",
    ].filter((tag) => !tags.includes(tag))

    return (
      <div style={{ width: "600px" }}>
        <Typography level="h5" gutterBottom>
          Project Tags
        </Typography>

        {/* Add Tag Input */}
        <div
          style={{
            display: "flex",
            gap: "8px",
            marginBottom: "20px",
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
          }}
        >
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Add a tag..."
            style={{
              flex: 1,
              padding: "8px 12px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          />
          <Button
            size="sm"
            onClick={addTag}
            disabled={!inputValue.trim() || tags.includes(inputValue.trim())}
          >
            Add Tag
          </Button>
        </div>

        {/* Current Tags */}
        <div style={{ marginBottom: "20px" }}>
          <Typography level="h5" gutterBottom>
            Current Tags ({tags.length})
          </Typography>
          <div
            style={{
              minHeight: "40px",
              padding: "12px",
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              background: "white",
            }}
          >
            {tags.length === 0 ? (
              <Typography
                level="body-2"
                style={{
                  color: "#666",
                  fontStyle: "italic",
                  textAlign: "center",
                }}
              >
                No tags added yet
              </Typography>
            ) : (
              <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                {tags.map((tag, index) => (
                  <Chip
                    key={tag}
                    label={tag}
                    color={index % 2 === 0 ? "primary" : "success"}
                    variant="fill"
                    rounded="full"
                    onDelete={() => removeTag(tag)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Suggested Tags */}
        {suggestedTags.length > 0 && (
          <div>
            <Typography level="h5" gutterBottom>
              Suggested Tags
            </Typography>
            <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
              {suggestedTags.slice(0, 8).map((tag) => (
                <button
                  key={tag}
                  onClick={() => setTags([...tags, tag])}
                  style={{
                    background: "none",
                    border: "none",
                    padding: 0,
                    cursor: "pointer",
                  }}
                >
                  <Chip
                    label={`+ ${tag}`}
                    color="default"
                    variant="outline"
                    rounded="full"
                    startDecorator={<Bookmark size={14} />}
                  />
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete tag management system with input, deletion, and suggestions using chips.",
      },
    },
  },
}
