import { useState } from "react"
import { createTheme, DateInput, ThemeProvider } from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import { addDays, endOfMonth, startOfMonth, subDays } from "date-fns"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/DateInput",
  component: DateInput,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The DateInput component provides a date picker with calendar popup. It supports various date formats, locales, eras (AD/BD), and date range restrictions.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Label for the date input field",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the date input",
    },
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text for the input",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the date input is disabled",
    },
    error: {
      control: { type: "boolean" },
      description: "Whether the date input has an error state",
    },
    era: {
      control: { type: "select" },
      options: ["ad", "bd"],
      description: "Era format: AD (Christian Era) or BD (Buddhist Era)",
    },
    locale: {
      control: { type: "select" },
      options: ["en", "th"],
      description: "Locale for date formatting and calendar",
    },
    format: {
      control: { type: "text" },
      description: "Custom date format string",
    },
  },
} satisfies Meta<typeof DateInput>

export default meta
type Story = StoryObj<typeof DateInput>

// Basic date input
export const Basic: Story = {
  args: {
    value: null,
    onChange: () => {},
  },
  render: () => {
    const [date, setDate] = useState<Date | null>(null)

    return (
      <DateInput
        value={date}
        onChange={(date) => setDate(date)}
        label="Select Date"
        placeholder="Choose a date..."
        helperText="Click to open calendar"
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic date input with calendar popup.",
      },
    },
  },
}

// With default value
export const WithDefaultValue: Story = {
  render: () => {
    const [date, setDate] = useState<Date | null>(new Date())

    return (
      <DateInput
        value={date}
        onChange={(date) => setDate(date)}
        label="Appointment Date"
        helperText="Current selected date"
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Date input with a pre-selected date.",
      },
    },
  },
}

// States demonstration
export const States: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Normal State</h4>
        <DateInput
          value={new Date()}
          onChange={() => {}}
          label="Normal Date Input"
          helperText="Normal state"
        />
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Disabled State
        </h4>
        <DateInput
          value={new Date()}
          onChange={() => {}}
          label="Disabled Date Input"
          helperText="This date input is disabled"
          disabled
        />
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Error State</h4>
        <DateInput
          value={null}
          onChange={() => {}}
          label="Error Date Input"
          helperText="Please select a valid date"
          error
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Different states of the date input component.",
      },
    },
  },
}

// Era comparison (AD vs BD)
export const EraComparison: Story = {
  render: () => {
    const [adDate, setAdDate] = useState<Date | null>(new Date())
    const [bdDate, setBdDate] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Christian Era (AD)
          </h4>
          <DateInput
            value={adDate}
            onChange={(date) => setAdDate(date)}
            label="AD Era"
            era="ad"
            helperText="Standard Christian calendar"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Buddhist Era (BD)
          </h4>
          <DateInput
            value={bdDate}
            onChange={(date) => setBdDate(date)}
            label="BD Era"
            era="bd"
            helperText="Buddhist calendar (+543 years)"
          />
        </div>

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Note:</strong> Buddhist Era (BD) adds 543 years to the
          Christian Era (AD). For example, 2024 AD = 2567 BD.
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Comparison of AD (Christian Era) and BD (Buddhist Era) date formats.",
      },
    },
  },
}

// Locale comparison
export const LocaleComparison: Story = {
  render: () => {
    const [enDate, setEnDate] = useState<Date | null>(new Date())
    const [thDate, setThDate] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            English Locale
          </h4>
          <DateInput
            value={enDate}
            onChange={(date) => setEnDate(date)}
            label="English Date"
            locale="en"
            helperText="English month and day names"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Thai Locale</h4>
          <DateInput
            value={thDate}
            onChange={(date) => setThDate(date)}
            label="Thai Date"
            locale="th"
            helperText="Thai month and day names"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Comparison of English and Thai locale formatting.",
      },
    },
  },
}

// Date restrictions
export const DateRestrictions: Story = {
  render: () => {
    const [futureDate, setFutureDate] = useState<Date | null>(null)
    const [pastDate, setPastDate] = useState<Date | null>(null)
    const [rangeDate, setRangeDate] = useState<Date | null>(null)

    const today = new Date()
    const thirtyDaysFromNow = addDays(today, 30)
    const thirtyDaysAgo = subDays(today, 30)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Future Dates Only
          </h4>
          <DateInput
            value={futureDate}
            onChange={(date) => setFutureDate(date)}
            label="Future Date"
            minDate={today}
            helperText="Only dates from today onwards"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Past Dates Only
          </h4>
          <DateInput
            value={pastDate}
            onChange={(date) => setPastDate(date)}
            label="Past Date"
            maxDate={today}
            helperText="Only dates up to today"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Date Range (±30 days)
          </h4>
          <DateInput
            value={rangeDate}
            onChange={(date) => setRangeDate(date)}
            label="Range Date"
            minDate={thirtyDaysAgo}
            maxDate={thirtyDaysFromNow}
            helperText="Only dates within 30 days of today"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Date inputs with various restrictions using minDate and maxDate.",
      },
    },
  },
}

// Custom formats
export const CustomFormats: Story = {
  render: () => {
    const [defaultFormat, setDefaultFormat] = useState<Date | null>(new Date())
    const [customFormat1, setCustomFormat1] = useState<Date | null>(new Date())
    const [customFormat2, setCustomFormat2] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Default Format
          </h4>
          <DateInput
            value={defaultFormat}
            onChange={(date) => setDefaultFormat(date)}
            label="Default"
            helperText="Default date format"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            MM/dd/yyyy Format
          </h4>
          <DateInput
            value={customFormat1}
            onChange={(date) => setCustomFormat1(date)}
            label="US Format"
            format="MM/dd/yyyy"
            helperText="Month/Day/Year format"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            dd-MM-yyyy Format
          </h4>
          <DateInput
            value={customFormat2}
            onChange={(date) => setCustomFormat2(date)}
            label="European Format"
            format="dd-MM-yyyy"
            helperText="Day-Month-Year format"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Different date format examples using the format prop.",
      },
    },
  },
}

// Controlled example
export const Controlled: Story = {
  render: () => {
    const [selectedDate, setSelectedDate] = useState<Date | null>(new Date())

    const today = new Date()
    const tomorrow = addDays(today, 1)
    const nextWeek = addDays(today, 7)
    const nextMonth = addDays(today, 30)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <DateInput
          value={selectedDate}
          onChange={(date) => setSelectedDate(date)}
          label="Appointment Date"
          helperText={
            selectedDate
              ? `Selected: ${selectedDate.toLocaleDateString()}`
              : "No date selected"
          }
        />

        <div
          style={{ padding: "8px", background: "#f5f5f5", borderRadius: "4px" }}
        >
          <strong>Selected date:</strong>{" "}
          {selectedDate ? selectedDate.toLocaleDateString() : "None"}
        </div>

        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <button
            onClick={() => setSelectedDate(today)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Today
          </button>
          <button
            onClick={() => setSelectedDate(tomorrow)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Tomorrow
          </button>
          <button
            onClick={() => setSelectedDate(nextWeek)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Next Week
          </button>
          <button
            onClick={() => setSelectedDate(nextMonth)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Next Month
          </button>
          <button
            onClick={() => setSelectedDate(null)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Clear
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled date input with external state management and quick date selection.",
      },
    },
  },
}

// Form integration example
export const FormIntegration: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      startDate: null as Date | null,
      endDate: null as Date | null,
      birthDate: null as Date | null,
    })

    const [submitted, setSubmitted] = useState(false)

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      setSubmitted(true)
      setTimeout(() => setSubmitted(false), 3000)
    }

    const isValid = formData.startDate && formData.endDate && formData.birthDate
    const isEndDateValid =
      !formData.startDate ||
      !formData.endDate ||
      formData.endDate >= formData.startDate

    return (
      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "400px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Event Registration</h3>

        <DateInput
          value={formData.startDate}
          onChange={(date) =>
            setFormData((prev) => ({ ...prev, startDate: date }))
          }
          label="Event Start Date"
          minDate={new Date()}
          error={!formData.startDate && submitted}
          helperText={
            !formData.startDate && submitted
              ? "Start date is required"
              : "Event begins on this date"
          }
        />

        <DateInput
          value={formData.endDate}
          onChange={(date) =>
            setFormData((prev) => ({ ...prev, endDate: date }))
          }
          label="Event End Date"
          minDate={formData.startDate || new Date()}
          error={(!formData.endDate || !isEndDateValid) && submitted}
          helperText={
            !formData.endDate && submitted
              ? "End date is required"
              : !isEndDateValid
                ? "End date must be after start date"
                : "Event ends on this date"
          }
        />

        <DateInput
          value={formData.birthDate}
          onChange={(date) =>
            setFormData((prev) => ({ ...prev, birthDate: date }))
          }
          label="Date of Birth"
          maxDate={subDays(new Date(), 365 * 18)} // Must be 18+ years old
          error={!formData.birthDate && submitted}
          helperText={
            !formData.birthDate && submitted
              ? "Birth date is required"
              : "Must be 18 or older to register"
          }
        />

        <button
          type="submit"
          disabled={!isValid || !isEndDateValid}
          style={{
            padding: "12px 24px",
            background: isValid && isEndDateValid ? "#007bff" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: isValid && isEndDateValid ? "pointer" : "not-allowed",
            fontSize: "16px",
            marginTop: "8px",
          }}
        >
          Register for Event
        </button>

        {submitted && isValid && isEndDateValid && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Registration submitted successfully!
          </div>
        )}
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete form with date validation, dependencies, and age restrictions.",
      },
    },
  },
}

// Month and year selection modes
export const ViewModes: Story = {
  render: () => {
    const [dateMode, setDateMode] = useState<Date | null>(new Date())
    const [monthMode, setMonthMode] = useState<Date | null>(new Date())
    const [yearMode, setYearMode] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Date Picker (Default)
          </h4>
          <DateInput
            value={dateMode}
            onChange={(date) => setDateMode(date)}
            label="Select Date"
            helperText="Standard date picker"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Month Picker
          </h4>
          <DateInput
            value={monthMode}
            onChange={(date) => setMonthMode(date)}
            label="Select Month"
            showMonthYearPicker
            helperText="Month and year selection only"
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Year Picker</h4>
          <DateInput
            value={yearMode}
            onChange={(date) => setYearMode(date)}
            label="Select Year"
            showYearPicker
            helperText="Year selection only"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Different selection modes: date, month, and year pickers.",
      },
    },
  },
}
