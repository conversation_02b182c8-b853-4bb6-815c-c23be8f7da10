import { useMemo, useState } from "react"
import {
  createTheme,
  Float<PERSON>utton,
  ThemeProvider,
  useScrollDirection,
} from "@apollo/ui/legacy"
import {
  Bell,
  Comment,
  Download,
  Edit,
  Heart,
  Info,
  Plus,
  Search,
  Smile,
  Star,
} from "@design-systems/apollo-icons"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

import "../../app/tailwind.css"

const meta: Meta<typeof FloatButton> = {
  title: "@design-systems∕apollo-ui/Components/Inputs/FloatButton",
  component: FloatButton,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `The FloatButton component is a control button designed to float on the screen. It features an icon and label that can be expanded/collapsed, commonly used for primary actions or quick access features.

## Features
- **Collapsible**: Can be expanded/collapsed to show/hide the label
- **Icon Positioning**: Icon can be positioned at start or end
- **Color Variants**: Supports primary and danger color variants
- **State Management**: Handles expanded, collapsed, disabled states
- **Event Handling**: Full click and interaction support
- **Accessibility**: Full keyboard and screen reader support

## Use Cases
- Floating action buttons (FAB)
- Help/support buttons
- Quick action shortcuts
- Context-sensitive actions`,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    icon: {
      description: "The icon to be displayed inside the button",
      control: false,
    },
    label: {
      description: "The label to be displayed inside the button",
      control: "text",
    },
    isExpanded: {
      description: "Whether the button is expanded to show the label",
      control: "boolean",
    },
    iconSide: {
      description: "The side of the icon relative to the label",
      control: "select",
      options: ["start", "end"],
    },
    color: {
      description: "The color variant of the button",
      control: "select",
      options: ["primary", "danger"],
    },
    variant: {
      description: "The variant style of the button",
      control: "select",
      options: ["solid", "outline", "plain"],
    },
    disabled: {
      description: "Whether the button is disabled",
      control: "boolean",
    },
    onClick: {
      description: "Click event handler",
      action: "clicked",
    },
  },
  decorators: [
    (Story) => {
      const theme = createTheme()
      return (
        <ThemeProvider theme={theme}>
          <Story />
        </ThemeProvider>
      )
    },
  ],
}

export default meta
type Story = StoryObj<typeof FloatButton>

export const Default: Story = {
  args: {
    icon: <Smile />,
    label: "Hello",
    isExpanded: false,
  },
}

export const Expanded: Story = {
  args: {
    icon: <Heart />,
    label: "Favorite",
    isExpanded: true,
  },
  parameters: {
    docs: {
      description: {
        story: "FloatButton in expanded state showing both icon and label.",
      },
    },
  },
}

export const IconPositions: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <FloatButton
        icon={<Download />}
        label="Download"
        isExpanded
        iconSide="start"
      />
      <FloatButton
        icon={<Download />}
        label="Download"
        isExpanded
        iconSide="end"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "FloatButton with icon positioned at start or end relative to the label.",
      },
    },
  },
}

export const ColorVariants: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <FloatButton icon={<Star />} label="Primary" isExpanded color="primary" />
      <FloatButton icon={<Star />} label="Danger" isExpanded color="danger" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "FloatButton with different color variants.",
      },
    },
  },
}

export const ButtonVariants: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <FloatButton icon={<Edit />} label="Solid" isExpanded variant="solid" />
      <FloatButton
        icon={<Edit />}
        label="Outline"
        isExpanded
        variant="outline"
      />
      <FloatButton icon={<Edit />} label="Plain" isExpanded variant="plain" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "FloatButton with different button variants.",
      },
    },
  },
}

export const DisabledStates: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <FloatButton
        icon={<Comment />}
        label="Collapsed"
        isExpanded={false}
        disabled
      />
      <FloatButton
        icon={<Comment />}
        label="Expanded"
        isExpanded={true}
        disabled
      />
      <FloatButton
        icon={<Comment />}
        label="Danger Disabled"
        isExpanded={true}
        color="danger"
        disabled
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "FloatButton in disabled state with various configurations.",
      },
    },
  },
}

export const ExpandCollapseBehavior: Story = {
  render: () => {
    const [isExpanded, setIsExpanded] = useState(false)

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          alignItems: "center",
        }}
      >
        <div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            style={{
              padding: "8px 16px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              background: "#f5f5f5",
              cursor: "pointer",
            }}
          >
            Toggle Expansion
          </button>
        </div>
        <FloatButton
          icon={<Bell />}
          label="Notifications"
          isExpanded={isExpanded}
          onClick={() => alert("Notifications clicked!")}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive example showing expand/collapse behavior of FloatButton.",
      },
    },
  },
}

export const ScrollBasedExpansion: Story = {
  render: () => {
    const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(
      null
    )
    const { scrollDirection } = useScrollDirection(containerRef)
    const isExpanded = useMemo(
      () => (scrollDirection ? ["up", "none"].includes(scrollDirection) : true),
      [scrollDirection]
    )

    return (
      <div style={{ position: "relative", width: "320px", height: "400px" }}>
        <div
          style={{
            width: "100%",
            height: "100%",
            backgroundColor: "#f0f0f0",
            overflow: "auto",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
          ref={setContainerRef}
        >
          <div
            style={{
              width: "100%",
              height: "800px",
              background: "linear-gradient(180deg, #e3f2fd 0%, #ffcdd2 100%)",
              padding: "20px",
              boxSizing: "border-box",
            }}
          >
            <h3>Scroll to see FloatButton behavior</h3>
            <p>
              The FloatButton will expand when scrolling up or not scrolling,
              and collapse when scrolling down.
            </p>
            <div style={{ marginTop: "400px" }}>
              <p>Keep scrolling to see the effect...</p>
            </div>
          </div>
        </div>
        <div style={{ position: "absolute", bottom: "16px", right: "16px" }}>
          <FloatButton
            icon={<Info />}
            label="Help"
            isExpanded={isExpanded}
            onClick={() => alert("Help clicked!")}
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "FloatButton that expands/collapses based on scroll direction using the useScrollDirection hook.",
      },
    },
  },
}

export const InteractivePlayground: Story = {
  render: () => {
    const [config, setConfig] = useState({
      isExpanded: true,
      iconSide: "start" as "start" | "end",
      color: "primary" as "primary" | "danger",
      variant: "solid" as "solid" | "outline" | "plain",
      disabled: false,
      label: "Action Button",
    })

    const handleConfigChange = (key: string, value: any) => {
      setConfig((prev) => ({ ...prev, [key]: value }))
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "16px",
            padding: "16px",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#f9f9f9",
          }}
        >
          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>Label:</span>
            <input
              type="text"
              value={config.label}
              onChange={(e) => handleConfigChange("label", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            />
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Is Expanded:
            </span>
            <input
              type="checkbox"
              checked={config.isExpanded}
              onChange={(e) =>
                handleConfigChange("isExpanded", e.target.checked)
              }
              style={{ width: "18px", height: "18px" }}
            />
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Icon Side:
            </span>
            <select
              value={config.iconSide}
              onChange={(e) => handleConfigChange("iconSide", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="start">Start</option>
              <option value="end">End</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>Color:</span>
            <select
              value={config.color}
              onChange={(e) => handleConfigChange("color", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="primary">Primary</option>
              <option value="danger">Danger</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Variant:
            </span>
            <select
              value={config.variant}
              onChange={(e) => handleConfigChange("variant", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="solid">Solid</option>
              <option value="outline">Outline</option>
              <option value="plain">Plain</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Disabled:
            </span>
            <input
              type="checkbox"
              checked={config.disabled}
              onChange={(e) => handleConfigChange("disabled", e.target.checked)}
              style={{ width: "18px", height: "18px" }}
            />
          </label>
        </div>

        <div
          style={{
            padding: "32px",
            border: "2px dashed #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#fafafa",
          }}
        >
          <FloatButton
            icon={<Plus />}
            label={config.label}
            isExpanded={config.isExpanded}
            iconSide={config.iconSide}
            color={config.color}
            variant={config.variant}
            disabled={config.disabled}
            onClick={() =>
              !config.disabled && alert(`${config.label} clicked!`)
            }
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive playground to experiment with all FloatButton props and see real-time changes.",
      },
    },
  },
}
