import React from "react"
import { createTheme, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@apollo/ui/legacy"
import {
  ArrowLeft,
  ArrowRight,
  Bell,
  Check,
  Close,
  DeleteOutlined,
  Download,
  Edit,
  Heart,
  Home,
  Info,
  Menu,
  More,
  Plus,
  Search,
  Setting,
  Smile,
  Star,
  Upload,
} from "@design-systems/apollo-icons"
import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"

import "../../app/tailwind.css"

const meta: Meta<typeof IconButton> = {
  title: "@design-systems∕apollo-ui/Components/Inputs/IconButton",
  component: IconButton,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `The IconButton component is a compact, customizable button primarily used for actions involving icons. It supports various states, sizes, and icon configurations, making it ideal for both primary and secondary actions.

## Features
- **Size Variants**: Small, medium, and large sizes
- **Color Variants**: Primary and danger color schemes
- **Button Variants**: Solid, outline, and plain styles
- **State Management**: Normal, disabled, loading states
- **Link Support**: Can function as both button and anchor
- **Icon Integration**: Seamless integration with Apollo Icons
- **Accessibility**: Full keyboard navigation and screen reader support

## Use Cases
- Toolbar actions
- Form controls
- Navigation elements
- Quick actions
- Menu items
- Status indicators`,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      description: "The size of the icon button",
      control: "select",
      options: ["small", "medium", "large"],
    },
    color: {
      description: "The color variant of the button",
      control: "select",
      options: ["primary", "danger"],
    },
    variant: {
      description: "The visual variant of the button",
      control: "select",
      options: ["solid", "outline", "plain"],
    },
    disabled: {
      description: "Whether the button is disabled",
      control: "boolean",
    },
    children: {
      description: "The icon element to display",
      control: false,
    },
    onClick: {
      description: "Click event handler",
      action: "clicked",
    },
    href: {
      description: "URL to navigate to (makes it a link)",
      control: "text",
    },
  },
  decorators: [
    (Story) => {
      const theme = createTheme()
      return (
        <ThemeProvider theme={theme}>
          <Story />
        </ThemeProvider>
      )
    },
  ],
}

export default meta
type Story = StoryObj<typeof IconButton>

export const Default: Story = {
  args: {
    children: <Heart />,
    size: "large",
  },
}

export const Sizes: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <IconButton size="small">
        <Heart />
      </IconButton>
      <IconButton size="medium">
        <Heart />
      </IconButton>
      <IconButton size="large">
        <Heart />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "IconButton in different sizes: small (24px), medium (32px), and large (40px).",
      },
    },
  },
}

export const ColorVariants: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <IconButton color="primary">
        <Star />
      </IconButton>
      <IconButton color="danger">
        <Star />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "IconButton with different color variants: primary (default) and danger.",
      },
    },
  },
}

export const ButtonVariants: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <IconButton variant="solid" color="primary">
        <Edit />
      </IconButton>
      <IconButton variant="outline" color="primary">
        <Edit />
      </IconButton>
      <IconButton variant="plain" color="primary">
        <Edit />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "IconButton with different visual variants: solid, outline, and plain.",
      },
    },
  },
}

export const DisabledStates: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <IconButton disabled size="small">
        <Download />
      </IconButton>
      <IconButton disabled size="medium">
        <Download />
      </IconButton>
      <IconButton disabled size="large">
        <Download />
      </IconButton>
      <IconButton disabled color="danger">
        <DeleteOutlined />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "IconButton in disabled state across different sizes and colors.",
      },
    },
  },
}

export const AsLinkButtons: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <IconButton href="https://example.com" rel="noopener noreferrer">
        <Upload />
      </IconButton>
      <IconButton href="/dashboard" variant="outline">
        <Home />
      </IconButton>
      <IconButton href="/settings" variant="plain" color="primary">
        <Setting />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "IconButton functioning as links with href attribute. Opens external links in new tab.",
      },
    },
  },
}

export const CommonIcons: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(60px, 1fr))",
        gap: "16px",
        maxWidth: "400px",
      }}
    >
      <IconButton>
        <Plus />
      </IconButton>
      <IconButton>
        <Close />
      </IconButton>
      <IconButton>
        <Edit />
      </IconButton>
      <IconButton>
        <DeleteOutlined />
      </IconButton>
      <IconButton>
        <Search />
      </IconButton>
      <IconButton>
        <Bell />
      </IconButton>
      <IconButton>
        <Info />
      </IconButton>
      <IconButton>
        <Star />
      </IconButton>
      <IconButton>
        <Download />
      </IconButton>
      <IconButton>
        <Upload />
      </IconButton>
      <IconButton>
        <Setting />
      </IconButton>
      <IconButton>
        <Menu />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "IconButton showcasing commonly used icons in applications.",
      },
    },
  },
}

export const ToolbarExample: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "8px 12px",
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        backgroundColor: "#f9f9f9",
      }}
    >
      <IconButton size="medium" variant="plain">
        <Menu />
      </IconButton>
      <div
        style={{ width: "1px", height: "24px", backgroundColor: "#e0e0e0" }}
      />
      <IconButton size="medium" variant="plain">
        <ArrowLeft />
      </IconButton>
      <IconButton size="medium" variant="plain">
        <ArrowRight />
      </IconButton>
      <div
        style={{ width: "1px", height: "24px", backgroundColor: "#e0e0e0" }}
      />
      <IconButton size="medium" variant="plain">
        <Edit />
      </IconButton>
      <IconButton size="medium" variant="plain" color="danger">
        <DeleteOutlined />
      </IconButton>
      <div style={{ flex: 1 }} />
      <IconButton size="medium" variant="plain">
        <Search />
      </IconButton>
      <IconButton size="medium" variant="plain">
        <More />
      </IconButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Real-world example of IconButtons used in a toolbar layout with separators and navigation actions.",
      },
    },
  },
}

export const InteractivePlayground: Story = {
  render: () => {
    const [config, setConfig] = React.useState({
      size: "large" as "small" | "medium" | "large",
      color: "primary" as "primary" | "danger",
      variant: "solid" as "solid" | "outline" | "plain",
      disabled: false,
      icon: "heart" as
        | "heart"
        | "star"
        | "edit"
        | "download"
        | "bell"
        | "setting",
      asLink: false,
    })

    const handleConfigChange = (key: string, value: any) => {
      setConfig((prev) => ({ ...prev, [key]: value }))
    }

    const iconMap = {
      heart: <Heart />,
      star: <Star />,
      edit: <Edit />,
      download: <Download />,
      bell: <Bell />,
      setting: <Setting />,
    }

    const buttonProps = {
      size: config.size,
      color: config.color,
      variant: config.variant,
      disabled: config.disabled,
      children: iconMap[config.icon],
      ...(config.asLink
        ? { href: "#example-link" }
        : { onClick: () => !config.disabled && alert("Icon button clicked!") }),
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "16px",
            padding: "16px",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#f9f9f9",
          }}
        >
          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>Size:</span>
            <select
              value={config.size}
              onChange={(e) => handleConfigChange("size", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>Color:</span>
            <select
              value={config.color}
              onChange={(e) => handleConfigChange("color", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="primary">Primary</option>
              <option value="danger">Danger</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Variant:
            </span>
            <select
              value={config.variant}
              onChange={(e) => handleConfigChange("variant", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="solid">Solid</option>
              <option value="outline">Outline</option>
              <option value="plain">Plain</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>Icon:</span>
            <select
              value={config.icon}
              onChange={(e) => handleConfigChange("icon", e.target.value)}
              style={{
                padding: "4px 8px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            >
              <option value="heart">Heart</option>
              <option value="star">Star</option>
              <option value="edit">Edit</option>
              <option value="download">Download</option>
              <option value="bell">Bell</option>
              <option value="setting">Setting</option>
            </select>
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Disabled:
            </span>
            <input
              type="checkbox"
              checked={config.disabled}
              onChange={(e) => handleConfigChange("disabled", e.target.checked)}
              style={{ width: "18px", height: "18px" }}
            />
          </label>

          <label
            style={{ display: "flex", flexDirection: "column", gap: "4px" }}
          >
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              As Link:
            </span>
            <input
              type="checkbox"
              checked={config.asLink}
              onChange={(e) => handleConfigChange("asLink", e.target.checked)}
              style={{ width: "18px", height: "18px" }}
            />
          </label>
        </div>

        <div
          style={{
            padding: "32px",
            border: "2px dashed #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#fafafa",
          }}
        >
          <IconButton {...buttonProps} />
        </div>

        <div
          style={{
            fontSize: "12px",
            color: "#666",
            textAlign: "center",
            maxWidth: "300px",
          }}
        >
          {config.asLink
            ? 'Functioning as a link (href="#example-link")'
            : "Functioning as a button with click handler"}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive playground to experiment with all IconButton props and see real-time changes.",
      },
    },
  },
}
