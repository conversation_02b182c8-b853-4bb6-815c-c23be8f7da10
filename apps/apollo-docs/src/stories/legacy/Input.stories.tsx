import { createTheme, Input, ThemeProvider } from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Input",
  component: Input,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A versatile input component for text entry with various styling options and validation states.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["outline"],
      description: "The visual style variant of the input",
    },
    size: {
      control: { type: "select" },
      options: ["small", "medium"],
      description: "The size of the input",
    },
    color: {
      control: { type: "select" },
      options: ["primary", "danger"],
      description: "The color theme of the input",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the input is disabled",
    },
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text for the input",
    },
    value: {
      control: { type: "text" },
      description: "The value of the input",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the input",
    },
    label: {
      control: { type: "text" },
      description: "Label text for the input",
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the input is required",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the input takes the full width of its container",
    },
  },
} satisfies Meta<typeof Input>

export default meta
type Story = StoryObj<typeof meta>

// Default story
export const Default: Story = {
  args: {
    placeholder: "Enter text...",
  },
}

// With label
export const WithLabel: Story = {
  args: {
    label: "Username",
    placeholder: "Enter your username",
  },
}

// Required field
export const Required: Story = {
  args: {
    label: "Email Address",
    placeholder: "Enter your email",
    required: true,
  },
}

// With helper text
export const WithHelperText: Story = {
  args: {
    label: "Password",
    placeholder: "Enter your password",
    helperText: "Password must be at least 8 characters long",
    type: "password",
  },
}

// Error state (using danger color)
export const Error: Story = {
  args: {
    label: "Email Address",
    placeholder: "Enter your email",
    value: "invalid-email",
    color: "danger",
    helperText: "Please enter a valid email address",
  },
}

// Disabled state
export const Disabled: Story = {
  args: {
    label: "Disabled Input",
    placeholder: "This input is disabled",
    disabled: true,
  },
}

// Different sizes
export const Small: Story = {
  args: {
    label: "Small Input",
    placeholder: "Small size input",
    size: "small",
  },
}

export const Medium: Story = {
  args: {
    label: "Medium Input",
    placeholder: "Medium size input",
    size: "medium",
  },
}

export const Large: Story = {
  args: {
    label: "Medium Input (Legacy only has small/medium)",
    placeholder: "Medium size input",
    size: "medium",
  },
}

export const OutlineVariant: Story = {
  args: {
    label: "Outline Variant",
    placeholder: "Outline style input",
    variant: "outline",
  },
}

// Primary color
export const PrimaryColor: Story = {
  args: {
    label: "Primary Color",
    placeholder: "Primary color input",
    color: "primary",
  },
}

// Danger color
export const DangerColor: Story = {
  args: {
    label: "Danger Color",
    placeholder: "Danger color input",
    color: "danger",
  },
}

// Interactive example
export const Interactive: Story = {
  args: {
    label: "Interactive Input",
    placeholder: "Try typing here...",
    helperText: "This input responds to your typing",
  },
  play: async ({ canvasElement }) => {
    // This would be used for interaction testing
    // Can be expanded with @storybook/testing-library
  },
}
