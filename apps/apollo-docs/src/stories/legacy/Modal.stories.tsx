import { useState } from "react"
import {
  <PERSON><PERSON>,
  create<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Feedback/Modal",
  component: Modal,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Modal displays content in a dialog overlay. It supports various sizes, custom headers/footers, action buttons, and configurable close behavior for different use cases.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    open: {
      control: { type: "boolean" },
      description: "Whether the modal is open",
    },
    size: {
      control: { type: "select" },
      options: ["default", "full"],
      description: "Modal size variant",
    },
    header: {
      control: { type: "text" },
      description: "Modal header content",
    },
    children: {
      control: { type: "text" },
      description: "Modal body content",
    },
    footer: {
      control: { type: "text" },
      description: "Custom footer content",
    },
    okButtonText: {
      control: { type: "text" },
      description: "OK button text",
    },
    cancelButtonText: {
      control: { type: "text" },
      description: "Cancel button text",
    },
    deleteButtonText: {
      control: { type: "text" },
      description: "Delete button text",
    },
    hideCloseIcon: {
      control: { type: "boolean" },
      description: "Hide the close icon",
    },
    closeAfterPressEsc: {
      control: { type: "boolean" },
      description: "Close modal when escape key is pressed",
    },
    closeAfterClickBackdrop: {
      control: { type: "boolean" },
      description: "Close modal when backdrop is clicked",
    },
    scrollableContent: {
      control: { type: "boolean" },
      description: "Enable scrollable content",
    },
  },
} satisfies Meta<typeof Modal>

export default meta
type Story = StoryObj<typeof Modal>

// Basic modal
export const Basic: Story = {
  render: () => {
    const [open, setOpen] = useState(false)

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Basic Modal</Button>
        <Modal open={open} onClose={() => setOpen(false)} header="Basic Modal">
          <Typography>This is a basic modal with simple content.</Typography>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic modal with header and simple content.",
      },
    },
  },
}

// Modal with action buttons
export const WithActionButtons: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    const [result, setResult] = useState<string>("")

    const handleOk = () => {
      setResult("OK clicked!")
      setOpen(false)
    }

    const handleCancel = () => {
      setResult("Cancel clicked!")
      setOpen(false)
    }

    return (
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            alignItems: "center",
          }}
        >
          <Button onClick={() => setOpen(true)}>Open Modal with Actions</Button>
          {result && (
            <div
              style={{
                padding: "8px 16px",
                background: "#e8f5e8",
                borderRadius: "4px",
                color: "#2e7d32",
              }}
            >
              {result}
            </div>
          )}
        </div>

        <Modal
          open={open}
          onClose={() => setOpen(false)}
          header="Confirm Action"
          okButtonText="Confirm"
          cancelButtonText="Cancel"
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Typography>
            Are you sure you want to proceed with this action? This cannot be
            undone.
          </Typography>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Modal with OK and Cancel action buttons.",
      },
    },
  },
}

// Delete confirmation modal
export const DeleteConfirmation: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    const [deleted, setDeleted] = useState(false)

    const handleDelete = () => {
      setDeleted(true)
      setOpen(false)
      setTimeout(() => setDeleted(false), 3000)
    }

    return (
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            alignItems: "center",
          }}
        >
          <Button color="danger" onClick={() => setOpen(true)}>
            Delete Item
          </Button>
          {deleted && (
            <div
              style={{
                padding: "8px 16px",
                background: "#ffebee",
                borderRadius: "4px",
                color: "#c62828",
              }}
            >
              Item deleted successfully!
            </div>
          )}
        </div>

        <Modal
          open={open}
          onClose={() => setOpen(false)}
          header="Delete Confirmation"
          icon={<span style={{ fontSize: "24px" }}>🗑️</span>}
          deleteButtonText="Delete"
          cancelButtonText="Cancel"
          onDelete={handleDelete}
          onCancel={() => setOpen(false)}
        >
          <Typography>
            <strong>Are you sure you want to delete this item?</strong>
            <br />
            <br />
            This action cannot be undone. All data associated with this item
            will be permanently removed.
          </Typography>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Delete confirmation modal with warning styling and delete button.",
      },
    },
  },
}

// Modal with custom header and footer
export const CustomHeaderFooter: Story = {
  render: () => {
    const [open, setOpen] = useState(false)

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Custom Modal</Button>
        <Modal
          open={open}
          onClose={() => setOpen(false)}
          header={
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <span style={{ fontSize: "24px" }}>⚙️</span>
              <div>
                <Typography level="h5" style={{ margin: 0 }}>
                  Settings
                </Typography>
                <Typography
                  level="caption"
                  style={{ margin: 0, color: "#666" }}
                >
                  Configure your preferences
                </Typography>
              </div>
            </div>
          }
          footer={
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
                padding: "0 24px 16px 24px",
              }}
            >
              <Typography level="caption" style={{ color: "#666" }}>
                Changes are saved automatically
              </Typography>
              <div style={{ display: "flex", gap: "8px" }}>
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Close
                </Button>
                <Button onClick={() => setOpen(false)}>Apply</Button>
              </div>
            </div>
          }
        >
          <div
            style={{
              padding: "0 24px",
              display: "flex",
              flexDirection: "column",
              gap: "16px",
            }}
          >
            <div>
              <Typography level="body-1" style={{ marginBottom: "8px" }}>
                <strong>Notifications</strong>
              </Typography>
              <label
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <input type="checkbox" defaultChecked />
                Email notifications
              </label>
              <label
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <input type="checkbox" />
                Push notifications
              </label>
            </div>

            <div>
              <Typography level="body-1" style={{ marginBottom: "8px" }}>
                <strong>Privacy</strong>
              </Typography>
              <label
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <input type="checkbox" defaultChecked />
                Public profile
              </label>
              <label
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <input type="checkbox" defaultChecked />
                Show online status
              </label>
            </div>
          </div>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Modal with custom header containing icon and subtitle, and custom footer with additional info.",
      },
    },
  },
}

// Full screen modal
export const FullScreen: Story = {
  render: () => {
    const [open, setOpen] = useState(false)

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Full Screen Modal</Button>
        <Modal
          open={open}
          onClose={() => setOpen(false)}
          size="full"
          header="Full Screen Modal"
          okButtonText="Save"
          cancelButtonText="Cancel"
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        >
          <div style={{ padding: "0 24px", height: "400px" }}>
            <Typography level="h5" style={{ marginBottom: "16px" }}>
              Document Editor
            </Typography>
            <textarea
              style={{
                width: "100%",
                height: "300px",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                resize: "none",
                fontFamily: "monospace",
              }}
              placeholder="Write your document content here..."
              defaultValue="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
            />
          </div>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Full screen modal that takes up the entire viewport.",
      },
    },
  },
}

// Scrollable content modal
export const ScrollableContent: Story = {
  render: () => {
    const [open, setOpen] = useState(false)

    const longContent = Array.from({ length: 20 }, (_, i) => (
      <div key={i} style={{ marginBottom: "16px" }}>
        <Typography level="h5">Section {i + 1}</Typography>
        <Typography>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
          minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in
          reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur.
        </Typography>
      </div>
    ))

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Scrollable Modal</Button>
        <Modal
          open={open}
          onClose={() => setOpen(false)}
          header="Long Content Modal"
          scrollableContent
          okButtonText="Got it"
          onOk={() => setOpen(false)}
        >
          <div style={{ padding: "0 24px" }}>{longContent}</div>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Modal with scrollable content when content exceeds viewport height.",
      },
    },
  },
}

// Modal without close options
export const NoCloseOptions: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    const [progress, setProgress] = useState(0)

    const startProcess = () => {
      setOpen(true)
      setProgress(0)

      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            setTimeout(() => setOpen(false), 1000)
            return 100
          }
          return prev + 10
        })
      }, 300)
    }

    return (
      <>
        <Button onClick={startProcess}>Start Process</Button>
        <Modal
          open={open}
          onClose={() => {}} // No close action
          header="Processing..."
          hideCloseIcon
          closeAfterPressEsc={false}
          closeAfterClickBackdrop={false}
        >
          <div style={{ padding: "0 24px", textAlign: "center" }}>
            <Typography style={{ marginBottom: "16px" }}>
              Please wait while we process your request...
            </Typography>

            <div
              style={{
                width: "100%",
                height: "8px",
                background: "#e0e0e0",
                borderRadius: "4px",
                overflow: "hidden",
                marginBottom: "8px",
              }}
            >
              <div
                style={{
                  width: `${progress}%`,
                  height: "100%",
                  background: "#007bff",
                  transition: "width 0.3s ease",
                }}
              />
            </div>

            <Typography level="caption" style={{ color: "#666" }}>
              {progress}% complete
            </Typography>
          </div>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Modal that cannot be closed by user - useful for processing states.",
      },
    },
  },
}

// Modal sizes and configurations
export const ModalVariants: Story = {
  render: () => {
    const [activeModal, setActiveModal] = useState<string | null>(null)

    const modals = [
      { id: "info", title: "Information", type: "info" },
      { id: "warning", title: "Warning", type: "warning" },
      { id: "error", title: "Error", type: "error" },
      { id: "success", title: "Success", type: "success" },
    ]

    return (
      <>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "12px",
            width: "300px",
          }}
        >
          {modals.map((modal) => (
            <Button
              key={modal.id}
              variant="outline"
              onClick={() => setActiveModal(modal.id)}
            >
              {modal.title}
            </Button>
          ))}
        </div>

        {/* Info Modal */}
        <Modal
          open={activeModal === "info"}
          onClose={() => setActiveModal(null)}
          header="Information"
          icon={<span style={{ fontSize: "24px" }}>ℹ️</span>}
          okButtonText="Understood"
          onOk={() => setActiveModal(null)}
        >
          <Typography>
            This is an informational message to help you understand something
            important.
          </Typography>
        </Modal>

        {/* Warning Modal */}
        <Modal
          open={activeModal === "warning"}
          onClose={() => setActiveModal(null)}
          header="Warning"
          icon={<span style={{ fontSize: "24px" }}>⚠️</span>}
          okButtonText="Proceed"
          cancelButtonText="Cancel"
          onOk={() => setActiveModal(null)}
          onCancel={() => setActiveModal(null)}
        >
          <Typography>
            <strong>Please be careful!</strong> This action may have unintended
            consequences.
          </Typography>
        </Modal>

        {/* Error Modal */}
        <Modal
          open={activeModal === "error"}
          onClose={() => setActiveModal(null)}
          header="Error"
          icon={<span style={{ fontSize: "24px" }}>❌</span>}
          okButtonText="Try Again"
          cancelButtonText="Cancel"
          onOk={() => setActiveModal(null)}
          onCancel={() => setActiveModal(null)}
        >
          <Typography>
            <strong>Something went wrong.</strong> Please check your input and
            try again.
          </Typography>
        </Modal>

        {/* Success Modal */}
        <Modal
          open={activeModal === "success"}
          onClose={() => setActiveModal(null)}
          header="Success"
          icon={<span style={{ fontSize: "24px" }}>✅</span>}
          okButtonText="Continue"
          onOk={() => setActiveModal(null)}
        >
          <Typography>
            <strong>Operation completed successfully!</strong> Your changes have
            been saved.
          </Typography>
        </Modal>
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Different modal variants for various message types: info, warning, error, and success.",
      },
    },
  },
}
