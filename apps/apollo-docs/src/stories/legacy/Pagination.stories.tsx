import { useState } from "react"
import {
  But<PERSON>,
  createTheme,
  Pa<PERSON><PERSON>,
  ThemeProvider,
  Typography,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/Pagination",
  component: Pagination,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Pagination enables the user to select a specific page from a range of pages. It provides navigation for content that is spread across multiple pages.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    count: {
      control: { type: "number", min: 1, max: 100 },
      description: "Total number of pages",
    },
    page: {
      control: { type: "number", min: 1 },
      description: "Current active page (controlled)",
    },
    defaultPage: {
      control: { type: "number", min: 1 },
      description: "Default page for uncontrolled component",
    },
    siblingCount: {
      control: { type: "number", min: 0, max: 5 },
      description: "Number of sibling pages around current page",
    },
    boundaryCount: {
      control: { type: "number", min: 1, max: 5 },
      description: "Number of boundary pages at start and end",
    },
    minimumVisibleCount: {
      control: { type: "number", min: 5, max: 20 },
      description: "Minimum pages to show before collapsing",
    },
    minimumEdgeRange: {
      control: { type: "number", min: 3, max: 10 },
      description: "Minimum items from start/end when current page in range",
    },
    showPrevPageButton: {
      control: { type: "boolean" },
      description: "Show previous page button",
    },
    showNextPageButtonButton: {
      control: { type: "boolean" },
      description: "Show next page button",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disable entire pagination",
    },
    disabledPrevPageButton: {
      control: { type: "boolean" },
      description: "Disable previous page button",
    },
    disabledNextPageButton: {
      control: { type: "boolean" },
      description: "Disable next page button",
    },
  },
} satisfies Meta<typeof Pagination>

export default meta
type Story = StoryObj<typeof meta>

// Basic pagination
export const Basic: Story = {
  args: {
    count: 10,
    defaultPage: 1,
  },
  parameters: {
    docs: {
      description: {
        story: "Basic pagination with 10 pages and default settings.",
      },
    },
  },
}

// Controlled pagination
export const Controlled: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalPages = 15

    const handlePageChange = (
      event: React.MouseEvent<HTMLButtonElement>,
      page: number
    ) => {
      setCurrentPage(page)
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
            textAlign: "center",
            minWidth: "300px",
          }}
        >
          <Typography level="h5" gutterBottom>
            Search Results
          </Typography>
          <Typography level="body-2" style={{ color: "#666" }}>
            Showing page {currentPage} of {totalPages} (
            {(currentPage - 1) * 20 + 1}-
            {Math.min(currentPage * 20, totalPages * 20)} of {totalPages * 20}{" "}
            results)
          </Typography>
        </div>

        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={handlePageChange}
          siblingCount={2}
          boundaryCount={2}
        />

        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <Button
            size="sm"
            variant="outline"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setCurrentPage(Math.ceil(totalPages / 2))}
          >
            Middle
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            Last
          </Button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled pagination with external state management and navigation helpers.",
      },
    },
  },
}

// Different sizes and configurations
export const DifferentConfigurations: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "32px",
        width: "800px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Small Dataset (5 pages)
        </Typography>
        <Pagination count={5} defaultPage={3} />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Medium Dataset (25 pages, current: 12)
        </Typography>
        <Pagination
          count={25}
          defaultPage={12}
          siblingCount={1}
          boundaryCount={2}
        />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Large Dataset (100 pages, current: 50)
        </Typography>
        <Pagination
          count={100}
          defaultPage={50}
          siblingCount={2}
          boundaryCount={1}
          minimumEdgeRange={8}
        />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Extended Siblings (count: 50, siblings: 3)
        </Typography>
        <Pagination
          count={50}
          defaultPage={25}
          siblingCount={3}
          boundaryCount={2}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different pagination configurations for various dataset sizes and display preferences.",
      },
    },
  },
}

// Button visibility options
export const ButtonVisibility: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "32px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Default (Both Buttons)
        </Typography>
        <Pagination
          count={10}
          defaultPage={5}
          showPrevPageButton={true}
          showNextPageButtonButton={true}
        />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          No Previous Button
        </Typography>
        <Pagination
          count={10}
          defaultPage={5}
          showPrevPageButton={false}
          showNextPageButtonButton={true}
        />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          No Next Button
        </Typography>
        <Pagination
          count={10}
          defaultPage={5}
          showPrevPageButton={true}
          showNextPageButtonButton={false}
        />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          No Navigation Buttons
        </Typography>
        <Pagination
          count={10}
          defaultPage={5}
          showPrevPageButton={false}
          showNextPageButtonButton={false}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different button visibility configurations for customized navigation experience.",
      },
    },
  },
}

// Disabled states
export const DisabledStates: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "32px",
        width: "600px",
      }}
    >
      <div>
        <Typography level="h5" gutterBottom>
          Fully Disabled
        </Typography>
        <Pagination count={10} defaultPage={5} disabled={true} />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Disabled Previous Button
        </Typography>
        <Pagination count={10} defaultPage={5} disabledPrevPageButton={true} />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Disabled Next Button
        </Typography>
        <Pagination count={10} defaultPage={5} disabledNextPageButton={true} />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          At First Page (Previous Auto-disabled)
        </Typography>
        <Pagination count={10} defaultPage={1} />
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          At Last Page (Next Auto-disabled)
        </Typography>
        <Pagination count={10} defaultPage={10} />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different disabled states including manual disable and automatic edge cases.",
      },
    },
  },
}

// Real-world table example
export const TablePaginationExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(10)
    const totalItems = 247
    const totalPages = Math.ceil(totalItems / itemsPerPage)

    const handlePageChange = (
      event: React.MouseEvent<HTMLButtonElement>,
      page: number
    ) => {
      setCurrentPage(page)
    }

    const handleItemsPerPageChange = (newItemsPerPage: number) => {
      setItemsPerPage(newItemsPerPage)
      setCurrentPage(1) // Reset to first page when changing items per page
    }

    const startItem = (currentPage - 1) * itemsPerPage + 1
    const endItem = Math.min(currentPage * itemsPerPage, totalItems)

    // Generate mock data for current page
    const generateRowData = (index: number) => ({
      id: startItem + index - 1,
      name: `User ${startItem + index - 1}`,
      email: `user${startItem + index - 1}@example.com`,
      status: Math.random() > 0.5 ? "Active" : "Inactive",
    })

    const currentPageData = Array.from(
      { length: Math.min(itemsPerPage, totalItems - startItem + 1) },
      (_, index) => generateRowData(index + 1)
    )

    return (
      <div style={{ width: "800px" }}>
        <Typography level="h4" gutterBottom>
          User Management
        </Typography>

        {/* Table */}
        <div
          style={{
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            overflow: "hidden",
            marginBottom: "20px",
          }}
        >
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead style={{ background: "#f8f9fa" }}>
              <tr>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                  }}
                >
                  ID
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                  }}
                >
                  Name
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                  }}
                >
                  Email
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                  }}
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody>
              {currentPageData.map((row, index) => (
                <tr
                  key={row.id}
                  style={{
                    borderBottom:
                      index < currentPageData.length - 1
                        ? "1px solid #f0f0f0"
                        : "none",
                  }}
                >
                  <td style={{ padding: "12px" }}>{row.id}</td>
                  <td style={{ padding: "12px" }}>{row.name}</td>
                  <td style={{ padding: "12px" }}>{row.email}</td>
                  <td style={{ padding: "12px" }}>
                    <span
                      style={{
                        padding: "2px 8px",
                        borderRadius: "12px",
                        fontSize: "12px",
                        fontWeight: "500",
                        background:
                          row.status === "Active" ? "#e8f5e8" : "#fff3e0",
                        color: row.status === "Active" ? "#2e7d32" : "#ef6c00",
                      }}
                    >
                      {row.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Footer */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
            <Typography level="body-2" style={{ color: "#666" }}>
              Showing {startItem}-{endItem} of {totalItems} entries
            </Typography>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Typography level="body-2">Show:</Typography>
              <select
                value={itemsPerPage}
                onChange={(e) =>
                  handleItemsPerPageChange(Number(e.target.value))
                }
                style={{
                  padding: "4px 8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
              <Typography level="body-2">per page</Typography>
            </div>
          </div>

          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            siblingCount={1}
            boundaryCount={1}
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world table pagination example with configurable items per page and data display.",
      },
    },
  },
}

// Search results example
export const SearchResultsExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalResults = 1247
    const resultsPerPage = 20
    const totalPages = Math.ceil(totalResults / resultsPerPage)

    const handlePageChange = (
      event: React.MouseEvent<HTMLButtonElement>,
      page: number
    ) => {
      setCurrentPage(page)
    }

    const startResult = (currentPage - 1) * resultsPerPage + 1
    const endResult = Math.min(currentPage * resultsPerPage, totalResults)

    // Mock search results
    const searchResults = Array.from(
      { length: resultsPerPage },
      (_, index) => ({
        id: startResult + index,
        title: `Search Result ${startResult + index}`,
        description: `This is a description for search result ${startResult + index}. It contains relevant information about the topic you searched for.`,
        url: `https://example.com/result-${startResult + index}`,
        date: new Date(
          Date.now() - Math.random() * 10000000000
        ).toLocaleDateString(),
      })
    )

    return (
      <div style={{ width: "700px" }}>
        <div style={{ marginBottom: "24px" }}>
          <Typography level="h3" gutterBottom>
            Search Results
          </Typography>
          <Typography level="body-1" style={{ color: "#666" }}>
            About {totalResults.toLocaleString()} results (
            {(Math.random() * 0.5 + 0.2).toFixed(2)} seconds)
          </Typography>
        </div>

        {/* Results */}
        <div style={{ marginBottom: "32px" }}>
          {searchResults.map((result) => (
            <div
              key={result.id}
              style={{
                marginBottom: "24px",
                paddingBottom: "16px",
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <Typography
                level="h5"
                style={{
                  color: "#1976d2",
                  marginBottom: "4px",
                  cursor: "pointer",
                }}
              >
                {result.title}
              </Typography>
              <Typography
                level="caption"
                style={{
                  color: "#006621",
                  marginBottom: "8px",
                  display: "block",
                }}
              >
                {result.url}
              </Typography>
              <Typography
                level="body-2"
                style={{
                  color: "#545454",
                  lineHeight: "1.5",
                }}
              >
                {result.description}
              </Typography>
              <Typography
                level="caption"
                style={{
                  color: "#999",
                  marginTop: "8px",
                  display: "block",
                }}
              >
                {result.date}
              </Typography>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "16px",
            padding: "20px",
            background: "#fafafa",
            borderRadius: "8px",
          }}
        >
          <Typography level="body-2" style={{ color: "#666" }}>
            Page {currentPage} of {totalPages} (showing {startResult}-
            {endResult} of {totalResults.toLocaleString()} results)
          </Typography>

          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            siblingCount={2}
            boundaryCount={1}
          />

          <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
            <Button
              variant="plain"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 10))}
              disabled={currentPage <= 10}
            >
              ← Previous 10
            </Button>
            <Button
              variant="plain"
              size="sm"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 10))
              }
              disabled={currentPage > totalPages - 10}
            >
              Next 10 →
            </Button>
          </div>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Search results pagination with Google-like interface and bulk navigation options.",
      },
    },
  },
}
