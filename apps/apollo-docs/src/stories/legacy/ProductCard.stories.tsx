import { createTheme, ProductCard, ThemeProvider } from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

import "../../app/tailwind.css"

const theme = createTheme()

const meta: Meta<typeof ProductCard> = {
  title: "@design-systems∕apollo-ui/Components/Data Display/ProductCard",
  component: ProductCard,
  decorators: [
    (Story) => (
      <ThemeProvider theme={theme}>
        <div className="p-4">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "ProductCard displays product information in a structured card format with image, title, body, and footer sections.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "text" },
      description: "Width of the product card",
      defaultValue: "160px",
    },
    title: {
      control: { type: "text" },
      description: "Title of the product",
    },
    body: {
      control: { type: "text" },
      description: "Main content body",
    },
    footer: {
      control: { type: "text" },
      description: "Footer content",
    },
    extra: {
      control: { type: "text" },
      description: "Extra content above title",
    },
    imageSrc: {
      control: { type: "text" },
      description: "Image source URL",
    },
    imageOverlay: {
      control: { type: "text" },
      description: "Overlay content on image",
    },
    noImage: {
      control: { type: "text" },
      description: "Content when no image",
    },
  },
}

export default meta
type Story = StoryObj<typeof ProductCard>

export const Basic: Story = {
  args: {
    title: "Premium Wireless Headphones",
    body: "High-quality wireless headphones with noise cancellation and premium sound quality.",
    imageSrc: "https://picsum.photos/200/200?random=1",
  },
}

export const WithAllSections: Story = {
  args: {
    title: "Smart Watch Pro",
    extra: (
      <div className="flex justify-between items-center mb-2">
        <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
          Sale
        </span>
        <span className="text-sm text-gray-500">⭐ 4.8</span>
      </div>
    ),
    body: (
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Advanced fitness tracking with heart rate monitoring and GPS.
        </p>
        <div className="flex items-center gap-2">
          <span className="text-lg font-bold text-blue-600">$299</span>
          <span className="text-sm text-gray-400 line-through">$399</span>
        </div>
      </div>
    ),
    footer: (
      <div className="flex gap-2 mt-3">
        <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700">
          Add to Cart
        </button>
        <button className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50">
          ♡
        </button>
      </div>
    ),
    imageSrc: "https://picsum.photos/200/200?random=2",
    imageOverlay: "New",
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <ProductCard
        size="120px"
        title="Small Card"
        body="Compact product card"
        imageSrc="https://picsum.photos/200/200?random=3"
      />
      <ProductCard
        size="160px"
        title="Default Card"
        body="Standard size product card with more content space"
        imageSrc="https://picsum.photos/200/200?random=4"
      />
      <ProductCard
        size="200px"
        title="Large Card"
        body="Larger product card with extended content area for detailed descriptions"
        imageSrc="https://picsum.photos/200/200?random=5"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "ProductCard supports different sizes through the size prop.",
      },
    },
  },
}

export const ImageVariants: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <ProductCard
        title="With Image"
        body="Product with standard image"
        imageSrc="https://picsum.photos/200/200?random=6"
      />
      <ProductCard
        title="With Overlay"
        body="Product with image overlay text"
        imageSrc="https://picsum.photos/200/200?random=7"
        imageOverlay="50% OFF"
      />
      <ProductCard
        title="No Image"
        body="Product without image shows placeholder"
        noImage={
          <div className="text-gray-400 text-center py-8">📷 No Image</div>
        }
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard handles different image states including overlays and no-image placeholders.",
      },
    },
  },
}

export const ECommerceShowcase: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {[
        {
          title: 'Laptop Pro 16"',
          price: "$2,399",
          originalPrice: "$2,699",
          rating: "4.9",
          reviews: "124",
          badge: "Best Seller",
          image: "https://picsum.photos/200/200?random=10",
          overlay: "Free Shipping",
        },
        {
          title: "Wireless Mouse",
          price: "$79",
          originalPrice: null,
          rating: "4.7",
          reviews: "89",
          badge: "New",
          image: "https://picsum.photos/200/200?random=11",
          overlay: null,
        },
        {
          title: "Gaming Keyboard",
          price: "$149",
          originalPrice: "$199",
          rating: "4.8",
          reviews: "56",
          badge: "Sale",
          image: "https://picsum.photos/200/200?random=12",
          overlay: "25% OFF",
        },
        {
          title: "USB-C Hub",
          price: "$89",
          originalPrice: null,
          rating: "4.6",
          reviews: "34",
          badge: null,
          image: "https://picsum.photos/200/200?random=13",
          overlay: null,
        },
      ].map((product, index) => (
        <ProductCard
          key={index}
          title={product.title}
          imageSrc={product.image}
          imageOverlay={product.overlay}
          extra={
            <div className="flex justify-between items-center">
              {product.badge && (
                <span
                  className={`text-xs px-2 py-1 rounded ${
                    product.badge === "Sale"
                      ? "bg-red-100 text-red-800"
                      : product.badge === "New"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {product.badge}
                </span>
              )}
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <span>⭐ {product.rating}</span>
                <span>({product.reviews})</span>
              </div>
            </div>
          }
          body={
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-gray-900">
                  {product.price}
                </span>
                {product.originalPrice && (
                  <span className="text-sm text-gray-400 line-through">
                    {product.originalPrice}
                  </span>
                )}
              </div>
            </div>
          }
          footer={
            <div className="space-y-2">
              <button className="w-full bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700 transition-colors">
                Add to Cart
              </button>
              <div className="flex gap-2">
                <button className="flex-1 border border-gray-300 py-1 px-2 rounded text-xs hover:bg-gray-50">
                  Wishlist
                </button>
                <button className="flex-1 border border-gray-300 py-1 px-2 rounded text-xs hover:bg-gray-50">
                  Compare
                </button>
              </div>
            </div>
          }
        />
      ))}
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Real-world e-commerce product showcase with pricing, ratings, badges, and actions.",
      },
    },
  },
}

export const FillWidth: Story = {
  render: () => (
    <div className="w-full max-w-2xl">
      <ProductCard
        size="fill"
        title="Full Width Product Card"
        extra={
          <div className="flex justify-between items-center">
            <span className="bg-purple-100 text-purple-800 text-sm px-3 py-1 rounded">
              Featured
            </span>
            <span className="text-sm text-gray-500">⭐ 4.9 (256 reviews)</span>
          </div>
        }
        body={
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Premium Wireless Earbuds</h4>
              <p className="text-gray-600 text-sm mb-3">
                Experience crystal-clear audio with our latest wireless earbuds
                featuring active noise cancellation, 30-hour battery life, and
                premium build quality.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Active Noise Cancellation</li>
                <li>• 30-hour battery life</li>
                <li>• IPX7 water resistance</li>
                <li>• Quick charge: 10min = 3hrs</li>
              </ul>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <span className="text-2xl font-bold text-blue-600">$199</span>
                <span className="text-lg text-gray-400 line-through">$299</span>
                <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                  33% OFF
                </span>
              </div>
              <div className="flex gap-2">
                <button className="flex-1 bg-blue-600 text-white py-3 px-6 rounded font-semibold hover:bg-blue-700">
                  Add to Cart
                </button>
                <button className="px-4 py-3 border border-gray-300 rounded hover:bg-gray-50">
                  ♡
                </button>
              </div>
            </div>
          </div>
        }
        imageSrc="https://picsum.photos/300/200?random=20"
        imageOverlay="Limited Time"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard with fill width adapts to container width, perfect for detailed product displays.",
      },
    },
  },
}

export const Interactive: Story = {
  render: () => {
    const products = [
      {
        id: 1,
        name: "Smartphone Pro",
        price: 899,
        image: "https://picsum.photos/200/200?random=30",
      },
      {
        id: 2,
        name: "Tablet Air",
        price: 549,
        image: "https://picsum.photos/200/200?random=31",
      },
      {
        id: 3,
        name: "Smartwatch",
        price: 399,
        image: "https://picsum.photos/200/200?random=32",
      },
    ]

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Interactive Product Cards</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              title={product.name}
              imageSrc={product.image}
              body={
                <div className="space-y-3">
                  <div className="text-xl font-bold text-blue-600">
                    ${product.price}
                  </div>
                  <div className="flex gap-2">
                    <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700 transition-colors">
                      Add to Cart
                    </button>
                  </div>
                </div>
              }
              className="cursor-pointer hover:shadow-lg transition-shadow border border-gray-200 rounded-lg overflow-hidden"
              onClick={() => alert(`Viewing ${product.name} details`)}
            />
          ))}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive ProductCards with click handlers and hover effects for user engagement.",
      },
    },
  },
}

export const CustomImageComponent: Story = {
  render: () => {
    // Custom image component with lazy loading
    const LazyImage = ({ src, alt, className, ...props }: any) => (
      <img
        {...props}
        src={src}
        alt={alt}
        className={`${className} transition-opacity duration-300`}
        loading="lazy"
        onLoad={(e) => {
          ;(e.target as HTMLImageElement).style.opacity = "1"
        }}
        style={{ opacity: 0 }}
      />
    )

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ProductCard
          title="Standard Image"
          body="Using default img component"
          imageSrc="https://picsum.photos/200/200?random=40"
        />
        <ProductCard<typeof LazyImage>
          title="Custom Lazy Image"
          body="Using custom lazy-loading image component"
          imageSrc="https://picsum.photos/200/200?random=41"
          ImageComponent={LazyImage}
          imageProps={{
            alt: "Product with lazy loading",
            onLoad: () => console.log("Image loaded!"),
          }}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "ProductCard supports custom image components for advanced functionality like lazy loading.",
      },
    },
  },
}
