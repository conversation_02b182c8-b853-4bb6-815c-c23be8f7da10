import { useState } from "react"
import { createTheme, Option, Select, ThemeProvider } from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Select",
  component: Select,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Select component allows users to choose one or multiple options from a dropdown list. It supports various states, validation, and customization options.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text when no option is selected",
    },
    label: {
      control: { type: "text" },
      description: "Label for the select field",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the select",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the select is disabled",
    },
    error: {
      control: { type: "boolean" },
      description: "Whether the select has an error state",
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the select is required",
    },
    multiple: {
      control: { type: "boolean" },
      description: "Whether multiple selections are allowed",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the select should take full width",
    },
  },
} satisfies Meta<typeof Select>

export default meta
type Story = StoryObj<typeof meta>

// Basic select
export const Basic: Story = {
  render: () => (
    <Select placeholder="Choose a fruit...">
      <Option value="apple">Apple</Option>
      <Option value="banana">Banana</Option>
      <Option value="cherry">Cherry</Option>
      <Option value="dragonfruit">Dragon Fruit</Option>
      <Option value="elderberry">Elderberry</Option>
    </Select>
  ),
  parameters: {
    docs: {
      description: {
        story: "Basic select with placeholder and simple options.",
      },
    },
  },
}

// With label and helper text
export const WithLabelAndHelper: Story = {
  render: () => (
    <Select
      label="Favorite Programming Language"
      helperText="Choose the language you enjoy working with most"
      placeholder="Select a language..."
      defaultValue="javascript"
    >
      <Option value="javascript">JavaScript</Option>
      <Option value="typescript">TypeScript</Option>
      <Option value="python">Python</Option>
      <Option value="java">Java</Option>
      <Option value="csharp">C#</Option>
      <Option value="go">Go</Option>
      <Option value="rust">Rust</Option>
    </Select>
  ),
  parameters: {
    docs: {
      description: {
        story: "Select with label, helper text, and default value.",
      },
    },
  },
}

// States demonstration
export const States: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Normal State</h4>
        <Select placeholder="Select an option..." label="Normal">
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Select>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Disabled State
        </h4>
        <Select
          placeholder="Cannot select..."
          label="Disabled"
          disabled
          helperText="This select is disabled"
        >
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Select>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Error State</h4>
        <Select
          placeholder="Fix the error..."
          label="Error State"
          error
          helperText="Please select a valid option"
          color="danger"
        >
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Select>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
          Required State
        </h4>
        <Select
          placeholder="Required selection..."
          label="Required Field"
          required
          helperText="This field is required"
        >
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Select>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different states of the select component: normal, disabled, error, and required.",
      },
    },
  },
}

// Controlled select
export const Controlled: Story = {
  render: () => {
    const [value, setValue] = useState<string>("medium")

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Select
          label="T-Shirt Size"
          value={value}
          onChange={(_, newValue) => setValue(newValue as string)}
          helperText={`Current selection: ${value}`}
        >
          <Option value="xs">Extra Small</Option>
          <Option value="small">Small</Option>
          <Option value="medium">Medium</Option>
          <Option value="large">Large</Option>
          <Option value="xl">Extra Large</Option>
          <Option value="xxl">2X Large</Option>
        </Select>

        <div
          style={{ padding: "8px", background: "#f5f5f5", borderRadius: "4px" }}
        >
          <strong>Selected value:</strong> {value}
        </div>

        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <button
            onClick={() => setValue("xs")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            XS
          </button>
          <button
            onClick={() => setValue("small")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Small
          </button>
          <button
            onClick={() => setValue("medium")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Medium
          </button>
          <button
            onClick={() => setValue("large")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Large
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled select with external state management and programmatic selection.",
      },
    },
  },
}

// Multiple selection
export const MultipleSelection: Story = {
  render: () => {
    const [selectedSkills, setSelectedSkills] = useState<string[]>([
      "react",
      "typescript",
    ])

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Select
          label="Technical Skills"
          multiple
          value={selectedSkills}
          onChange={(_, newValue) => setSelectedSkills(newValue as string[])}
          helperText={`Selected: ${selectedSkills.length} skill${selectedSkills.length !== 1 ? "s" : ""}`}
        >
          <Option value="react">React</Option>
          <Option value="vue">Vue</Option>
          <Option value="angular">Angular</Option>
          <Option value="typescript">TypeScript</Option>
          <Option value="javascript">JavaScript</Option>
          <Option value="python">Python</Option>
          <Option value="nodejs">Node.js</Option>
          <Option value="docker">Docker</Option>
          <Option value="kubernetes">Kubernetes</Option>
          <Option value="aws">AWS</Option>
        </Select>

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
          }}
        >
          <strong>Selected Skills:</strong>
          {selectedSkills.length > 0 ? (
            <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
              {selectedSkills.map((skill) => (
                <li key={skill}>{skill}</li>
              ))}
            </ul>
          ) : (
            <p style={{ margin: "4px 0 0 0", fontStyle: "italic" }}>
              No skills selected
            </p>
          )}
        </div>

        <div style={{ display: "flex", gap: "8px" }}>
          <button
            onClick={() => setSelectedSkills(["react", "typescript", "nodejs"])}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Frontend Stack
          </button>
          <button
            onClick={() => setSelectedSkills(["python", "docker", "aws"])}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Backend Stack
          </button>
          <button
            onClick={() => setSelectedSkills([])}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Clear All
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Multiple selection example with skill selection and preset options.",
      },
    },
  },
}

// Full width example
export const FullWidth: Story = {
  render: () => (
    <div style={{ width: "100%", maxWidth: "600px" }}>
      <Select
        label="Country/Region"
        placeholder="Select your country..."
        fullWidth
        defaultValue="us"
        helperText="This select takes the full width of its container"
      >
        <Option value="us">🇺🇸 United States</Option>
        <Option value="ca">🇨🇦 Canada</Option>
        <Option value="uk">🇬🇧 United Kingdom</Option>
        <Option value="de">🇩🇪 Germany</Option>
        <Option value="fr">🇫🇷 France</Option>
        <Option value="jp">🇯🇵 Japan</Option>
        <Option value="au">🇦🇺 Australia</Option>
        <Option value="br">🇧🇷 Brazil</Option>
        <Option value="in">🇮🇳 India</Option>
        <Option value="cn">🇨🇳 China</Option>
      </Select>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Full width select that adapts to its container width.",
      },
    },
  },
}

// Form example
export const FormExample: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      priority: "",
      category: "",
      assignee: "",
      tags: [] as string[],
    })

    const [submitted, setSubmitted] = useState(false)

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      setSubmitted(true)
      setTimeout(() => setSubmitted(false), 3000)
    }

    const isValid = formData.priority && formData.category && formData.assignee

    return (
      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "400px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Create Issue</h3>

        <Select
          label="Priority"
          placeholder="Select priority..."
          required
          value={formData.priority}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, priority: value as string }))
          }
          error={!formData.priority && submitted}
          helperText={
            !formData.priority && submitted ? "Priority is required" : undefined
          }
        >
          <Option value="low">🟢 Low</Option>
          <Option value="medium">🟡 Medium</Option>
          <Option value="high">🟠 High</Option>
          <Option value="critical">🔴 Critical</Option>
        </Select>

        <Select
          label="Category"
          placeholder="Select category..."
          required
          value={formData.category}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, category: value as string }))
          }
          error={!formData.category && submitted}
          helperText={
            !formData.category && submitted ? "Category is required" : undefined
          }
        >
          <Option value="bug">🐛 Bug</Option>
          <Option value="feature">✨ Feature Request</Option>
          <Option value="improvement">🔧 Improvement</Option>
          <Option value="documentation">📚 Documentation</Option>
          <Option value="question">❓ Question</Option>
        </Select>

        <Select
          label="Assignee"
          placeholder="Assign to..."
          required
          value={formData.assignee}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, assignee: value as string }))
          }
          error={!formData.assignee && submitted}
          helperText={
            !formData.assignee && submitted ? "Assignee is required" : undefined
          }
        >
          <Option value="john">👨‍💻 John Doe</Option>
          <Option value="jane">👩‍💻 Jane Smith</Option>
          <Option value="bob">👨‍💼 Bob Johnson</Option>
          <Option value="alice">👩‍🎨 Alice Brown</Option>
          <Option value="unassigned">👤 Unassigned</Option>
        </Select>

        <Select
          label="Tags"
          placeholder="Select tags..."
          multiple
          value={formData.tags}
          onChange={(_, value) =>
            setFormData((prev) => ({ ...prev, tags: value as string[] }))
          }
          helperText="Optional: Add relevant tags"
        >
          <Option value="frontend">Frontend</Option>
          <Option value="backend">Backend</Option>
          <Option value="database">Database</Option>
          <Option value="ui">UI</Option>
          <Option value="ux">UX</Option>
          <Option value="performance">Performance</Option>
          <Option value="security">Security</Option>
          <Option value="testing">Testing</Option>
        </Select>

        <button
          type="submit"
          disabled={!isValid}
          style={{
            padding: "12px 24px",
            background: isValid ? "#007bff" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: isValid ? "pointer" : "not-allowed",
            fontSize: "16px",
            marginTop: "8px",
          }}
        >
          Create Issue
        </button>

        {submitted && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Issue created successfully!
          </div>
        )}
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete form example with validation, required fields, and submission handling.",
      },
    },
  },
}

// Complex options example
export const ComplexOptions: Story = {
  render: () => {
    const [selectedUser, setSelectedUser] = useState<string>("")

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Select
          label="Team Member"
          placeholder="Select a team member..."
          value={selectedUser}
          onChange={(_, value) => setSelectedUser(value as string)}
          helperText="Choose from available team members"
        >
          <Option value="john-doe">👨‍💻 John Doe - Senior Developer</Option>
          <Option value="jane-smith">👩‍💼 Jane Smith - Product Manager</Option>
          <Option value="bob-johnson">👨‍🎨 Bob Johnson - UI/UX Designer</Option>
          <Option value="alice-brown">👩‍🔬 Alice Brown - QA Engineer</Option>
          <Option value="charlie-wilson">
            👨‍💼 Charlie Wilson - Project Manager
          </Option>
          <Option value="diana-lee">👩‍💻 Diana Lee - DevOps Engineer</Option>
        </Select>

        {selectedUser && (
          <div
            style={{
              padding: "12px",
              background: "#f8f9fa",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            <strong>Selected:</strong>{" "}
            {selectedUser
              .replace("-", " ")
              .replace(/\b\w/g, (l) => l.toUpperCase())}
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Select with complex option content including emojis and detailed descriptions.",
      },
    },
  },
}
