import { useState } from "react"
import { createTheme, SortingIcon, ThemeProvider } from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

import "../../app/tailwind.css"

const theme = createTheme()

const meta: Meta<typeof SortingIcon> = {
  title: "@design-systems∕apollo-ui/Components/Data Display/SortingIcon",
  component: SortingIcon,
  decorators: [
    (Story) => (
      <ThemeProvider theme={theme}>
        <div className="p-4">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "SortingIcon displays sortable column indicators with ascending, descending, and default states. Perfect for table headers and data sorting interfaces.",
      },
    },
  },
  argTypes: {
    status: {
      control: { type: "select" },
      options: ["default", "asc", "desc"],
      description: "Current sorting state",
      defaultValue: "default",
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes",
    },
  },
}

export default meta
type Story = StoryObj<typeof SortingIcon>

export const Basic: Story = {
  args: {
    status: "default",
  },
}

export const SortingStates: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <div className="mb-2">
          <SortingIcon status="default" />
        </div>
        <p className="text-sm text-gray-600">Default</p>
      </div>

      <div className="text-center">
        <div className="mb-2">
          <SortingIcon status="asc" />
        </div>
        <p className="text-sm text-gray-600">Ascending</p>
      </div>

      <div className="text-center">
        <div className="mb-2">
          <SortingIcon status="desc" />
        </div>
        <p className="text-sm text-gray-600">Descending</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "SortingIcon supports three states: default (unsorted), ascending, and descending.",
      },
    },
  },
}

export const TableHeaders: Story = {
  render: () => {
    const [sortColumn, setSortColumn] = useState<string | null>(null)
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

    const handleSort = (column: string) => {
      if (sortColumn === column) {
        if (sortDirection === "asc") {
          setSortDirection("desc")
        } else {
          setSortColumn(null)
          setSortDirection("asc")
        }
      } else {
        setSortColumn(column)
        setSortDirection("asc")
      }
    }

    const getSortStatus = (column: string) => {
      if (sortColumn === column) {
        return sortDirection
      }
      return "default"
    }

    const columns = [
      { key: "name", label: "Name" },
      { key: "email", label: "Email" },
      { key: "role", label: "Role" },
      { key: "status", label: "Status" },
      { key: "lastActive", label: "Last Active" },
    ]

    return (
      <div className="w-full max-w-4xl">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="border border-gray-300 px-4 py-3 text-left cursor-pointer hover:bg-gray-100 select-none"
                  onClick={() => handleSort(column.key)}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-700">
                      {column.label}
                    </span>
                    <SortingIcon status={getSortStatus(column.key)} />
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-3">John Doe</td>
              <td className="border border-gray-300 px-4 py-3">
                <EMAIL>
              </td>
              <td className="border border-gray-300 px-4 py-3">Admin</td>
              <td className="border border-gray-300 px-4 py-3">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  Active
                </span>
              </td>
              <td className="border border-gray-300 px-4 py-3">2 hours ago</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-3">Jane Smith</td>
              <td className="border border-gray-300 px-4 py-3">
                <EMAIL>
              </td>
              <td className="border border-gray-300 px-4 py-3">User</td>
              <td className="border border-gray-300 px-4 py-3">
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">
                  Away
                </span>
              </td>
              <td className="border border-gray-300 px-4 py-3">1 day ago</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-3">Bob Johnson</td>
              <td className="border border-gray-300 px-4 py-3">
                <EMAIL>
              </td>
              <td className="border border-gray-300 px-4 py-3">Editor</td>
              <td className="border border-gray-300 px-4 py-3">
                <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
                  Offline
                </span>
              </td>
              <td className="border border-gray-300 px-4 py-3">3 days ago</td>
            </tr>
          </tbody>
        </table>

        {sortColumn && (
          <p className="mt-4 text-sm text-gray-600">
            Currently sorting by <strong>{sortColumn}</strong> in{" "}
            <strong>{sortDirection}ending</strong> order
          </p>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "SortingIcon used in table headers for interactive column sorting functionality.",
      },
    },
  },
}

export const DataGridExample: Story = {
  render: () => {
    const [sortConfig, setSortConfig] = useState<{
      key: string
      direction: "asc" | "desc"
    } | null>(null)

    const data = [
      { id: 1, product: "Laptop Pro", price: 1299, stock: 45, rating: 4.8 },
      { id: 2, product: "Mouse Wireless", price: 79, stock: 120, rating: 4.5 },
      {
        id: 3,
        product: "Keyboard Mechanical",
        price: 159,
        stock: 67,
        rating: 4.7,
      },
      { id: 4, product: "Monitor 4K", price: 599, stock: 23, rating: 4.6 },
      { id: 5, product: "Headphones Pro", price: 299, stock: 89, rating: 4.9 },
    ]

    const handleSort = (key: string) => {
      let direction: "asc" | "desc" = "asc"
      if (
        sortConfig &&
        sortConfig.key === key &&
        sortConfig.direction === "asc"
      ) {
        direction = "desc"
      }
      setSortConfig({ key, direction })
    }

    const sortedData = [...data].sort((a, b) => {
      if (!sortConfig) return 0

      const aValue = a[sortConfig.key as keyof typeof a]
      const bValue = b[sortConfig.key as keyof typeof b]

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1
      }
      return 0
    })

    const getSortStatus = (key: string) => {
      if (sortConfig?.key === key) {
        return sortConfig.direction
      }
      return "default"
    }

    return (
      <div className="w-full max-w-5xl">
        <h3 className="text-lg font-semibold mb-4">Product Inventory</h3>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-white rounded-lg shadow">
            <thead>
              <tr className="bg-gray-100">
                <th className="border-b border-gray-200 px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort("product")}
                    className="flex items-center justify-between w-full font-semibold text-gray-700 hover:text-gray-900"
                  >
                    Product Name
                    <SortingIcon status={getSortStatus("product")} />
                  </button>
                </th>
                <th className="border-b border-gray-200 px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort("price")}
                    className="flex items-center justify-between w-full font-semibold text-gray-700 hover:text-gray-900"
                  >
                    Price
                    <SortingIcon status={getSortStatus("price")} />
                  </button>
                </th>
                <th className="border-b border-gray-200 px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort("stock")}
                    className="flex items-center justify-between w-full font-semibold text-gray-700 hover:text-gray-900"
                  >
                    Stock
                    <SortingIcon status={getSortStatus("stock")} />
                  </button>
                </th>
                <th className="border-b border-gray-200 px-6 py-4 text-left">
                  <button
                    onClick={() => handleSort("rating")}
                    className="flex items-center justify-between w-full font-semibold text-gray-700 hover:text-gray-900"
                  >
                    Rating
                    <SortingIcon status={getSortStatus("rating")} />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedData.map((item, index) => (
                <tr
                  key={item.id}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  <td className="border-b border-gray-200 px-6 py-4">
                    {item.product}
                  </td>
                  <td className="border-b border-gray-200 px-6 py-4 font-mono">
                    ${item.price}
                  </td>
                  <td className="border-b border-gray-200 px-6 py-4">
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        item.stock > 50
                          ? "bg-green-100 text-green-800"
                          : item.stock > 20
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                      }`}
                    >
                      {item.stock} units
                    </span>
                  </td>
                  <td className="border-b border-gray-200 px-6 py-4">
                    <div className="flex items-center gap-1">
                      <span>⭐</span>
                      <span>{item.rating}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {sortConfig && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
            <p className="text-sm text-blue-800">
              Sorted by <strong>{sortConfig.key}</strong> in{" "}
              <strong>{sortConfig.direction}ending</strong> order
            </p>
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete data grid example with functional sorting using SortingIcon.",
      },
    },
  },
}

export const CustomStyling: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h4 className="font-semibold mb-3">Size Variations</h4>
        <div className="flex items-center gap-6">
          <div className="text-center">
            <div className="mb-2" style={{ fontSize: "12px" }}>
              <SortingIcon status="asc" />
            </div>
            <p className="text-xs text-gray-600">Small</p>
          </div>

          <div className="text-center">
            <div className="mb-2" style={{ fontSize: "16px" }}>
              <SortingIcon status="desc" />
            </div>
            <p className="text-xs text-gray-600">Default</p>
          </div>

          <div className="text-center">
            <div className="mb-2" style={{ fontSize: "24px" }}>
              <SortingIcon status="default" />
            </div>
            <p className="text-xs text-gray-600">Large</p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-semibold mb-3">Custom Colors</h4>
        <div className="flex items-center gap-6">
          <div className="text-center">
            <div className="mb-2" style={{ color: "#ef4444" }}>
              <SortingIcon status="asc" />
            </div>
            <p className="text-xs text-gray-600">Red</p>
          </div>

          <div className="text-center">
            <div className="mb-2" style={{ color: "#3b82f6" }}>
              <SortingIcon status="desc" />
            </div>
            <p className="text-xs text-gray-600">Blue</p>
          </div>

          <div className="text-center">
            <div className="mb-2" style={{ color: "#10b981" }}>
              <SortingIcon status="default" />
            </div>
            <p className="text-xs text-gray-600">Green</p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-semibold mb-3">Custom Styling</h4>
        <div className="flex items-center gap-6">
          <div className="text-center">
            <div className="mb-2 p-2 bg-gray-100 rounded">
              <SortingIcon status="asc" />
            </div>
            <p className="text-xs text-gray-600">With Background</p>
          </div>

          <div className="text-center">
            <div className="mb-2 p-2 border-2 border-blue-300 rounded">
              <SortingIcon status="desc" />
            </div>
            <p className="text-xs text-gray-600">With Border</p>
          </div>

          <div className="text-center">
            <div className="mb-2 p-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded text-white">
              <SortingIcon status="default" />
            </div>
            <p className="text-xs text-gray-600">Gradient</p>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "SortingIcon can be customized with different sizes, colors, and styling approaches.",
      },
    },
  },
}

export const AccessibilityExample: Story = {
  render: () => {
    const [sortColumn, setSortColumn] = useState<string | null>(null)
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

    const handleSort = (column: string) => {
      if (sortColumn === column) {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc")
      } else {
        setSortColumn(column)
        setSortDirection("asc")
      }
    }

    const getSortStatus = (column: string) => {
      if (sortColumn === column) {
        return sortDirection
      }
      return "default"
    }

    const getSortAriaLabel = (column: string) => {
      const status = getSortStatus(column)
      if (status === "asc") {
        return `Sort ${column} descending`
      } else if (status === "desc") {
        return `Sort ${column} ascending`
      } else {
        return `Sort by ${column}`
      }
    }

    return (
      <div className="w-full max-w-4xl">
        <h3 className="text-lg font-semibold mb-4">Accessible Sorting Table</h3>

        <table
          className="w-full border-collapse border border-gray-300"
          role="table"
        >
          <thead>
            <tr className="bg-gray-50">
              <th
                scope="col"
                className="border border-gray-300 px-4 py-3 text-left"
              >
                <button
                  onClick={() => handleSort("Name")}
                  aria-label={getSortAriaLabel("Name")}
                  className="flex items-center justify-between w-full font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                >
                  Name
                  <SortingIcon
                    status={getSortStatus("Name")}
                    aria-hidden="true"
                  />
                </button>
              </th>
              <th
                scope="col"
                className="border border-gray-300 px-4 py-3 text-left"
              >
                <button
                  onClick={() => handleSort("Department")}
                  aria-label={getSortAriaLabel("Department")}
                  className="flex items-center justify-between w-full font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                >
                  Department
                  <SortingIcon
                    status={getSortStatus("Department")}
                    aria-hidden="true"
                  />
                </button>
              </th>
              <th
                scope="col"
                className="border border-gray-300 px-4 py-3 text-left"
              >
                <button
                  onClick={() => handleSort("Salary")}
                  aria-label={getSortAriaLabel("Salary")}
                  className="flex items-center justify-between w-full font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
                >
                  Salary
                  <SortingIcon
                    status={getSortStatus("Salary")}
                    aria-hidden="true"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-3">
                Alice Johnson
              </td>
              <td className="border border-gray-300 px-4 py-3">Engineering</td>
              <td className="border border-gray-300 px-4 py-3">$95,000</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-3">Bob Smith</td>
              <td className="border border-gray-300 px-4 py-3">Marketing</td>
              <td className="border border-gray-300 px-4 py-3">$85,000</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-3">Carol Davis</td>
              <td className="border border-gray-300 px-4 py-3">Design</td>
              <td className="border border-gray-300 px-4 py-3">$90,000</td>
            </tr>
          </tbody>
        </table>

        <div className="mt-4 text-sm text-gray-600">
          <p>This table demonstrates proper accessibility features:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Keyboard navigation support</li>
            <li>Screen reader friendly aria-labels</li>
            <li>Focus indicators on sort buttons</li>
            <li>Semantic table structure</li>
          </ul>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Accessible implementation of SortingIcon with proper ARIA labels and keyboard navigation.",
      },
    },
  },
}

export const Interactive: Story = {
  render: () => {
    const [cycles, setCycles] = useState(0)
    const [currentStatus, setCurrentStatus] = useState<
      "default" | "asc" | "desc"
    >("default")

    const statusOptions: Array<"default" | "asc" | "desc"> = [
      "default",
      "asc",
      "desc",
    ]

    const handleClick = () => {
      const nextIndex =
        (statusOptions.indexOf(currentStatus) + 1) % statusOptions.length
      setCurrentStatus(statusOptions[nextIndex])

      if (nextIndex === 0) {
        setCycles(cycles + 1)
      }
    }

    const getStatusDescription = (status: "default" | "asc" | "desc") => {
      switch (status) {
        case "default":
          return "No sorting applied"
        case "asc":
          return "Sorted in ascending order (A→Z, 1→9)"
        case "desc":
          return "Sorted in descending order (Z→A, 9→1)"
      }
    }

    return (
      <div className="text-center space-y-6">
        <div>
          <h4 className="font-semibold mb-4">Interactive Sorting Demo</h4>
          <button
            onClick={handleClick}
            className="flex items-center gap-3 mx-auto px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          >
            <span className="font-medium">Click to cycle through states</span>
            <SortingIcon status={currentStatus} />
          </button>
        </div>

        <div className="space-y-3">
          <p className="text-lg">
            <strong>Current Status:</strong> {currentStatus}
          </p>
          <p className="text-gray-600">{getStatusDescription(currentStatus)}</p>
          <p className="text-sm text-gray-500">Complete cycles: {cycles}</p>
        </div>

        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
          {statusOptions.map((status) => (
            <button
              key={status}
              onClick={() => setCurrentStatus(status)}
              className={`p-3 border rounded-lg transition-colors ${
                currentStatus === status
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-300 hover:bg-gray-50"
              }`}
            >
              <div className="flex flex-col items-center gap-2">
                <SortingIcon status={status} />
                <span className="text-sm capitalize">{status}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive example showing SortingIcon state cycling and manual state selection.",
      },
    },
  },
}

export const Playground: Story = {
  args: {
    status: "default",
    className: "",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive playground to test different SortingIcon configurations.",
      },
    },
  },
}
