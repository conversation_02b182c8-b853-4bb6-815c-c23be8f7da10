import { useState } from "react"
import { createTheme, Switch, ThemeProvider } from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Switch",
  component: Switch,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Switch component allows users to toggle between two states (on/off). It supports various label placements and can be controlled or uncontrolled.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: { type: "text" },
      description: "The label content for the switch",
    },
    labelPlacement: {
      control: { type: "select" },
      options: ["top", "bottom", "left", "right"],
      description: "Position of the label relative to the switch",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the switch is disabled",
    },
    defaultChecked: {
      control: { type: "boolean" },
      description: "Whether the switch is checked by default",
    },
  },
} satisfies Meta<typeof Switch>

export default meta
type Story = StoryObj<typeof meta>

// Default switch
export const Default: Story = {
  args: {
    label: "Enable notifications",
    defaultChecked: false,
  },
}

// All label placements
export const LabelPlacements: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "24px",
        alignItems: "center",
      }}
    >
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, 200px)",
          gap: "20px",
          justifyItems: "center",
        }}
      >
        <div style={{ textAlign: "center" }}>
          <Switch label="Top" labelPlacement="top" defaultChecked />
          <p style={{ margin: "8px 0 0 0", fontSize: "12px", color: "#666" }}>
            labelPlacement="top"
          </p>
        </div>

        <div style={{ textAlign: "center" }}>
          <Switch label="Bottom" labelPlacement="bottom" defaultChecked />
          <p style={{ margin: "8px 0 0 0", fontSize: "12px", color: "#666" }}>
            labelPlacement="bottom"
          </p>
        </div>

        <div style={{ textAlign: "center" }}>
          <Switch label="Left" labelPlacement="left" defaultChecked />
          <p style={{ margin: "8px 0 0 0", fontSize: "12px", color: "#666" }}>
            labelPlacement="left"
          </p>
        </div>

        <div style={{ textAlign: "center" }}>
          <Switch label="Right" labelPlacement="right" defaultChecked />
          <p style={{ margin: "8px 0 0 0", fontSize: "12px", color: "#666" }}>
            labelPlacement="right" (default)
          </p>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Demonstration of all available label placement options.",
      },
    },
  },
}

// Switch states
export const States: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: "16px",
        }}
      >
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Normal States
          </h4>
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <Switch label="Unchecked" defaultChecked={false} />
            <Switch label="Checked" defaultChecked={true} />
          </div>
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Disabled States
          </h4>
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <Switch
              label="Disabled unchecked"
              disabled
              defaultChecked={false}
            />
            <Switch label="Disabled checked" disabled defaultChecked={true} />
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "All possible switch states: normal and disabled, both checked and unchecked.",
      },
    },
  },
}

// Controlled switch
export const Controlled: Story = {
  render: () => {
    const [checked, setChecked] = useState(false)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Switch
          label="Controlled switch"
          checked={checked}
          onChange={(event) => setChecked(event.target.checked)}
        />

        <div
          style={{ padding: "8px", background: "#f5f5f5", borderRadius: "4px" }}
        >
          <strong>Current state:</strong> {checked ? "ON" : "OFF"}
        </div>

        <div style={{ display: "flex", gap: "8px" }}>
          <button
            onClick={() => setChecked(true)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Turn ON
          </button>
          <button
            onClick={() => setChecked(false)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Turn OFF
          </button>
          <button
            onClick={() => setChecked(!checked)}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Toggle
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled switch with external state management and programmatic control.",
      },
    },
  },
}

// Settings panel example
export const SettingsPanel: Story = {
  render: () => {
    const [settings, setSettings] = useState({
      notifications: true,
      darkMode: false,
      autoSave: true,
      analytics: false,
      sound: true,
    })

    const handleToggle = (key: keyof typeof settings) => {
      setSettings((prev) => ({
        ...prev,
        [key]: !prev[key],
      }))
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "300px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>App Settings</h3>

        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <Switch
            label="Push notifications"
            checked={settings.notifications}
            onChange={() => handleToggle("notifications")}
          />

          <Switch
            label="Dark mode"
            checked={settings.darkMode}
            onChange={() => handleToggle("darkMode")}
          />

          <Switch
            label="Auto-save documents"
            checked={settings.autoSave}
            onChange={() => handleToggle("autoSave")}
          />

          <Switch
            label="Send analytics data"
            checked={settings.analytics}
            onChange={() => handleToggle("analytics")}
          />

          <Switch
            label="Sound effects"
            checked={settings.sound}
            onChange={() => handleToggle("sound")}
          />
        </div>

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Current Settings:</strong>
          <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
            {Object.entries(settings).map(([key, value]) => (
              <li key={key}>
                {key.replace(/([A-Z])/g, " $1").toLowerCase()}:{" "}
                {value ? "✅" : "❌"}
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world example of switches in a settings panel with state management.",
      },
    },
  },
}

// Feature toggles example
export const FeatureToggles: Story = {
  render: () => {
    const [features, setFeatures] = useState({
      betaFeatures: false,
      experimentalUI: false,
      advancedMode: false,
      developerTools: false,
    })

    const handleFeatureToggle = (feature: keyof typeof features) => {
      setFeatures((prev) => ({
        ...prev,
        [feature]: !prev[feature],
      }))
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "400px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Feature Toggles</h3>

        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div>
            <Switch
              label="🧪 Beta Features"
              checked={features.betaFeatures}
              onChange={() => handleFeatureToggle("betaFeatures")}
            />
            <p style={{ margin: "4px 0 0 0", fontSize: "12px", color: "#666" }}>
              Access to experimental features that are still in testing
            </p>
          </div>

          <div>
            <Switch
              label="🎨 Experimental UI"
              checked={features.experimentalUI}
              onChange={() => handleFeatureToggle("experimentalUI")}
              disabled={!features.betaFeatures}
            />
            <p style={{ margin: "4px 0 0 0", fontSize: "12px", color: "#666" }}>
              Try the new interface design (requires Beta Features)
            </p>
          </div>

          <div>
            <Switch
              label="⚡ Advanced Mode"
              checked={features.advancedMode}
              onChange={() => handleFeatureToggle("advancedMode")}
            />
            <p style={{ margin: "4px 0 0 0", fontSize: "12px", color: "#666" }}>
              Show advanced options and detailed controls
            </p>
          </div>

          <div>
            <Switch
              label="🔧 Developer Tools"
              checked={features.developerTools}
              onChange={() => handleFeatureToggle("developerTools")}
              disabled={!features.advancedMode}
            />
            <p style={{ margin: "4px 0 0 0", fontSize: "12px", color: "#666" }}>
              Enable debugging tools (requires Advanced Mode)
            </p>
          </div>
        </div>

        {(features.betaFeatures || features.advancedMode) && (
          <div
            style={{
              padding: "12px",
              background: "#e3f2fd",
              border: "1px solid #2196f3",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            🎉 Advanced features enabled! You now have access to experimental
            functionality.
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Feature toggle example with dependent switches and conditional UI.",
      },
    },
  },
}

// Accessibility example
export const AccessibilityFocused: Story = {
  render: () => {
    const [a11ySettings, setA11ySettings] = useState({
      screenReader: false,
      highContrast: false,
      largeText: false,
      reduceMotion: false,
      keyboardNav: true,
    })

    const handleA11yToggle = (setting: keyof typeof a11ySettings) => {
      setA11ySettings((prev) => ({
        ...prev,
        [setting]: !prev[setting],
      }))
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "350px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Accessibility Settings</h3>

        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <Switch
            label="Screen reader optimizations"
            checked={a11ySettings.screenReader}
            onChange={() => handleA11yToggle("screenReader")}
            labelPlacement="top"
          />

          <Switch
            label="High contrast mode"
            checked={a11ySettings.highContrast}
            onChange={() => handleA11yToggle("highContrast")}
            labelPlacement="top"
          />

          <Switch
            label="Large text size"
            checked={a11ySettings.largeText}
            onChange={() => handleA11yToggle("largeText")}
            labelPlacement="top"
          />

          <Switch
            label="Reduce motion effects"
            checked={a11ySettings.reduceMotion}
            onChange={() => handleA11yToggle("reduceMotion")}
            labelPlacement="top"
          />

          <Switch
            label="Enhanced keyboard navigation"
            checked={a11ySettings.keyboardNav}
            onChange={() => handleA11yToggle("keyboardNav")}
            labelPlacement="top"
          />
        </div>

        <div
          style={{
            padding: "12px",
            background: "#f1f8e9",
            border: "1px solid #4caf50",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Active Accessibility Features:</strong>
          <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
            {Object.entries(a11ySettings)
              .filter(([_, enabled]) => enabled)
              .map(([key, _]) => (
                <li key={key}>
                  {key.replace(/([A-Z])/g, " $1").toLowerCase()}
                </li>
              ))}
          </ul>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Accessibility-focused example with top-aligned labels and descriptive content.",
      },
    },
  },
}

// Form integration example
export const FormIntegration: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      agreeToTerms: false,
      subscribeNewsletter: true,
      enableNotifications: false,
      shareData: false,
    })

    const [submitted, setSubmitted] = useState(false)

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      setSubmitted(true)
      setTimeout(() => setSubmitted(false), 3000)
    }

    const isValid = formData.agreeToTerms

    return (
      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          maxWidth: "400px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Account Preferences</h3>

        <Switch
          label="I agree to the Terms of Service"
          checked={formData.agreeToTerms}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, agreeToTerms: e.target.checked }))
          }
        />

        <Switch
          label="Subscribe to newsletter"
          checked={formData.subscribeNewsletter}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              subscribeNewsletter: e.target.checked,
            }))
          }
        />

        <Switch
          label="Enable push notifications"
          checked={formData.enableNotifications}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              enableNotifications: e.target.checked,
            }))
          }
        />

        <Switch
          label="Share usage data for improvements"
          checked={formData.shareData}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, shareData: e.target.checked }))
          }
        />

        <button
          type="submit"
          disabled={!isValid}
          style={{
            padding: "12px 24px",
            background: isValid ? "#007bff" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: isValid ? "pointer" : "not-allowed",
            fontSize: "16px",
            marginTop: "8px",
          }}
        >
          Create Account
        </button>

        {!isValid && (
          <p style={{ color: "#d32f2f", fontSize: "14px", margin: "0" }}>
            Please agree to the Terms of Service to continue.
          </p>
        )}

        {submitted && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Account created successfully!
          </div>
        )}
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Switch components integrated within a form with validation and submission.",
      },
    },
  },
}
