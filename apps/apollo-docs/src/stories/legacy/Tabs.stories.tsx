import { useState } from "react"
import {
  <PERSON><PERSON>,
  createTheme,
  Tab,
  TabPanel,
  <PERSON><PERSON>,
  <PERSON>bsList,
  ThemeProvider,
  Typo<PERSON>,
} from "@apollo/ui/legacy"
import type { Meta, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/Tabs",
  component: Tabs,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Tabs organize content into multiple sections and allow users to navigate between them. The component consists of Tabs (container), TabsList (tab headers), Tab (individual tab), and TabPanel (content areas).",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether tabs take full width",
    },
    defaultValue: {
      control: { type: "text" },
      description: "Default selected tab value",
    },
    orientation: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
      description: "Tab orientation",
    },
  },
} satisfies Meta<typeof Tabs>

export default meta
type Story = StoryObj<typeof meta>

// Basic tabs
export const Basic: Story = {
  render: () => (
    <div style={{ width: "500px" }}>
      <Tabs defaultValue="tab1">
        <TabsList>
          <Tab value="tab1">Overview</Tab>
          <Tab value="tab2">Details</Tab>
          <Tab value="tab3">Settings</Tab>
        </TabsList>
        <TabPanel value="tab1">
          <Typography level="h4" gutterBottom>
            Overview
          </Typography>
          <Typography>
            This is the overview tab content. It provides a summary of the most
            important information.
          </Typography>
        </TabPanel>
        <TabPanel value="tab2">
          <Typography level="h4" gutterBottom>
            Details
          </Typography>
          <Typography>
            This is the details tab content. It contains more comprehensive
            information about the subject.
          </Typography>
        </TabPanel>
        <TabPanel value="tab3">
          <Typography level="h4" gutterBottom>
            Settings
          </Typography>
          <Typography>
            This is the settings tab content. Here users can configure various
            options and preferences.
          </Typography>
        </TabPanel>
      </Tabs>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Basic tabs with three panels showing different content.",
      },
    },
  },
} // Full width tabs
export const FullWidth: Story = {
  render: () => (
    <div style={{ width: "100%", maxWidth: "700px" }}>
      <Tabs defaultValue="home" fullWidth>
        <TabsList>
          <Tab value="home">Home</Tab>
          <Tab value="products">Products</Tab>
          <Tab value="services">Services</Tab>
          <Tab value="contact">Contact</Tab>
        </TabsList>
        <TabPanel value="home">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h3" gutterBottom>
              Welcome Home
            </Typography>
            <Typography gutterBottom>
              This is the home page content. We're glad to have you here!
              Explore our products and services using the tabs above.
            </Typography>
            <Button>Get Started</Button>
          </div>
        </TabPanel>
        <TabPanel value="products">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h3" gutterBottom>
              Our Products
            </Typography>
            <Typography gutterBottom>
              Discover our range of innovative products designed to meet your
              needs.
            </Typography>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(3, 1fr)",
                gap: "16px",
                marginTop: "16px",
              }}
            >
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Product A
                </Typography>
                <Typography level="body-2">Description of product A</Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Product B
                </Typography>
                <Typography level="body-2">Description of product B</Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Product C
                </Typography>
                <Typography level="body-2">Description of product C</Typography>
              </div>
            </div>
          </div>
        </TabPanel>
        <TabPanel value="services">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h3" gutterBottom>
              Our Services
            </Typography>
            <Typography gutterBottom>
              We offer comprehensive services to support your business goals.
            </Typography>
            <ul style={{ marginLeft: "20px", marginTop: "16px" }}>
              <li>
                <Typography>Consulting Services</Typography>
              </li>
              <li>
                <Typography>Technical Support</Typography>
              </li>
              <li>
                <Typography>Training Programs</Typography>
              </li>
              <li>
                <Typography>Custom Solutions</Typography>
              </li>
            </ul>
          </div>
        </TabPanel>
        <TabPanel value="contact">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h3" gutterBottom>
              Contact Us
            </Typography>
            <Typography gutterBottom>
              Get in touch with our team. We'd love to hear from you!
            </Typography>
            <div style={{ marginTop: "16px" }}>
              <Typography level="body-2" gutterBottom>
                📧 Email: <EMAIL>
              </Typography>
              <Typography level="body-2" gutterBottom>
                📞 Phone: (*************
              </Typography>
              <Typography level="body-2">
                📍 Address: 123 Business St, City, State 12345
              </Typography>
            </div>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Full width tabs that distribute evenly across the container width.",
      },
    },
  },
}

// Controlled tabs
export const Controlled: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState("step1")
    const [completedSteps, setCompletedSteps] = useState<string[]>([])

    const handleNext = () => {
      if (!completedSteps.includes(activeTab)) {
        setCompletedSteps((prev) => [...prev, activeTab])
      }

      if (activeTab === "step1") setActiveTab("step2")
      else if (activeTab === "step2") setActiveTab("step3")
      else if (activeTab === "step3") setActiveTab("complete")
    }

    const handlePrevious = () => {
      if (activeTab === "step2") setActiveTab("step1")
      else if (activeTab === "step3") setActiveTab("step2")
      else if (activeTab === "complete") setActiveTab("step3")
    }

    const handleReset = () => {
      setActiveTab("step1")
      setCompletedSteps([])
    }

    return (
      <div style={{ width: "600px" }}>
        <Tabs
          value={activeTab}
          onChange={(_, value) => setActiveTab(value as string)}
        >
          <TabsList>
            <Tab value="step1">
              {completedSteps.includes("step1") ? "✓ " : ""}Step 1
            </Tab>
            <Tab value="step2" disabled={!completedSteps.includes("step1")}>
              {completedSteps.includes("step2") ? "✓ " : ""}Step 2
            </Tab>
            <Tab value="step3" disabled={!completedSteps.includes("step2")}>
              {completedSteps.includes("step3") ? "✓ " : ""}Step 3
            </Tab>
            <Tab value="complete" disabled={!completedSteps.includes("step3")}>
              Complete
            </Tab>
          </TabsList>

          <TabPanel value="step1">
            <div style={{ padding: "24px 0" }}>
              <Typography level="h4" gutterBottom>
                Step 1: Basic Information
              </Typography>
              <Typography gutterBottom>
                Please provide your basic information to get started.
              </Typography>
              <div style={{ marginTop: "16px" }}>
                <input
                  placeholder="Full Name"
                  style={{
                    width: "100%",
                    padding: "8px",
                    marginBottom: "8px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                  }}
                />
                <input
                  placeholder="Email Address"
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ddd",
                    borderRadius: "4px",
                  }}
                />
              </div>
            </div>
          </TabPanel>

          <TabPanel value="step2">
            <div style={{ padding: "24px 0" }}>
              <Typography level="h4" gutterBottom>
                Step 2: Preferences
              </Typography>
              <Typography gutterBottom>
                Configure your preferences and settings.
              </Typography>
              <div style={{ marginTop: "16px" }}>
                <label style={{ display: "block", marginBottom: "8px" }}>
                  <input type="checkbox" style={{ marginRight: "8px" }} />
                  Email notifications
                </label>
                <label style={{ display: "block", marginBottom: "8px" }}>
                  <input type="checkbox" style={{ marginRight: "8px" }} />
                  SMS notifications
                </label>
                <label style={{ display: "block" }}>
                  <input type="checkbox" style={{ marginRight: "8px" }} />
                  Newsletter subscription
                </label>
              </div>
            </div>
          </TabPanel>

          <TabPanel value="step3">
            <div style={{ padding: "24px 0" }}>
              <Typography level="h4" gutterBottom>
                Step 3: Review
              </Typography>
              <Typography gutterBottom>
                Please review your information before submitting.
              </Typography>
              <div
                style={{
                  marginTop: "16px",
                  padding: "16px",
                  background: "#f8f9fa",
                  borderRadius: "8px",
                }}
              >
                <Typography level="body-2" gutterBottom>
                  Review your details here...
                </Typography>
                <Typography level="caption">
                  All information looks good and ready for submission.
                </Typography>
              </div>
            </div>
          </TabPanel>

          <TabPanel value="complete">
            <div style={{ padding: "24px 0", textAlign: "center" }}>
              <Typography level="h4" gutterBottom>
                🎉 Complete!
              </Typography>
              <Typography gutterBottom>
                Congratulations! You have successfully completed all steps.
              </Typography>
              <Button
                onClick={handleReset}
                variant="outline"
                style={{ marginTop: "16px" }}
              >
                Start Over
              </Button>
            </div>
          </TabPanel>
        </Tabs>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: "24px",
            paddingTop: "16px",
            borderTop: "1px solid #e0e0e0",
          }}
        >
          <Button
            onClick={handlePrevious}
            variant="outline"
            disabled={activeTab === "step1"}
          >
            Previous
          </Button>
          <Button onClick={handleNext} disabled={activeTab === "complete"}>
            {activeTab === "step3" ? "Finish" : "Next"}
          </Button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled tabs with step progression, disabled states, and completion tracking.",
      },
    },
  },
}

// Tabs with icons and badges
export const WithIconsAndBadges: Story = {
  render: () => (
    <div style={{ width: "600px" }}>
      <Tabs defaultValue="dashboard">
        <TabsList>
          <Tab value="dashboard">📊 Dashboard</Tab>
          <Tab value="messages">💬 Messages (3)</Tab>
          <Tab value="notifications">🔔 Notifications</Tab>
          <Tab value="settings">⚙️ Settings</Tab>
        </TabsList>

        <TabPanel value="dashboard">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              📊 Dashboard
            </Typography>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
                gap: "16px",
              }}
            >
              <div
                style={{
                  padding: "16px",
                  background: "#e3f2fd",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Total Users
                </Typography>
                <Typography level="h3">1,234</Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  background: "#e8f5e8",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Revenue
                </Typography>
                <Typography level="h3">$12,345</Typography>
              </div>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="messages">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              💬 Messages
            </Typography>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "12px" }}
            >
              <div
                style={{
                  padding: "12px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  John Doe
                </Typography>
                <Typography level="body-2">
                  Hey, how's the project going?
                </Typography>
                <Typography level="caption" style={{ color: "#666" }}>
                  2 minutes ago
                </Typography>
              </div>
              <div
                style={{
                  padding: "12px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Jane Smith
                </Typography>
                <Typography level="body-2">
                  The meeting has been rescheduled to 3 PM.
                </Typography>
                <Typography level="caption" style={{ color: "#666" }}>
                  1 hour ago
                </Typography>
              </div>
              <div
                style={{
                  padding: "12px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  Team Lead
                </Typography>
                <Typography level="body-2">
                  Great work on the latest release!
                </Typography>
                <Typography level="caption" style={{ color: "#666" }}>
                  3 hours ago
                </Typography>
              </div>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="notifications">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              🔔 Notifications
            </Typography>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "8px" }}
            >
              <div
                style={{
                  padding: "12px",
                  background: "#fff3cd",
                  borderRadius: "8px",
                }}
              >
                <Typography level="body-2">
                  System maintenance scheduled for tonight
                </Typography>
              </div>
              <div
                style={{
                  padding: "12px",
                  background: "#d1ecf1",
                  borderRadius: "8px",
                }}
              >
                <Typography level="body-2">
                  New feature update available
                </Typography>
              </div>
              <div
                style={{
                  padding: "12px",
                  background: "#f8d7da",
                  borderRadius: "8px",
                }}
              >
                <Typography level="body-2">
                  Storage space running low
                </Typography>
              </div>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="settings">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              ⚙️ Settings
            </Typography>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "16px" }}
            >
              <div>
                <Typography level="h5" gutterBottom>
                  Profile Settings
                </Typography>
                <label style={{ display: "block", marginBottom: "8px" }}>
                  <input
                    type="checkbox"
                    style={{ marginRight: "8px" }}
                    defaultChecked
                  />
                  Public profile
                </label>
                <label style={{ display: "block" }}>
                  <input type="checkbox" style={{ marginRight: "8px" }} />
                  Show email address
                </label>
              </div>
              <div>
                <Typography level="h5" gutterBottom>
                  Notification Settings
                </Typography>
                <label style={{ display: "block", marginBottom: "8px" }}>
                  <input
                    type="checkbox"
                    style={{ marginRight: "8px" }}
                    defaultChecked
                  />
                  Email notifications
                </label>
                <label style={{ display: "block" }}>
                  <input
                    type="checkbox"
                    style={{ marginRight: "8px" }}
                    defaultChecked
                  />
                  Push notifications
                </label>
              </div>
            </div>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Tabs with emoji icons and badge indicators for enhanced visual communication.",
      },
    },
  },
}

// Scrollable tabs
export const ScrollableTabs: Story = {
  render: () => {
    const tabs = [
      "Overview",
      "Getting Started",
      "Components",
      "API Reference",
      "Examples",
      "Best Practices",
      "Troubleshooting",
      "FAQ",
      "Changelog",
      "Contributing",
      "License",
      "Support",
    ]

    return (
      <div style={{ width: "500px" }}>
        <Tabs defaultValue="Overview">
          <TabsList variant="scrollable" scrollButtons>
            {tabs.map((tab) => (
              <Tab key={tab} value={tab}>
                {tab}
              </Tab>
            ))}
          </TabsList>

          {tabs.map((tab) => (
            <TabPanel key={tab} value={tab}>
              <div style={{ padding: "20px 0" }}>
                <Typography level="h4" gutterBottom>
                  {tab}
                </Typography>
                <Typography>
                  This is the content for the {tab} section.
                  {tab === "Overview" &&
                    " This section provides a high-level overview of the documentation."}
                  {tab === "Getting Started" &&
                    " Learn how to set up and begin using our system."}
                  {tab === "Components" &&
                    " Explore all available components and their usage."}
                  {tab === "API Reference" &&
                    " Detailed API documentation and method references."}
                  {tab === "Examples" &&
                    " Code examples and implementation samples."}
                  {tab === "Best Practices" &&
                    " Recommended patterns and coding standards."}
                  {tab === "Troubleshooting" &&
                    " Common issues and their solutions."}
                  {tab === "FAQ" && " Frequently asked questions and answers."}
                  {tab === "Changelog" && " Version history and release notes."}
                  {tab === "Contributing" &&
                    " Guidelines for contributing to the project."}
                  {tab === "License" &&
                    " License information and terms of use."}
                  {tab === "Support" &&
                    " How to get help and support resources."}
                </Typography>
              </div>
            </TabPanel>
          ))}
        </Tabs>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Scrollable tabs with navigation arrows when tabs exceed container width.",
      },
    },
  },
}

// Tabs with different content types
export const DifferentContentTypes: Story = {
  render: () => (
    <div style={{ width: "700px" }}>
      <Tabs defaultValue="table">
        <TabsList>
          <Tab value="table">📋 Table</Tab>
          <Tab value="chart">📈 Chart</Tab>
          <Tab value="form">📝 Form</Tab>
          <Tab value="gallery">🖼️ Gallery</Tab>
        </TabsList>

        <TabPanel value="table">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Data Table
            </Typography>
            <table style={{ width: "100%", borderCollapse: "collapse" }}>
              <thead>
                <tr style={{ borderBottom: "2px solid #e0e0e0" }}>
                  <th style={{ padding: "12px", textAlign: "left" }}>Name</th>
                  <th style={{ padding: "12px", textAlign: "left" }}>Email</th>
                  <th style={{ padding: "12px", textAlign: "left" }}>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                  <td style={{ padding: "12px" }}>John Doe</td>
                  <td style={{ padding: "12px" }}><EMAIL></td>
                  <td style={{ padding: "12px" }}>Active</td>
                </tr>
                <tr style={{ borderBottom: "1px solid #e0e0e0" }}>
                  <td style={{ padding: "12px" }}>Jane Smith</td>
                  <td style={{ padding: "12px" }}><EMAIL></td>
                  <td style={{ padding: "12px" }}>Inactive</td>
                </tr>
                <tr>
                  <td style={{ padding: "12px" }}>Bob Johnson</td>
                  <td style={{ padding: "12px" }}><EMAIL></td>
                  <td style={{ padding: "12px" }}>Pending</td>
                </tr>
              </tbody>
            </table>
          </div>
        </TabPanel>

        <TabPanel value="chart">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Analytics Chart
            </Typography>
            <div
              style={{
                height: "200px",
                background: "linear-gradient(45deg, #e3f2fd, #bbdefb)",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography level="h5" style={{ color: "#1976d2" }}>
                📊 Chart Placeholder
              </Typography>
            </div>
            <Typography
              level="caption"
              style={{ color: "#666", marginTop: "8px", display: "block" }}
            >
              Chart showing user engagement over time
            </Typography>
          </div>
        </TabPanel>

        <TabPanel value="form">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Contact Form
            </Typography>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "12px",
                maxWidth: "400px",
              }}
            >
              <input
                placeholder="Your Name"
                style={{
                  padding: "8px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                }}
              />
              <input
                placeholder="Email Address"
                type="email"
                style={{
                  padding: "8px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                }}
              />
              <textarea
                placeholder="Your Message"
                rows={4}
                style={{
                  padding: "8px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  resize: "vertical",
                }}
              />
              <Button style={{ alignSelf: "flex-start" }}>Send Message</Button>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="gallery">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Image Gallery
            </Typography>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(3, 1fr)",
                gap: "12px",
              }}
            >
              {[1, 2, 3, 4, 5, 6].map((num) => (
                <div
                  key={num}
                  style={{
                    height: "120px",
                    background: `linear-gradient(135deg, #${Math.floor(Math.random() * 16777215).toString(16)}, #${Math.floor(Math.random() * 16777215).toString(16)})`,
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Typography style={{ color: "white", fontSize: "24px" }}>
                    🖼️
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Tabs containing different types of content: tables, charts, forms, and image galleries.",
      },
    },
  },
}

// Nested tabs
export const NestedTabs: Story = {
  render: () => (
    <div style={{ width: "700px" }}>
      <Tabs defaultValue="products">
        <TabsList>
          <Tab value="products">Products</Tab>
          <Tab value="services">Services</Tab>
          <Tab value="support">Support</Tab>
        </TabsList>

        <TabPanel value="products">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Products
            </Typography>

            <div style={{ marginTop: "16px" }}>
              <Tabs defaultValue="software">
                <TabsList>
                  <Tab value="software">Software</Tab>
                  <Tab value="hardware">Hardware</Tab>
                  <Tab value="accessories">Accessories</Tab>
                </TabsList>

                <TabPanel value="software">
                  <div style={{ padding: "16px 0" }}>
                    <Typography level="h5" gutterBottom>
                      Software Products
                    </Typography>
                    <ul style={{ marginLeft: "20px" }}>
                      <li>
                        <Typography>Desktop Application</Typography>
                      </li>
                      <li>
                        <Typography>Mobile App</Typography>
                      </li>
                      <li>
                        <Typography>Web Platform</Typography>
                      </li>
                      <li>
                        <Typography>API Suite</Typography>
                      </li>
                    </ul>
                  </div>
                </TabPanel>

                <TabPanel value="hardware">
                  <div style={{ padding: "16px 0" }}>
                    <Typography level="h5" gutterBottom>
                      Hardware Products
                    </Typography>
                    <ul style={{ marginLeft: "20px" }}>
                      <li>
                        <Typography>IoT Devices</Typography>
                      </li>
                      <li>
                        <Typography>Sensors</Typography>
                      </li>
                      <li>
                        <Typography>Controllers</Typography>
                      </li>
                      <li>
                        <Typography>Gateways</Typography>
                      </li>
                    </ul>
                  </div>
                </TabPanel>

                <TabPanel value="accessories">
                  <div style={{ padding: "16px 0" }}>
                    <Typography level="h5" gutterBottom>
                      Accessories
                    </Typography>
                    <ul style={{ marginLeft: "20px" }}>
                      <li>
                        <Typography>Cables & Adapters</Typography>
                      </li>
                      <li>
                        <Typography>Mounting Hardware</Typography>
                      </li>
                      <li>
                        <Typography>Protective Cases</Typography>
                      </li>
                      <li>
                        <Typography>Power Supplies</Typography>
                      </li>
                    </ul>
                  </div>
                </TabPanel>
              </Tabs>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="services">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Services
            </Typography>
            <Typography gutterBottom>
              We offer comprehensive services to support your business needs.
            </Typography>
            <div style={{ marginTop: "16px" }}>
              <Typography level="h5" gutterBottom>
                Available Services:
              </Typography>
              <ul style={{ marginLeft: "20px" }}>
                <li>
                  <Typography>Consultation & Planning</Typography>
                </li>
                <li>
                  <Typography>Implementation & Setup</Typography>
                </li>
                <li>
                  <Typography>Training & Education</Typography>
                </li>
                <li>
                  <Typography>Ongoing Support</Typography>
                </li>
              </ul>
            </div>
          </div>
        </TabPanel>

        <TabPanel value="support">
          <div style={{ padding: "20px 0" }}>
            <Typography level="h4" gutterBottom>
              Support
            </Typography>
            <Typography gutterBottom>
              Get help when you need it with our comprehensive support options.
            </Typography>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
                gap: "16px",
                marginTop: "16px",
              }}
            >
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  📚 Documentation
                </Typography>
                <Typography level="body-2">
                  Comprehensive guides and tutorials
                </Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  💬 Live Chat
                </Typography>
                <Typography level="body-2">Real-time support chat</Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  📧 Email Support
                </Typography>
                <Typography level="body-2">24/7 email assistance</Typography>
              </div>
              <div
                style={{
                  padding: "16px",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                }}
              >
                <Typography level="h5" gutterBottom>
                  📞 Phone Support
                </Typography>
                <Typography level="body-2">Direct phone support</Typography>
              </div>
            </div>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Nested tabs showing tabs within tab panels for complex navigation hierarchies.",
      },
    },
  },
}
