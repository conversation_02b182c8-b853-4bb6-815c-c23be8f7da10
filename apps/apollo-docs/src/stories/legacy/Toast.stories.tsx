import { useState } from "react"
import {
  <PERSON><PERSON>,
  createTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  To<PERSON>,
  ToastProvider,
  useToast,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Feedback/Toast",
  component: Toast,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <ToastProvider maxSnack={5}>
          <div style={{ padding: "20px" }}>
            <Story />
          </div>
        </ToastProvider>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Toast displays brief notification messages to users. It supports different severity levels, positioning, auto-hide functionality, and can be used with ToastProvider for programmatic control.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    open: {
      control: { type: "boolean" },
      description: "Whether the toast is visible",
    },
    severity: {
      control: { type: "select" },
      options: ["success", "info", "warning", "error"],
      description: "Toast severity/type",
    },
    position: {
      control: { type: "select" },
      options: [
        "top-center",
        "top-right",
        "top-left",
        "bottom-center",
        "bottom-right",
        "bottom-left",
      ],
      description: "Toast position on screen",
    },
    title: {
      control: { type: "text" },
      description: "Toast title",
    },
    description: {
      control: { type: "text" },
      description: "Toast description",
    },
    autoHideDuration: {
      control: { type: "number" },
      description: "Auto-hide duration in milliseconds",
    },
    lineClamp: {
      control: { type: "number" },
      description: "Maximum lines for description text",
    },
  },
} satisfies Meta<typeof Toast>

export default meta
type Story = StoryObj<typeof meta>

// Basic toast
export const Basic: Story = {
  args: {
    title: "Basic Toast",
  },
  render: () => {
    const [open, setOpen] = useState(false)

    return (
      <>
        <Button onClick={() => setOpen(true)}>Show Basic Toast</Button>
        <Toast
          open={open}
          onClose={() => setOpen(false)}
          title="Success!"
          description="Your action was completed successfully."
          severity="success"
        />
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic toast notification with title and description.",
      },
    },
  },
}

// Different severity types
export const SeverityTypes: Story = {
  args: {
    title: "Severity Types",
  },
  render: () => {
    const [activeToast, setActiveToast] = useState<string | null>(null)

    const toasts = [
      {
        id: "success",
        severity: "success" as const,
        title: "Success!",
        description: "Operation completed successfully.",
      },
      {
        id: "info",
        severity: "info" as const,
        title: "Information",
        description: "Here's some useful information.",
      },
      {
        id: "warning",
        severity: "warning" as const,
        title: "Warning",
        description: "Please be careful with this action.",
      },
      {
        id: "error",
        severity: "error" as const,
        title: "Error",
        description: "Something went wrong. Please try again.",
      },
    ]

    return (
      <>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "12px",
            width: "300px",
          }}
        >
          {toasts.map((toast) => (
            <Button
              key={toast.id}
              variant="outline"
              onClick={() => setActiveToast(toast.id)}
            >
              {toast.severity}
            </Button>
          ))}
        </div>

        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            open={activeToast === toast.id}
            onClose={() => setActiveToast(null)}
            severity={toast.severity}
            title={toast.title}
            description={toast.description}
          />
        ))}
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Different toast severity types: success, info, warning, and error.",
      },
    },
  },
}

// Different positions
export const Positions: Story = {
  args: {
    title: "Positions",
  },
  render: () => {
    const [activePosition, setActivePosition] = useState<string | null>(null)

    const positions = [
      { id: "top-left", position: "top-left" as const, label: "Top Left" },
      {
        id: "top-center",
        position: "top-center" as const,
        label: "Top Center",
      },
      { id: "top-right", position: "top-right" as const, label: "Top Right" },
      {
        id: "bottom-left",
        position: "bottom-left" as const,
        label: "Bottom Left",
      },
      {
        id: "bottom-center",
        position: "bottom-center" as const,
        label: "Bottom Center",
      },
      {
        id: "bottom-right",
        position: "bottom-right" as const,
        label: "Bottom Right",
      },
    ]

    return (
      <>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
            gap: "8px",
            width: "450px",
            marginBottom: "20px",
          }}
        >
          {positions.map((pos) => (
            <Button
              key={pos.id}
              size="sm"
              variant="outline"
              onClick={() => setActivePosition(pos.id)}
            >
              {pos.label}
            </Button>
          ))}
        </div>

        <div style={{ textAlign: "center", color: "#666", fontSize: "12px" }}>
          Click a button to see toast in different positions
        </div>

        {positions.map((pos) => (
          <Toast
            key={pos.id}
            open={activePosition === pos.id}
            onClose={() => setActivePosition(null)}
            position={pos.position}
            severity="info"
            title={`Toast at ${pos.label}`}
            description="This toast appears in a different position."
            autoHideDuration={2000}
          />
        ))}
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Toast notifications in different screen positions.",
      },
    },
  },
}

// Auto-hide durations
export const AutoHideDurations: Story = {
  args: {
    title: "Auto-hide Durations",
  },
  render: () => {
    const [activeToast, setActiveToast] = useState<string | null>(null)

    const durations = [
      { id: "short", duration: 1000, label: "1 second" },
      { id: "medium", duration: 3000, label: "3 seconds" },
      { id: "long", duration: 6000, label: "6 seconds" },
      { id: "manual", duration: 0, label: "Manual close" },
    ]

    return (
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            width: "200px",
          }}
        >
          {durations.map((item) => (
            <Button
              key={item.id}
              variant="outline"
              onClick={() => setActiveToast(item.id)}
            >
              {item.label}
            </Button>
          ))}
        </div>

        {durations.map((item) => (
          <Toast
            key={item.id}
            open={activeToast === item.id}
            onClose={() => setActiveToast(null)}
            severity="success"
            title={`Auto-hide: ${item.label}`}
            description={
              item.duration === 0
                ? "This toast won't auto-hide. Click X to close."
                : `This toast will disappear in ${item.duration / 1000} seconds.`
            }
            autoHideDuration={item.duration || undefined}
          />
        ))}
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Toast notifications with different auto-hide durations.",
      },
    },
  },
}

// Using ToastProvider and useToast hook
export const WithToastProvider: Story = {
  args: {
    title: "Toast Provider",
  },
  render: () => {
    const ToastButtons = () => {
      const { showSuccessToast, showErrorToast } = useToast()

      const handleSuccess = () => {
        showSuccessToast({
          title: "File Uploaded",
          description:
            "Your file has been uploaded successfully to the server.",
        })
      }

      const handleError = () => {
        showErrorToast({
          title: "Upload Failed",
          description:
            "Failed to upload file. Please check your connection and try again.",
        })
      }

      const handleMultiple = () => {
        showSuccessToast({
          title: "Task 1 Complete",
          description: "First task finished.",
        })

        setTimeout(() => {
          showSuccessToast({
            title: "Task 2 Complete",
            description: "Second task finished.",
          })
        }, 500)

        setTimeout(() => {
          showErrorToast({
            title: "Task 3 Failed",
            description: "Third task encountered an error.",
          })
        }, 1000)
      }

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            width: "200px",
          }}
        >
          <Button onClick={handleSuccess}>Show Success</Button>
          <Button onClick={handleError} color="danger">
            Show Error
          </Button>
          <Button onClick={handleMultiple} variant="outline">
            Show Multiple
          </Button>
        </div>
      )
    }

    return <ToastButtons />
  },
  parameters: {
    docs: {
      description: {
        story:
          "Using ToastProvider and useToast hook for programmatic toast management.",
      },
    },
  },
}

// Custom content and styling
export const CustomContent: Story = {
  args: {
    title: "Custom Content",
  },
  render: () => {
    const [showCustom, setShowCustom] = useState(false)
    const [showLong, setShowLong] = useState(false)

    return (
      <>
        <div style={{ display: "flex", gap: "12px" }}>
          <Button onClick={() => setShowCustom(true)}>Custom Icon</Button>
          <Button onClick={() => setShowLong(true)} variant="outline">
            Long Content
          </Button>
        </div>

        {/* Custom icon toast */}
        <Toast
          open={showCustom}
          onClose={() => setShowCustom(false)}
          severity="info"
          title="Custom Toast"
          description="This toast has a custom icon instead of the default one."
          startDecorator={<span style={{ fontSize: "20px" }}>🎉</span>}
        />

        {/* Long content toast */}
        <Toast
          open={showLong}
          onClose={() => setShowLong(false)}
          severity="warning"
          title="Storage Almost Full"
          description="You're using 95% of your available storage space. Consider deleting old files or upgrading your plan to avoid interruption of service. Files may fail to upload when storage is full."
          lineClamp={3}
          autoHideDuration={5000}
        />
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Toast with custom icons and long content that uses line clamping.",
      },
    },
  },
}

// Real-world scenarios
export const RealWorldScenarios: Story = {
  args: {
    title: "Real World Scenarios",
  },
  render: () => {
    const [toasts, setToasts] = useState<Array<{ id: string; type: string }>>(
      []
    )

    const showToast = (type: string) => {
      const id = Date.now().toString()
      setToasts((prev) => [...prev, { id, type }])

      // Auto remove after delay
      setTimeout(
        () => {
          setToasts((prev) => prev.filter((t) => t.id !== id))
        },
        type === "upload" ? 5000 : 3000
      )
    }

    const removeToast = (id: string) => {
      setToasts((prev) => prev.filter((t) => t.id !== id))
    }

    const scenarios = [
      { id: "save", label: "Save Document", type: "save" },
      { id: "delete", label: "Delete Item", type: "delete" },
      { id: "upload", label: "Upload File", type: "upload" },
      { id: "login", label: "Login Success", type: "login" },
      { id: "error", label: "Network Error", type: "error" },
    ]

    const getToastProps = (type: string) => {
      switch (type) {
        case "save":
          return {
            severity: "success" as const,
            title: "Document Saved",
            description: "Your changes have been saved automatically.",
          }
        case "delete":
          return {
            severity: "success" as const,
            title: "Item Deleted",
            description: "The item has been permanently removed.",
          }
        case "upload":
          return {
            severity: "info" as const,
            title: "Upload Complete",
            description: "Your file has been uploaded and is being processed.",
          }
        case "login":
          return {
            severity: "success" as const,
            title: "Welcome Back!",
            description: "You have successfully logged in.",
          }
        case "error":
          return {
            severity: "error" as const,
            title: "Connection Error",
            description:
              "Unable to connect to server. Please check your internet connection.",
          }
        default:
          return {
            severity: "info" as const,
            title: "Notification",
            description: "Something happened.",
          }
      }
    }

    return (
      <>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "8px",
            width: "300px",
            marginBottom: "20px",
          }}
        >
          {scenarios.map((scenario) => (
            <Button
              key={scenario.id}
              variant="outline"
              size="sm"
              onClick={() => showToast(scenario.type)}
            >
              {scenario.label}
            </Button>
          ))}
        </div>

        <div style={{ textAlign: "center", color: "#666", fontSize: "12px" }}>
          Active toasts: {toasts.length}
        </div>

        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            open={true}
            onClose={() => removeToast(toast.id)}
            position="top-right"
            {...getToastProps(toast.type)}
          />
        ))}
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Real-world toast scenarios for common application events like saving, deleting, uploading, and errors.",
      },
    },
  },
}

// Toast queue management
export const ToastQueue: Story = {
  args: {
    title: "Toast Queue",
  },
  render: () => {
    const [queue, setQueue] = useState<Array<{ id: number; message: string }>>(
      []
    )

    const addToQueue = () => {
      const id = Date.now()
      const messages = [
        "Message 1: Task started",
        "Message 2: Processing data",
        "Message 3: Validation complete",
        "Message 4: Saving results",
        "Message 5: Operation finished",
      ]

      messages.forEach((message, index) => {
        setTimeout(() => {
          setQueue((prev) => [...prev, { id: id + index, message }])

          // Auto remove after 2 seconds
          setTimeout(() => {
            setQueue((prev) => prev.filter((item) => item.id !== id + index))
          }, 2000)
        }, index * 500)
      })
    }

    return (
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            alignItems: "center",
          }}
        >
          <Button onClick={addToQueue}>Start Queue Demo</Button>
          <div style={{ fontSize: "12px", color: "#666" }}>
            This will show 5 toasts in sequence
          </div>
        </div>

        {queue.map((item, index) => (
          <Toast
            key={item.id}
            open={true}
            onClose={() =>
              setQueue((prev) => prev.filter((q) => q.id !== item.id))
            }
            severity="info"
            title={`Step ${index + 1}`}
            description={item.message}
            position="top-center"
            autoHideDuration={2000}
          />
        ))}
      </>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Managing multiple toasts in a queue for sequential notifications.",
      },
    },
  },
}
