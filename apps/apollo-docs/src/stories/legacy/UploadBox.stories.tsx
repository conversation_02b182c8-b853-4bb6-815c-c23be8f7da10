import { useEffect, useState } from "react"
import {
  createTheme,
  ThemeProvider,
  UploadBox,
  type UploadBoxFileState,
} from "@apollo/ui/legacy"
import type { <PERSON>a, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/UploadBox",
  component: UploadBox,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "600px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The UploadBox component provides a drag-and-drop file upload interface with support for single and multiple files, file type restrictions, size limits, and upload progress indicators.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Label for the upload box",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the upload box",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the upload box is disabled",
    },
    multiple: {
      control: { type: "boolean" },
      description: "Whether multiple files can be uploaded",
    },
    required: {
      control: { type: "boolean" },
      description: "Whether file upload is required",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the upload box should take full width",
    },
    fileLimit: {
      control: { type: "number" },
      description: "Maximum number of files that can be uploaded",
    },
    maxFileSizeInBytes: {
      control: { type: "number" },
      description: "Maximum file size in bytes",
    },
  },
} satisfies Meta<typeof UploadBox>

export default meta
type Story = StoryObj<typeof meta>

// Basic upload box
export const Basic: Story = {
  render: () => {
    const [file, setFile] = useState<File | null>(null)

    const handleDelete = () => {
      setFile(null)
    }

    return (
      <UploadBox
        label="Upload Document"
        helperText="Choose a file to upload"
        value={file}
        onUpload={(file) => setFile(file)}
        onDelete={handleDelete}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic single file upload with drag and drop support.",
      },
    },
  },
}

// Multiple files upload
export const MultipleFiles: Story = {
  render: () => {
    const [files, setFiles] = useState<File[]>([])

    const handleUpload = (newFiles: File[]) => {
      setFiles((prev) => [...prev, ...newFiles])
    }

    const handleDelete = (index: number) => {
      setFiles((prev) => prev.filter((_, i) => i !== index))
    }

    return (
      <UploadBox
        multiple
        label="Upload Multiple Files"
        helperText={`Selected ${files.length} file${files.length !== 1 ? "s" : ""}`}
        value={files}
        onUpload={handleUpload}
        onDelete={handleDelete}
        fileLimit={5}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Multiple file upload with file limit and management.",
      },
    },
  },
}

// With file type restrictions
export const FileTypeRestrictions: Story = {
  render: () => {
    const [files, setFiles] = useState<File[]>([])

    const handleUpload = (newFiles: File[]) => {
      setFiles((prev) => [...prev, ...newFiles])
    }

    const handleDelete = (index: number) => {
      setFiles((prev) => prev.filter((_, i) => i !== index))
    }

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Images Only</h4>
          <UploadBox
            multiple
            label="Upload Images"
            helperText="Only JPG, PNG, and GIF files are allowed"
            value={files}
            onUpload={handleUpload}
            onDelete={handleDelete}
            allowedFilesExtension={["jpg", "jpeg", "png", "gif"]}
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Documents Only
          </h4>
          <UploadBox
            label="Upload Document"
            helperText="Only PDF, DOC, and TXT files are allowed"
            allowedFilesExtension={["pdf", "doc", "docx", "txt"]}
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Upload boxes with different file type restrictions.",
      },
    },
  },
}

// With size limits
export const SizeLimits: Story = {
  render: () => {
    const [smallFile, setSmallFile] = useState<File | null>(null)
    const [largeFile, setLargeFile] = useState<File | null>(null)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Small Files (1MB limit)
          </h4>
          <UploadBox
            label="Upload Small File"
            helperText="Maximum file size: 1MB"
            value={smallFile}
            onUpload={(file) => setSmallFile(file)}
            onDelete={() => setSmallFile(null)}
            maxFileSizeInBytes={1024 * 1024} // 1MB
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Large Files (10MB limit)
          </h4>
          <UploadBox
            label="Upload Large File"
            helperText="Maximum file size: 10MB"
            value={largeFile}
            onUpload={(file) => setLargeFile(file)}
            onDelete={() => setLargeFile(null)}
            maxFileSizeInBytes={10 * 1024 * 1024} // 10MB
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Upload boxes with different file size limitations.",
      },
    },
  },
}

// Upload states
export const UploadStates: Story = {
  render: () => {
    const [normalFile, setNormalFile] = useState<File | null>(null)
    const [uploadingFile, setUploadingFile] = useState<File | null>(null)
    const [uploadingState, setUploadingState] =
      useState<UploadBoxFileState | null>(null)
    const [errorFile, setErrorFile] = useState<File | null>(null)

    const handleUploadWithProgress = (file: File) => {
      setUploadingFile(file)
      setUploadingState({
        key: "uploading-file",
        uploading: true,
      })

      // Simulate upload completion after 3 seconds
      setTimeout(() => {
        setUploadingState({
          key: "uploading-file",
          uploading: false,
        })
      }, 3000)
    }

    const handleUploadWithError = (file: File) => {
      setErrorFile(file)
      // Simulate an error state
      setTimeout(() => {
        setErrorFile(null)
      }, 100)
    }

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Normal State
          </h4>
          <UploadBox
            label="Upload File"
            helperText="Normal upload state"
            value={normalFile}
            onUpload={(file) => setNormalFile(file)}
            onDelete={() => setNormalFile(null)}
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Uploading State
          </h4>
          <UploadBox
            label="Upload with Progress"
            helperText="Shows upload progress indicator"
            value={uploadingFile}
            fileState={uploadingState}
            onUpload={handleUploadWithProgress}
            onDelete={() => {
              setUploadingFile(null)
              setUploadingState(null)
            }}
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>
            Disabled State
          </h4>
          <UploadBox
            label="Disabled Upload"
            helperText="Upload is disabled"
            disabled
          />
        </div>

        <div>
          <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Error State</h4>
          <UploadBox
            label="Upload with Error"
            helperText="Upload failed - please try again"
            value={errorFile}
            onUpload={handleUploadWithError}
            onDelete={() => setErrorFile(null)}
            errorMessage="Upload failed due to network error"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Different upload states: normal, uploading, disabled, and error.",
      },
    },
  },
}

// Full width example
export const FullWidth: Story = {
  render: () => {
    const [files, setFiles] = useState<File[]>([])

    const handleUpload = (newFiles: File[]) => {
      setFiles((prev) => [...prev, ...newFiles])
    }

    const handleDelete = (index: number) => {
      setFiles((prev) => prev.filter((_, i) => i !== index))
    }

    return (
      <div style={{ width: "100%", maxWidth: "800px" }}>
        <UploadBox
          multiple
          fullWidth
          label="Full Width Upload"
          helperText="This upload box takes the full width of its container"
          value={files}
          onUpload={handleUpload}
          onDelete={handleDelete}
          fileLimit={10}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Full width upload box that adapts to its container.",
      },
    },
  },
}

// Custom messages and descriptions
export const CustomMessages: Story = {
  render: () => {
    const [files, setFiles] = useState<File[]>([])

    const handleUpload = (newFiles: File[]) => {
      setFiles((prev) => [...prev, ...newFiles])
    }

    const handleDelete = (index: number) => {
      setFiles((prev) => prev.filter((_, i) => i !== index))
    }

    const renderDescription = () => (
      <div style={{ textAlign: "center", padding: "20px" }}>
        <div style={{ fontSize: "48px", marginBottom: "16px" }}>📄</div>
        <h3 style={{ margin: "0 0 8px 0", fontSize: "18px" }}>
          Drop your files here
        </h3>
        <p style={{ margin: "0 0 12px 0", color: "#666" }}>
          or click to browse from your computer
        </p>
        <div style={{ fontSize: "12px", color: "#999" }}>
          Supports: PDF, DOC, XLS, PPT • Max 5MB per file
        </div>
      </div>
    )

    const renderErrorMessage = (state: any) => (
      <div
        style={{
          padding: "12px",
          background: "#fee",
          border: "1px solid #fcc",
          borderRadius: "4px",
          color: "#c33",
        }}
      >
        <strong>Upload Error:</strong>{" "}
        {state.errors?.[0]?.message || "Please check your files and try again."}
      </div>
    )

    return (
      <UploadBox
        multiple
        label="Custom Upload Interface"
        helperText={`${files.length} file${files.length !== 1 ? "s" : ""} selected`}
        value={files}
        onUpload={handleUpload}
        onDelete={handleDelete}
        renderDescription={renderDescription}
        renderErrorMessage={renderErrorMessage}
        allowedFilesExtension={[
          "pdf",
          "doc",
          "docx",
          "xls",
          "xlsx",
          "ppt",
          "pptx",
        ]}
        maxFileSizeInBytes={5 * 1024 * 1024}
      />
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Upload box with custom description and error message rendering.",
      },
    },
  },
}

// Form integration example
export const FormIntegration: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      resume: null as File | null,
      coverLetter: null as File | null,
      portfolio: [] as File[],
    })

    const [submitted, setSubmitted] = useState(false)

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      setSubmitted(true)
      setTimeout(() => setSubmitted(false), 3000)
    }

    const isValid = formData.resume && formData.coverLetter

    return (
      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxWidth: "500px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Job Application</h3>

        <UploadBox
          label="Resume *"
          helperText={
            !formData.resume && submitted
              ? "Resume is required"
              : "Upload your current resume"
          }
          required
          value={formData.resume}
          onUpload={(file) =>
            setFormData((prev) => ({ ...prev, resume: file }))
          }
          onDelete={() => setFormData((prev) => ({ ...prev, resume: null }))}
          allowedFilesExtension={["pdf", "doc", "docx"]}
          maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
          errorMessage={
            !formData.resume && submitted ? "Resume is required" : undefined
          }
        />

        <UploadBox
          label="Cover Letter *"
          helperText={
            !formData.coverLetter && submitted
              ? "Cover letter is required"
              : "Upload your cover letter"
          }
          required
          value={formData.coverLetter}
          onUpload={(file) =>
            setFormData((prev) => ({ ...prev, coverLetter: file }))
          }
          onDelete={() =>
            setFormData((prev) => ({ ...prev, coverLetter: null }))
          }
          allowedFilesExtension={["pdf", "doc", "docx"]}
          maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
          errorMessage={
            !formData.coverLetter && submitted
              ? "Cover letter is required"
              : undefined
          }
        />

        <UploadBox
          multiple
          label="Portfolio (Optional)"
          helperText={`${formData.portfolio.length} portfolio item${formData.portfolio.length !== 1 ? "s" : ""} uploaded`}
          value={formData.portfolio}
          onUpload={(files) =>
            setFormData((prev) => ({
              ...prev,
              portfolio: [...prev.portfolio, ...files],
            }))
          }
          onDelete={(index: number) =>
            setFormData((prev) => ({
              ...prev,
              portfolio: prev.portfolio.filter((_, i) => i !== index),
            }))
          }
          allowedFilesExtension={["pdf", "jpg", "jpeg", "png", "gif"]}
          maxFileSizeInBytes={5 * 1024 * 1024} // 5MB
          fileLimit={5}
        />

        <button
          type="submit"
          disabled={!isValid}
          style={{
            padding: "12px 24px",
            background: isValid ? "#007bff" : "#ccc",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: isValid ? "pointer" : "not-allowed",
            fontSize: "16px",
            marginTop: "8px",
          }}
        >
          Submit Application
        </button>

        {submitted && isValid && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Application submitted successfully!
          </div>
        )}
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete form integration with required and optional file uploads.",
      },
    },
  },
}
