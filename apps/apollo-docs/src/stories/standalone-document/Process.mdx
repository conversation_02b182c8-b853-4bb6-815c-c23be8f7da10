import { Meta } from "@storybook/addon-docs/blocks"

# Design System Process

This document outlines the process for submitting and handling requests for updates within the Apollo Design System project.

<img src="/process/overall.png" alt="Overall Workflow" />

<Meta title="Process: Change⁄Update Request" tags={["new"]} />

<br />
<br />
<br />
<br />

## Update Request Steps

To make team member noticed of the request, please follow these steps:

Any team member of UX/UI can submit a request for updates or changes to the design system. Before the update will be accepted, the request must be reviewed and approved by another UX/UI team member.

        1. **Create the Request Issue**: First of all create an issue at [This Link](https://apollo-issue-apollo-issue.vercel.app/issues/new) with the details of the request.

        <img src="/process/create-issue.png" alt="Create Issue" />

        **Template for request:**

        ```
        ----

        Title: [Short description of the request]
        Description: [Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.]
        Priority:[ Low | Medium | High | Urgent ]
        Due Date: Date
        Review Due Date: Date

        Issue Link: LINK

        ---

        ✅ Approved ❌ Declined

        ```

        2. **Post the detail of changes**: The request should include a summary detailed description of the changes needed, the reason for the change, and any relevant context or examples. Post in the MS Team at `[Design System] Design Library & Issues`

        <img src="/process/post.png" alt="Process Example" />

        3. **Backlog/Planned**: Once the request is approved, it will be added to the project backlog. The team will prioritize the request based on its importance and urgency.

        <img src="/process/approved.png" alt="Approved" />

<br />
<br />
<br />
<br />

## Implementation Review Steps

To make sure that implemented code aligned with Figma Design. We needed the confirmation from the Designer.

1. **Notify the Designer**: Once the implementation is complete, UI-Engineer will notify the Designer in the MS Team at `[Design System] Design Library & Issues` channel with the review request link.

Example: [Chromatic](https://www.chromatic.com/review?appId=68ac0015b0397b0b5a740fcd&number=1&type=unlinked&view=activity)

<img src="/process/process-exam.png" alt="Process Example" />

2. **Set Target Release**: The UI-Engineer will set the target release version in the issue to indicate when the changes are expected to be deployed.

<img src="/process/complete.png" alt="Process Example" />

3. **Release**: Once the changes have been reviewed and approved, they will be released to the design system. After the release, Announce the release in the MS Team.
