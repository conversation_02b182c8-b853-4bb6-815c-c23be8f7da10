{"name": "@apollo/pilot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3031", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/ui": "1.0.0-beta.18", "@codesandbox/sandpack-react": "^2.20.0", "@codesandbox/sandpack-themes": "^2.0.21", "@design-systems/apollo-icons": "workspace:*", "@google/genai": "^1.7.0", "@modelcontextprotocol/sdk": "^1.12.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "0.288.0", "next": "15.3.4", "next-themes": "^0.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^0.0.55", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}