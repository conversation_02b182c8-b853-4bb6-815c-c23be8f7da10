import { NextRequest, NextResponse } from "next/server"
import { SYSTEM_INSTRUCTIONS } from "@/prompts/system-instructions"
import { GoogleGenAI, mcpToTool, ToolListUnion } from "@google/genai"
import { Client } from "@modelcontextprotocol/sdk/client/index.js"
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js"

import { codeUpdateFunctions, executeToolFunction } from "@/lib/ai-tools"

const ai = new GoogleGenAI({
  apiKey: process.env.GOOGLE_AI_API_KEY,
})

// Create MCP client factory function
async function createMcpClient(): Promise<Client | null> {
  try {
    const serverParams = new StdioClientTransport({
      command:
        "/Users/<USER>/Documents/workspace/apollo-design-system/apps/docs/node_modules/.bin/cj-apollo",
      args: ["run", "mcp"],
    })

    const client = new Client({
      name: "apollo-mcp",
      version: "1.0.0",
    })

    await client.connect(serverParams)
    console.log("[MCP] Client connected successfully")
    return client
  } catch (error) {
    console.error("[MCP] Failed to connect client:", error)
    return null
  }
}

// Simple in-memory store for chat sessions
interface ChatMessage {
  role: "user" | "model"
  parts: { text: string }[]
}

const chatSessions = new Map<string, ChatMessage[]>()

// Generate a simple session ID
function generateSessionId(): string {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  )
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, sessionId, history = [], currentFiles = {} } = body

    if (!message) {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      )
    }

    // Handle session-based chat history
    let currentSessionId = sessionId
    let chatHistory: ChatMessage[] = []

    if (currentSessionId && chatSessions.has(currentSessionId)) {
      // Use existing session history
      chatHistory = chatSessions.get(currentSessionId)!
    } else if (history.length > 0) {
      // Use provided history
      chatHistory = history
      currentSessionId = currentSessionId || generateSessionId()
    } else {
      // Start new session with default history
      currentSessionId = generateSessionId()
      chatHistory = [
        {
          role: "user",
          parts: [{ text: "Hello" }],
        },
        {
          role: "model",
          parts: [{ text: "Great to meet you. What would you like to know?" }],
        },
      ]
    }

    // Add user message to history
    chatHistory.push({
      role: "user",
      parts: [{ text: message }],
    })

    // Check if streaming is requested - only allow streaming requests
    const isStreaming = request.headers.get("accept")?.includes("text/stream")

    if (!isStreaming) {
      return NextResponse.json(
        {
          error:
            "Only streaming requests are supported. Please set Accept header to 'text/stream'.",
        },
        { status: 400 }
      )
    }

    try {
      // Return streaming response with continuation logic
      const encoder = new TextEncoder()
      const readableStream = new ReadableStream({
        async start(controller) {
          let mcpClient: Client | null = null

          try {
            let iteration = 0
            const currentChatHistory = [...chatHistory]
            let consecutiveNoActionCount = 0 // Track iterations without meaningful action
            let totalSuccessfulActions = 0 // Track total successful function calls

            // Maintain live file state that updates as AI makes changes
            const liveFileState = { ...currentFiles }
            console.log(
              "[File State] Initial files:",
              Object.keys(liveFileState)
            )

            // Initialize MCP client
            mcpClient = await createMcpClient()
            if (!mcpClient) {
              console.warn(
                "[MCP] Could not connect to MCP server, proceeding without MCP tools"
              )
            }

            while (true) {
              // No arbitrary limit - let logic determine when to stop
              iteration++
              console.log(
                `[AI Iteration ${iteration}] Total successful actions: ${totalSuccessfulActions}`
              )

              // Safety check - prevent runaway execution (generous limit based on successful actions)
              if (
                iteration > 50 ||
                (iteration > 10 && totalSuccessfulActions === 0)
              ) {
                console.log(
                  `[AI] Emergency stop at iteration ${iteration} - possible infinite loop`
                )
                const emergencyData =
                  JSON.stringify({
                    text: "\n\n*Task execution stopped - taking too long. Please try breaking down your request into smaller parts.*",
                  }) + "\n"
                controller.enqueue(encoder.encode(emergencyData))
                break
              }

              // await client.connect(serverParams)

              // Prepare tools array - include MCP tools if available
              const tools = [
                {
                  functionDeclarations: codeUpdateFunctions,
                },
              ] as ToolListUnion

              // Add MCP tools if client is connected
              if (mcpClient) {
                try {
                  const mcpTool = mcpToTool(mcpClient)
                  tools.push(mcpTool) // Add MCP tools
                  console.log("[MCP] Added MCP tools to AI model")
                } catch (error) {
                  console.error("[MCP] Error adding MCP tools:", error)
                }
              }

              // Generate content with function calling support
              const response = await ai.models.generateContent({
                model: "gemini-2.5-flash",
                contents: currentChatHistory,
                config: {
                  tools,
                  automaticFunctionCalling: {
                    disable: true,
                  },
                  systemInstruction: SYSTEM_INSTRUCTIONS,
                },
              })

              const candidate = response.candidates?.[0]
              if (!candidate?.content?.parts) {
                const errorData =
                  JSON.stringify({
                    text: "No response from AI",
                    error: true,
                  }) + "\n"
                controller.enqueue(encoder.encode(errorData))
                break
              }

              let hasText = false
              let hasFunctionCalls = false
              const functionResults: {
                success: boolean
                action: string
                message?: string
                error?: string
              }[] = []

              // Process the response parts
              for (const part of candidate.content.parts) {
                // Stream text response
                if (part.text) {
                  hasText = true
                  const textData = JSON.stringify({ text: part.text }) + "\n"
                  controller.enqueue(encoder.encode(textData))
                }

                // Stream thought
                if (part.thought) {
                  const thoughtData =
                    JSON.stringify({
                      thought: part.thought,
                    }) + "\n"
                  controller.enqueue(encoder.encode(thoughtData))
                }

                // Execute function calls
                if (part.functionCall) {
                  hasFunctionCalls = true
                  const { name, args } = part.functionCall
                  console.log("[AI Function Call]", name, args)

                  if (!name || !args) {
                    const errorResult = {
                      success: false,
                      action: name || "unknown",
                      error: `Function called without ${!name ? "name" : "arguments"}`,
                    }
                    functionResults.push(errorResult)
                    const errorData =
                      JSON.stringify({
                        function_result: errorResult,
                        text: errorResult.error,
                      }) + "\n"
                    controller.enqueue(encoder.encode(errorData))
                    continue
                  }

                  try {
                    const result = await Promise.resolve(
                      executeToolFunction(
                        name,
                        args,
                        liveFileState,
                        currentSessionId
                      )
                    )
                    console.log("[AI Function Result]", result)
                    functionResults.push(result)

                    // Update live file state when files are modified
                    if (result.success && result.data) {
                      if (result.action === "update_app_code") {
                        // updateAppCode already normalizes the filename with leading slash
                        const filename = result.data.filename as string
                        liveFileState[filename] = result.data.code as string
                        console.log(`[File State] Updated ${filename}`)
                      } else if (result.action === "add_component_file") {
                        // Normalize filename to match client-side expectations
                        const rawFilename = result.data.filename as string
                        const filename = rawFilename.startsWith("/")
                          ? rawFilename
                          : `/${rawFilename}`
                        liveFileState[filename] = result.data.code as string
                        console.log(`[File State] Added ${filename}`)
                      } else if (result.action === "update_index_file") {
                        liveFileState["index.tsx"] = result.data.code as string
                        console.log(`[File State] Updated index.tsx`)
                      }
                    }

                    const resultData =
                      JSON.stringify({
                        function_result: result,
                        text:
                          result.message ||
                          (result.success ? "Complete" : result.error || ""),
                      }) + "\n"
                    controller.enqueue(encoder.encode(resultData))
                  } catch (error) {
                    const errorResult = {
                      success: false,
                      action: name,
                      error: `Function execution failed: ${error}`,
                    }
                    functionResults.push(errorResult)
                    const errorData =
                      JSON.stringify({
                        function_result: errorResult,
                        text: errorResult.error,
                      }) + "\n"
                    controller.enqueue(encoder.encode(errorData))
                  }
                }
              }

              // Add AI response to chat history
              currentChatHistory.push({
                role: "model",
                parts: candidate.content.parts.map(
                  (part: { text?: string }) => ({
                    text: part.text || "",
                  })
                ),
              })

              // Determine if we should continue
              let shouldContinue = false

              if (hasFunctionCalls && functionResults.length > 0) {
                // If there were function calls, check if we should continue
                const successfulResults = functionResults.filter(
                  (r) => r.success
                )
                const failedResults = functionResults.filter((r) => !r.success)

                // Update counters
                totalSuccessfulActions += successfulResults.length

                if (successfulResults.length > 0) {
                  consecutiveNoActionCount = 0 // Reset counter on successful action
                } else {
                  consecutiveNoActionCount++
                }

                // Create summary of function results
                const resultsSummary = functionResults
                  .map((result) => {
                    if (result.success) {
                      return `✓ ${result.action}: ${result.message || "Success"}`
                    } else {
                      return `✗ ${result.action}: ${result.error || "Failed"}`
                    }
                  })
                  .join("\n")

                // Continue if we have successful operations and should keep working
                if (successfulResults.length > 0) {
                  shouldContinue = true

                  // Add continuation prompt
                  currentChatHistory.push({
                    role: "user",
                    parts: [
                      {
                        text: `Function results:\n${resultsSummary}\n\nContinue with the implementation. If the task is complete, respond with just text (no function calls). If more work is needed, use the appropriate tools to continue.`,
                      },
                    ],
                  })
                } else if (failedResults.length > 0) {
                  // If all failed, try to continue with error handling but limit attempts
                  if (consecutiveNoActionCount < 3) {
                    shouldContinue = true

                    currentChatHistory.push({
                      role: "user",
                      parts: [
                        {
                          text: `Some operations failed:\n${resultsSummary}\n\nPlease analyze the errors and try a different approach to complete the task.`,
                        },
                      ],
                    })
                  } else {
                    console.log(
                      `[AI] Stopping - too many consecutive failures (${consecutiveNoActionCount})`
                    )
                    const failureData =
                      JSON.stringify({
                        text: "\n\n*Task execution stopped due to consecutive failures. Please check your request and try again.*",
                      }) + "\n"
                    controller.enqueue(encoder.encode(failureData))
                    shouldContinue = false
                  }
                }
              } else if (hasText && !hasFunctionCalls) {
                // If only text response, check if task seems complete
                const response_text = candidate.content.parts
                  .filter((part) => part.text)
                  .map((part) => part.text)
                  .join("")

                // Check for completion indicators
                const completionIndicators = [
                  "task completed",
                  "implementation complete",
                  "all done",
                  "finished",
                  "complete",
                  "ready",
                  "successfully implemented",
                ]

                const seemsComplete = completionIndicators.some((indicator) =>
                  response_text.toLowerCase().includes(indicator)
                )

                if (seemsComplete) {
                  console.log(`[AI] Task appears complete based on AI response`)
                  shouldContinue = false
                } else {
                  consecutiveNoActionCount++

                  // Continue only if we haven't had too many no-action iterations
                  if (
                    consecutiveNoActionCount < 3 &&
                    totalSuccessfulActions > 0
                  ) {
                    shouldContinue = true
                    currentChatHistory.push({
                      role: "user",
                      parts: [
                        {
                          text: "Continue with the implementation using the appropriate tools. Take action to complete the user's request.",
                        },
                      ],
                    })
                  } else if (
                    totalSuccessfulActions === 0 &&
                    consecutiveNoActionCount < 2
                  ) {
                    // Give AI a chance to start working even if no actions yet
                    shouldContinue = true
                    currentChatHistory.push({
                      role: "user",
                      parts: [
                        {
                          text: "Please start implementing the user's request using the available tools. Take concrete actions to fulfill the request.",
                        },
                      ],
                    })
                  } else {
                    console.log(
                      `[AI] Stopping - too many iterations without meaningful action`
                    )
                    shouldContinue = false
                  }
                }
              }

              // Stop if we shouldn't continue
              if (!shouldContinue) {
                console.log(
                  `[AI] Stopping at iteration ${iteration} - task appears complete or stopping criteria met`
                )
                break
              }
            }

            // Update the original chat history with the final state
            chatHistory = currentChatHistory
            chatSessions.set(currentSessionId, currentChatHistory)

            // Send session ID
            const sessionData =
              JSON.stringify({ sessionId: currentSessionId }) + "\n"
            controller.enqueue(encoder.encode(sessionData))

            controller.close()
          } catch (error) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            if ((error as any)?.code === 429) {
              const rateLimitErrorData =
                JSON.stringify({
                  text: "Rate limit exceeded. Please try again later tomorrow.",
                  error: true,
                }) + "\n"
              controller.enqueue(encoder.encode(rateLimitErrorData))
              controller.close()
              return
            }
            console.error("AI API Error:", error)
            const errorData =
              JSON.stringify({
                text: "Sorry, I encountered an error. Please try again.",
                error: true,
              }) + "\n"
            controller.enqueue(encoder.encode(errorData))
            controller.close()
          } finally {
            // Clean up MCP client connection
            if (mcpClient) {
              try {
                await mcpClient.close()
                console.log("[MCP] Client connection closed")
              } catch (error) {
                console.error("[MCP] Error closing client:", error)
              }
            }
          }
        },
      })

      // await client.close()

      return new Response(readableStream, {
        headers: {
          "Content-Type": "text/stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      })
    } catch (error) {
      console.error("AI API Error:", error)

      const encoder = new TextEncoder()
      const readableStream = new ReadableStream({
        start(controller) {
          const errorData =
            JSON.stringify({
              text: "Sorry, I encountered an error. Please try again.",
              error: true,
            }) + "\n"
          controller.enqueue(encoder.encode(errorData))
          controller.close()
        },
      })

      return new Response(readableStream, {
        headers: {
          "Content-Type": "text/stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      })
    }
  } catch (error) {
    console.error("AI API Error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Optional: Add GET method for health check
export async function GET() {
  return NextResponse.json({
    status: "AI API is running",
    model: "gemini-2.5-flash",
  })
}
