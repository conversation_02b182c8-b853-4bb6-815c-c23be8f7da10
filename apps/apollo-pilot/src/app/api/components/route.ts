import { existsSync, readdirSync, readFileSync, statSync } from "fs"
import { join } from "path"
import { NextResponse } from "next/server"

interface ComponentInfo {
  name: string
  componentName: string
  category: string
  path: string
  hasExamples: boolean
  exampleCount: number
  description: string
}

export async function GET() {
  try {
    // Get the documentation components directory path
    const docsPath = join(
      process.cwd(),
      "..",
      "..",
      "apps",
      "docs",
      "docs",
      "Components"
    )

    if (!existsSync(docsPath)) {
      return NextResponse.json(
        { error: "Components documentation directory not found" },
        { status: 404 }
      )
    }

    const components = await scanComponentsDirectory(docsPath)

    return NextResponse.json({
      components,
      totalCount: components.length,
      categories: [...new Set(components.map((c) => c.category))].sort(),
      success: true,
    })
  } catch (error) {
    console.error("Error scanning components:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

async function scanComponentsDirectory(
  docsPath: string
): Promise<ComponentInfo[]> {
  const components: ComponentInfo[] = []

  try {
    // Read all category directories
    const categories = readdirSync(docsPath).filter((item) => {
      const itemPath = join(docsPath, item)
      return statSync(itemPath).isDirectory() && !item.startsWith(".")
    })

    for (const category of categories) {
      const categoryPath = join(docsPath, category)

      // Read all component directories within each category
      const componentDirs = readdirSync(categoryPath).filter((item) => {
        const itemPath = join(categoryPath, item)
        return (
          statSync(itemPath).isDirectory() &&
          !item.startsWith(".") &&
          !item.startsWith("_")
        )
      })

      for (const componentDir of componentDirs) {
        const componentPath = join(categoryPath, componentDir)
        const indexMdxPath = join(componentPath, "index.mdx")

        // Check if component has index.mdx file
        if (existsSync(indexMdxPath)) {
          const examplesDir = join(componentPath, "examples")
          let exampleCount = 0
          let hasExamples = false

          // Check for examples directory and count example files
          if (existsSync(examplesDir)) {
            const exampleFiles = readdirSync(examplesDir).filter((file) =>
              /\.(tsx?|jsx?)$/.test(file)
            )
            exampleCount = exampleFiles.length
            hasExamples = exampleCount > 0
          }

          // Extract description from markdown file
          const description = extractComponentDescription(indexMdxPath)

          components.push({
            name: componentDir.toLowerCase(),
            componentName: componentDir,
            category: category,
            path: `${category}/${componentDir}`,
            hasExamples,
            exampleCount,
            description,
          })
        }
      }
    }

    // Sort components by category, then by name
    return components.sort((a, b) => {
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category)
      }
      return a.name.localeCompare(b.name)
    })
  } catch (error) {
    console.error("Error scanning components directory:", error)
    return []
  }
}

function extractComponentDescription(markdownPath: string): string {
  try {
    const content = readFileSync(markdownPath, "utf-8")

    // Look for the first paragraph after the frontmatter and imports
    const lines = content.split("\n")
    let startIndex = 0

    // Skip frontmatter (between --- and ---)
    if (lines[0]?.trim() === "---") {
      for (let i = 1; i < lines.length; i++) {
        if (lines[i]?.trim() === "---") {
          startIndex = i + 1
          break
        }
      }
    }

    // Skip import statements and empty lines
    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i]?.trim()
      if (line && !line.startsWith("import ") && !line.startsWith("<")) {
        // Found the first content line, look for description
        const descriptionMatch = line.match(/^([^.!?]*[.!?])/)
        if (descriptionMatch) {
          return descriptionMatch[1].trim()
        }
        // If no sentence ending, take the whole line but limit length
        return line.length > 150 ? line.substring(0, 150) + "..." : line
      }
    }

    // Fallback: extract from title if no description found
    const titleMatch = content.match(/title:\s*["']?([^"'\n]+)["']?/)
    if (titleMatch) {
      return `${titleMatch[1]} component`
    }

    return "No description available"
  } catch (error) {
    console.error(`Error extracting description from ${markdownPath}:`, error)
    return "No description available"
  }
}
