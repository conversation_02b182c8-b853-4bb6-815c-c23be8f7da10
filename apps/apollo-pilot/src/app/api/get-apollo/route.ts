import fs from "fs"
import path from "path"
import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // // Check if the request comes from the same origin
    const origin = request.headers.get("origin")
    const referer = request.headers.get("referer")
    const host = request.headers.get("host")

    // Allow requests from same origin or direct access (no origin/referer)
    const isValidOrigin =
      !origin ||
      origin === `${request.nextUrl.protocol}//${host}` ||
      (referer && new URL(referer).host === host)

    if (!isValidOrigin) {
      return NextResponse.json(
        {
          success: false,
          error: "Access denied",
        },
        { status: 403 }
      )
    }
    // Get the workspace root directory (go up from apps/apollo-pilot to workspace root)
    const workspaceRoot = path.resolve(process.cwd(), "../..")

    // Define paths to the files in the @apollo/ui package
    const jsFilePath = path.join(
      workspaceRoot,
      "packages/apollo-ui/dist/index.js"
    )
    const cssFilePath = path.join(
      workspaceRoot,
      "packages/apollo-ui/dist/index.css"
    )

    // Read both files as strings
    const jsContent = fs.readFileSync(jsFilePath, "utf-8")
    const cssContent = fs.readFileSync(cssFilePath, "utf-8")

    // Return both files as JSON response
    return NextResponse.json({
      success: true,
      files: {
        javascript: jsContent,
        css: cssContent,
      },
    })
  } catch (error) {
    console.error("Error reading Apollo UI files:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Failed to read Apollo UI files",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      {
        status: 500,
      }
    )
  }
}
