import { existsSync, readdirSync, readFileSync, statSync } from "fs"
import { join } from "path"
import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const component = searchParams.get("component")

    if (!component) {
      return NextResponse.json(
        { error: "Component parameter is required" },
        { status: 400 }
      )
    }

    // Get the documentation markdown file path
    const docsPath = join(
      process.cwd(),
      "..",
      "..",
      "apps",
      "docs",
      "docs",
      "Components"
    )
    const markdownPath = findComponentMarkdown(docsPath, component)

    if (!markdownPath || !existsSync(markdownPath)) {
      return NextResponse.json(
        { error: `Documentation not found for component: ${component}` },
        { status: 404 }
      )
    }

    // Read the markdown content
    const markdownContent = readFileSync(markdownPath, "utf-8")

    // Process the markdown to replace ComponentPreview<PERSON> with actual code
    const processedMarkdown = await processComponentPreviews(
      markdownContent,
      markdownPath
    )

    return NextResponse.json({
      component,
      markdown: processedMarkdown,
      success: true,
    })
  } catch (error) {
    console.error("Error processing documentation:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

function findComponentMarkdown(
  docsPath: string,
  component: string
): string | null {
  // Get all category directories dynamically
  const categories = ["data-display", "Inputs", "feedback", "layout"]

  // Try both lowercase and capitalized component names
  const componentVariations = [
    component.toLowerCase(),
    component.charAt(0).toUpperCase() + component.slice(1),
    component.toUpperCase(),
    component,
  ]

  const possiblePaths: string[] = []

  // Generate all possible combinations dynamically
  for (const category of categories) {
    for (const componentName of componentVariations) {
      possiblePaths.push(join(docsPath, category, componentName, "index.mdx"))
    }
  }

  for (const path of possiblePaths) {
    if (existsSync(path)) {
      return path
    }
  }

  // Fallback: scan all directories if not found in common patterns
  try {
    for (const category of categories) {
      const categoryPath = join(docsPath, category)
      if (existsSync(categoryPath)) {
        const componentDirs = readdirSync(categoryPath).filter(
          (dir: string) => {
            const dirPath = join(categoryPath, dir)
            return statSync(dirPath).isDirectory()
          }
        )

        for (const dir of componentDirs) {
          if (dir.toLowerCase() === component.toLowerCase()) {
            const indexPath = join(categoryPath, dir, "index.mdx")
            if (existsSync(indexPath)) {
              return indexPath
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error during fallback search:", error)
  }

  return null
}

async function processComponentPreviews(
  markdownContent: string,
  markdownPath: string
): Promise<string> {
  let processedContent = markdownContent

  // Remove frontmatter (content between --- lines at the beginning)
  processedContent = processedContent.replace(/^---[\s\S]*?---\n/, "")

  // Remove import statements
  processedContent = processedContent.replace(/^import\s+.*$/gm, "")

  // Remove empty lines at the beginning
  processedContent = processedContent.replace(/^\s*\n+/, "")

  // Pattern to match ComponentPreviewUI tags
  const componentPreviewRegex =
    /<ComponentPreviewUI\s+name="([^"]+)"\s+code=\{([^}]+)\}\s*\/>/g

  let match

  while ((match = componentPreviewRegex.exec(markdownContent)) !== null) {
    const [fullMatch, exampleName] = match

    try {
      // Get the directory containing the markdown file
      const markdownDir = markdownPath.replace("/index.mdx", "")
      const examplesDir = join(markdownDir, "examples")

      // Try to find the example file
      const possibleExtensions = [".tsx", ".ts", ".jsx", ".js"]
      let exampleContent = ""

      for (const ext of possibleExtensions) {
        const examplePath = join(examplesDir, `${exampleName}${ext}`)
        if (existsSync(examplePath)) {
          exampleContent = readFileSync(examplePath, "utf-8")
          break
        }
      }

      if (exampleContent) {
        // Replace the ComponentPreviewUI tag with a code block
        const codeBlock = `\`\`\`tsx\n${exampleContent}\`\`\``
        processedContent = processedContent.replace(fullMatch, codeBlock)
      } else {
        console.warn(`Example file not found for: ${exampleName}`)
        // Keep the original tag if file not found
      }
    } catch (error) {
      console.error(`Error processing example ${exampleName}:`, error)
      // Keep the original tag if there's an error
    }
  }

  return processedContent
}
