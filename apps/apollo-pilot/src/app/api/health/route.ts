import { NextResponse } from "next/server"

export async function GET() {
  try {
    // You can add more health checks here if needed
    // For example, check database connectivity, external services, etc.

    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "1.0.0",
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Health check failed",
      },
      { status: 500 }
    )
  }
}
