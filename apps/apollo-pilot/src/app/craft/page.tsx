"use client"

import { useCallback, useEffect, useMemo, useState } from "react"

import { CodePreview } from "@/components/ui/code-preview"
import { PanelLayout } from "@/components/ui/panel-layout"

interface ChatMessage {
  id: string
  text: string
  side?: "left" | "right"
  flat?: boolean
  isLoading?: boolean
  isToolExecution?: boolean
  isThought?: boolean
  isExecuting?: boolean
}

interface FunctionResult {
  success: boolean
  action: string
  data?: Record<string, unknown>
  message?: string
  error?: string
}

interface DocumentItem {
  id: string
  title: string
  content: string
  isActive?: boolean
}

export default function CraftPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [activeFile, setActiveFile] = useState<string>("/App.tsx")
  const [activeTab, setActiveTab] = useState<"code" | "other" | "document">(
    "code"
  )
  const [hasCodeUpdates, setHasCodeUpdates] = useState(false)
  const [documentTabs, setDocumentTabs] = useState<DocumentItem[]>([])
  const [activeDocumentId, setActiveDocumentId] = useState<string | null>(null)

  const [hasDocumentUpdates, setHasDocumentUpdates] = useState(false)
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false)

  // Debug useEffect to track activeTab changes
  useEffect(() => {
    console.log("activeTab changed to:", activeTab)
    console.log(
      "documentTabs:",
      documentTabs.map((tab) => ({ id: tab.id, title: tab.title }))
    )
    console.log("activeDocumentId:", activeDocumentId)
  }, [activeTab, documentTabs, activeDocumentId])

  // Fetch component documentation from API
  const fetchComponentDocumentation = useCallback(
    async (componentNames: string[], shouldSwitchTab: boolean = true) => {
      console.log("fetchComponentDocumentation called with:", {
        componentNames,
        shouldSwitchTab,
        currentActiveTab: activeTab,
      })
      setIsLoadingDocuments(true)

      try {
        const documentPromises = componentNames.map(async (componentName) => {
          console.log(`Fetching documentation for: ${componentName}`)
          const response = await fetch(
            `/api/get-document?component=${encodeURIComponent(componentName)}`
          )
          if (!response.ok) {
            throw new Error(
              `Failed to fetch documentation for ${componentName}`
            )
          }
          const result = await response.json()
          console.log(
            `Successfully fetched documentation for: ${componentName}`
          )
          return {
            id: componentName,
            title: componentName,
            content: result.markdown,
            isActive: false,
          }
        })

        const newDocuments = await Promise.all(documentPromises)
        console.log(
          "All documents fetched:",
          newDocuments.map((d) => ({
            id: d.id,
            title: d.title,
            contentLength: d.content.length,
          }))
        )

        // Update document tabs, keeping existing tabs and adding/updating new ones
        setDocumentTabs((prevTabs) => {
          const updatedTabs = [...prevTabs]

          newDocuments.forEach((newDoc) => {
            const existingIndex = updatedTabs.findIndex(
              (tab) => tab.id === newDoc.id
            )
            if (existingIndex >= 0) {
              // Update existing tab
              updatedTabs[existingIndex] = { ...newDoc, isActive: false }
            } else {
              // Add new tab
              updatedTabs.push({ ...newDoc, isActive: false })
            }
          })

          return updatedTabs
        })

        // Set the first new document as active
        if (newDocuments.length > 0) {
          setActiveDocumentId(newDocuments[0].id)
          console.log("Set active document ID to:", newDocuments[0].id)
        }

        // If switchToDocTab is true (default), switch to documentation tab
        if (shouldSwitchTab) {
          console.log("Switching to document tab")
          setActiveTab("document")
          setHasDocumentUpdates(false) // Clear notification since we're switching
        } else {
          console.log("Not switching tabs, adding notification dot")
          // Show notification dot if not switching tabs
          if (activeTab !== "document") {
            setHasDocumentUpdates(true)
          }
        }
      } catch (error) {
        console.error("Error fetching component documentation:", error)
        // Create error document
        const errorDocument: DocumentItem = {
          id: "error",
          title: "Error",
          content: `Failed to fetch documentation for components: ${componentNames.join(", ")}`,
          isActive: false,
        }

        // Add error tab
        setDocumentTabs((prevTabs) => {
          const existingIndex = prevTabs.findIndex((tab) => tab.id === "error")
          if (existingIndex >= 0) {
            // Update existing error tab
            const updatedTabs = [...prevTabs]
            updatedTabs[existingIndex] = errorDocument
            return updatedTabs
          } else {
            // Add new error tab
            return [...prevTabs, errorDocument]
          }
        })

        setActiveDocumentId("error")
        console.log("Set active document ID to: error")

        // Still switch to document tab to show the error
        if (shouldSwitchTab) {
          console.log("Switching to document tab (error case)")
          setActiveTab("document")
          setHasDocumentUpdates(false)
        }
      } finally {
        console.log("Setting isLoadingDocuments to false")
        setIsLoadingDocuments(false)
      }
    },
    [activeTab]
  )

  // Code preview state
  const [codeFiles, setCodeFiles] = useState<Record<string, string>>({
    "/App.tsx": `
import { Button } from '@apollo/ui'
export default function App() {
  return <Button>Hello Apollo UI 234</Button>
}
    `,
    "index.tsx": `import React, { StrictMode } from "react"
import { createRoot } from "react-dom/client"

// @ts-ignore
import App from "./App"
import { ThemeProvider } from "@apollo/ui"
import "@apollo/ui/style.css"
const root = createRoot(document.getElementById("root"))
root.render(
  <StrictMode>
      <ThemeProvider>
        <App />
      </ThemeProvider>
  </StrictMode>
)`,
  })

  // Handle function results from AI
  const handleFunctionResult = useCallback(
    (result: FunctionResult) => {
      // Handle failed function calls by logging them and potentially showing user feedback
      if (!result.success) {
        // For component-related errors, create a special error document
        if (result.action === "get_component_documentation" && result.error) {
          const errorDocument: DocumentItem = {
            id: "component-error",
            title: "Component Error",
            content: `## Component Not Found\n\n${result.error}\n\nPlease use the available components from the Apollo Design System.`,
            isActive: false,
          }

          // Add error tab
          setDocumentTabs((prevTabs) => {
            const existingIndex = prevTabs.findIndex(
              (tab) => tab.id === "component-error"
            )
            if (existingIndex >= 0) {
              // Update existing error tab
              const updatedTabs = [...prevTabs]
              updatedTabs[existingIndex] = errorDocument
              return updatedTabs
            } else {
              // Add new error tab
              return [...prevTabs, errorDocument]
            }
          })

          setActiveDocumentId("component-error")
          // Switch to document tab to show the error
          setActiveTab("document")
          setHasDocumentUpdates(false)
        }

        return // Don't process failed results further
      }

      switch (result.action) {
        case "update_app_code":
          const filename = (result.data?.filename as string) || "/App.tsx"
          setCodeFiles((prev) => ({
            ...prev,
            [filename]: result.data?.code as string,
          }))
          // Set the updated file as active to show the changes
          setActiveFile(filename)
          // Auto-switch to code tab for code-related actions
          setActiveTab("code")
          setHasCodeUpdates(false) // Clear notification since we're switching
          break
        case "add_component_file":
          setCodeFiles((prev) => ({
            ...prev,
            [`/${result.data?.filename as string}`]: result.data
              ?.code as string,
          }))
          // Automatically switch to the newly created file
          if (result.data?.filename) {
            const filename = result.data.filename as string
            const normalizedFilename = filename.startsWith("/")
              ? filename
              : `/${filename}`
            setActiveFile(normalizedFilename)
          }
          // Auto-switch to code tab for code-related actions
          setActiveTab("code")
          setHasCodeUpdates(false) // Clear notification since we're switching
          break
        case "update_index_file":
          setCodeFiles((prev) => ({
            ...prev,
            "index.tsx": result.data?.code as string,
          }))
          // Auto-switch to code tab for code-related actions
          setActiveTab("code")
          setHasCodeUpdates(false) // Clear notification since we're switching
          break
        case "read_current_code":
          // Set the active file to show in the preview tab
          if (result.data?.filename) {
            const filename = result.data.filename as string
            // Ensure the filename starts with "/" for consistency
            const normalizedFilename = filename.startsWith("/")
              ? filename
              : `/${filename}`
            setActiveFile(normalizedFilename)
          }
          break
        case "list_project_files":
          // Handle project files listing - this is mainly for AI context
          // Could potentially show this information in the UI if needed
          console.log("[Project Files]", result.data?.files)
          break
        case "get_components_list":
          // Handle components list - this is not a component document, so don't show in document tab
          if (result.data?.components) {
            const componentsData = result.data.components as Array<{
              name: string
              category: string
              description: string
            }>

            const componentsList = componentsData
              .map(
                (comp) =>
                  `- **${comp.name}** (${comp.category}): ${comp.description}`
              )
              .join("\n")

            // Log the components list for debugging but don't add to document tabs
            console.log("[Available Components]", componentsList)

            // Log caching info if message indicates cached result
            if (result.message?.includes("(cached)")) {
              console.log(
                "[Components Cache] Using cached component list - no API call made"
              )
            } else {
              console.log(
                "[Components Cache] Fresh component list fetched and cached"
              )
            }
          }
          break
        case "get_component_documentation":
          // Handle single component documentation - show it to user
          if (result.data?.component && result.data?.markdown) {
            const componentDoc: DocumentItem = {
              id: result.data.component as string,
              title: result.data.component as string,
              content: result.data.markdown as string,
              isActive: false,
            }

            // Add component documentation tab
            setDocumentTabs((prevTabs) => {
              const existingIndex = prevTabs.findIndex(
                (tab) => tab.id === componentDoc.id
              )
              if (existingIndex >= 0) {
                // Update existing tab
                const updatedTabs = [...prevTabs]
                updatedTabs[existingIndex] = componentDoc
                return updatedTabs
              } else {
                // Add new tab
                return [...prevTabs, componentDoc]
              }
            })

            setActiveDocumentId(componentDoc.id)
            // Switch to document tab to show the documentation
            setActiveTab("document")
            setHasDocumentUpdates(false)
          }
          break
        case "show_documentation":
          console.log(
            "show_documentation triggered with result.data:",
            result.data
          )
          // Handle documentation data - only show component documentation in document tab
          if (result.data?.documents && Array.isArray(result.data.documents)) {
            const documentItems: DocumentItem[] = result.data.documents.map(
              (item: any) => ({
                id: item.id as string,
                title: item.title as string,
                content: item.content as string,
                isActive: false,
              })
            )

            // Filter to only include component documentation (exclude general docs, tutorials, etc.)
            const componentDocuments = documentItems.filter((doc) => {
              // Only include documents that appear to be component documentation
              // This can be determined by the content structure or naming convention
              const isComponentDoc =
                doc.content.includes("## Props") ||
                doc.content.includes("## API") ||
                doc.content.includes("## Component") ||
                doc.content.includes("### Props") ||
                doc.id.match(/^[A-Z][a-zA-Z]*$/) // Component names typically start with capital letter

              return isComponentDoc
            })

            if (componentDocuments.length > 0) {
              // Extract component names to fetch documentation
              const componentNames = componentDocuments.map((doc) => doc.id)
              console.log("Component names to fetch:", componentNames)

              // Always switch to document tab when show_documentation is called with component docs
              fetchComponentDocumentation(componentNames, true)
            } else {
              console.log(
                "No component documentation found, not updating document tab"
              )
            }
          } else {
            console.warn("Invalid document data in result.data:", result.data)
            console.warn("Expected result.data.documents to be an array")
          }
          break
        default:
          // Log unhandled actions for debugging
          console.warn("[Unhandled Function Result]", result.action, result)
          break
      }
    },
    [activeTab, fetchComponentDocumentation]
  )

  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      text: inputValue,
      side: "right",
    }

    // Add user message to chat
    setMessages((prev) => [...prev, userMessage])
    const currentMessage = inputValue
    setInputValue("")
    setIsLoading(true)

    // Function to make AI request with current file state
    const makeAIRequest = async (
      message: string,
      isFollowUp: boolean = false
    ) => {
      let currentMessageId = ""

      try {
        const response = await fetch("/api/ai/codegen", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "text/stream",
          },
          body: JSON.stringify({
            message: message,
            sessionId: sessionId,
            currentFiles: codeFiles, // Always use latest codeFiles state
          }),
        })

        if (!response.ok) {
          throw new Error("Failed to get response")
        }

        const reader = response.body?.getReader()
        if (!reader) {
          throw new Error("No response body")
        }

        let aiResponseText = ""
        let receivedSessionId = ""
        let hasToolExecuted = false
        let shouldContinue = false

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = new TextDecoder().decode(value)
          const lines = chunk.split("\n").filter((line) => line.trim())

          for (const line of lines) {
            try {
              const data = JSON.parse(line)

              if (data.text && !data.function_result) {
                console.log("data...", data)
                // If tools have been executed, create a new message bubble for new text
                if (hasToolExecuted) {
                  const newMessageId = `ai-${Date.now()}`
                  const newMessage: ChatMessage = {
                    id: newMessageId,
                    text: data.text,
                    side: "left",
                    flat: false, // New message after tool execution should not be flat
                    isLoading: false,
                  }
                  setMessages((prev) => [...prev, newMessage])
                  currentMessageId = newMessageId
                  aiResponseText = data.text

                  hasToolExecuted = false
                } else {
                  // If no current message exists, create the first AI response message
                  if (!currentMessageId) {
                    const newMessageId = `ai-${Date.now()}`
                    const newMessage: ChatMessage = {
                      id: newMessageId,
                      text: data.text,
                      side: "left",
                      flat: false, // First AI message should not be flat
                      isLoading: false,
                    }
                    setMessages((prev) => [...prev, newMessage])
                    currentMessageId = newMessageId
                    aiResponseText = data.text
                  } else {
                    aiResponseText += data.text
                    // Update the current message with accumulated text
                    setMessages((prev) =>
                      prev.map((msg) =>
                        msg.id === currentMessageId
                          ? { ...msg, text: aiResponseText, isLoading: false }
                          : msg
                      )
                    )
                  }
                }

                // Check if AI indicates it should continue
                const continueIndicators = [
                  "continue with",
                  "next step",
                  "moving on to",
                  "now implementing",
                  "let me",
                  "i'll also",
                  "additionally",
                  "next, i'll",
                ]

                const completionIndicators = [
                  "task completed",
                  "implementation complete",
                  "all done",
                  "finished",
                  "ready",
                  "successfully implemented",
                ]

                const textLower = data.text.toLowerCase()
                const seemsComplete = completionIndicators.some((indicator) =>
                  textLower.includes(indicator)
                )
                const shouldContinueText = continueIndicators.some(
                  (indicator) => textLower.includes(indicator)
                )

                if (shouldContinueText && !seemsComplete) {
                  shouldContinue = true
                }
              }

              if (data.thought) {
                // Add thought message as a separate thought bubble
                const thoughtMessageId = `thought-${Date.now()}`
                const thoughtMessage: ChatMessage = {
                  id: thoughtMessageId,
                  text: data.thought,
                  side: "left",
                  flat: true,
                  isThought: true,
                }

                setMessages((prev) => [...prev, thoughtMessage])
              }

              if (data.function_result) {
                // Handle function results from AI
                handleFunctionResult(data.function_result)
                hasToolExecuted = true

                // If there's a successful function execution, we should continue
                if (data.function_result.success) {
                  shouldContinue = true
                }

                // If there's a message from the tool result, create a tool execution message
                if (data.function_result.message) {
                  const toolMessageId = `tool-${Date.now()}`
                  const toolMessage: ChatMessage = {
                    id: toolMessageId,
                    text: data.function_result.message,
                    side: "left",
                    flat: true,
                    isToolExecution: true,
                  }

                  setMessages((prev) => [...prev, toolMessage])
                }

                // If there's an error from the tool result, also show it as a message
                if (
                  !data.function_result.success &&
                  data.function_result.error
                ) {
                  const errorMessageId = `tool-error-${Date.now()}`
                  const errorMessage: ChatMessage = {
                    id: errorMessageId,
                    text: `❌ ${data.function_result.error}`,
                    side: "left",
                    flat: true,
                    isToolExecution: true,
                  }

                  setMessages((prev) => [...prev, errorMessage])
                }
              }

              if (data.sessionId) {
                receivedSessionId = data.sessionId
              }
            } catch (e) {
              // Skip invalid JSON lines
              console.warn("Failed to parse chunk:", line)
            }
          }
        }

        // Update session ID if provided
        if (receivedSessionId) {
          setSessionId(receivedSessionId)
        }

        return shouldContinue
      } catch (error) {
        console.error("Error in AI request:", error)

        // Add error message if no response was received
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          text: "Sorry, I encountered an error. Please try again.",
          side: "left",
          isLoading: false,
        }
        setMessages((prev) => [...prev, errorMessage])
        return false
      }
    }

    try {
      // Make initial request
      const shouldContinue = await makeAIRequest(currentMessage)

      // If AI should continue, make follow-up requests
      if (shouldContinue) {
        console.log(
          "AI indicates it should continue, making follow-up request..."
        )

        // Add a brief delay to allow state updates to complete
        await new Promise((resolve) => setTimeout(resolve, 500))

        // Make follow-up request with current file state
        const continueMessage =
          "Continue with the implementation. Use the appropriate tools to complete the remaining tasks."
        await makeAIRequest(continueMessage, true)
      }
    } catch (error) {
      console.error("Error in conversation flow:", error)
    } finally {
      setIsLoading(false)
    }
  }, [inputValue, isLoading, sessionId, codeFiles, handleFunctionResult])

  const handleTabChange = useCallback(
    (tab: "code" | "other" | "document") => {
      console.log("handleTabChange called with tab:", tab)
      console.log("Current activeTab before change:", activeTab)
      setActiveTab(tab)
      console.log("ActiveTab should now be:", tab)
      // Clear notification dot when switching to code tab
      if (tab === "code") {
        setHasCodeUpdates(false)
      }
      // Clear notification dot when switching to document tab
      if (tab === "document") {
        setHasDocumentUpdates(false)
      }
    },
    [activeTab]
  )

  const handleInputChange = useCallback((value: string) => {
    setInputValue(value)
  }, [])

  // Handle document tab switching
  const handleDocumentTabChange = useCallback((documentId: string) => {
    setActiveDocumentId(documentId)
  }, [])

  // Debug function to test documentation functionality
  const testDocumentation = () => {
    console.log("=== TESTING DOCUMENTATION FUNCTIONALITY ===")
    console.log("Before test - documentTabs:", documentTabs)
    console.log("Before test - activeTab:", activeTab)
    console.log("Before test - activeDocumentId:", activeDocumentId)

    const testResult: FunctionResult = {
      success: true,
      action: "show_documentation",
      data: {
        documents: [
          {
            id: "button-docs",
            title: "Button",
            content:
              "## Button\n\nThe Button component is a clickable element...",
            isActive: true,
          },
          {
            id: "input-docs",
            title: "Input",
            content:
              "## Input\n\nThe Input component is a wrapper around the native input element...",
            isActive: false,
          },
        ],
        switchToDocTab: true,
      },
    }

    console.log("Calling handleFunctionResult with:", testResult)
    handleFunctionResult(testResult)
  }

  // Mock UI component for when not showing code
  const MockUI = useMemo(
    () => (
      <div className="flex flex-col h-full min-h-full gap-4 p-6">
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7.93-7M3 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Apollo Design System Assistant
          </h3>
          <p className="text-gray-600 mb-4">
            I can help you with design system questions, create UI components,
            or discuss design patterns. Ask me about coding to see the code
            preview!
          </p>
          {/* Debug button */}
          <button
            onClick={testDocumentation}
            className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Test Documentation Tab
          </button>
          <div className="flex flex-wrap gap-2 justify-center">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
              Components
            </span>
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              Design Tokens
            </span>
            <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
              React
            </span>
            <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
              TypeScript
            </span>
          </div>
        </div>
      </div>
    ),
    []
  )

  // Memoize the code content for the "code" tab
  const codeContent = useMemo(
    () => (
      <div className="flex flex-col h-full min-h-full gap-4">
        <CodePreview files={codeFiles} activeFile={activeFile} />
      </div>
    ),
    [codeFiles, activeFile]
  )

  // Memoize the other content for the "other" tab
  const otherContent = useMemo(
    () => <div className="flex flex-col h-full min-h-full gap-4">{MockUI}</div>,
    [MockUI]
  )

  return (
    <PanelLayout
      messages={messages}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      onSendMessage={handleSendMessage}
      isLoading={isLoading}
      activeTab={activeTab}
      onTabChange={handleTabChange}
      hasCodeUpdates={hasCodeUpdates}
      hasDocumentUpdates={hasDocumentUpdates}
      otherContent={otherContent}
      documentTabs={documentTabs}
      activeDocumentId={activeDocumentId}
      onDocumentTabChange={handleDocumentTabChange}
      isLoadingDocuments={isLoadingDocuments}
    >
      {codeContent}
    </PanelLayout>
  )
}
