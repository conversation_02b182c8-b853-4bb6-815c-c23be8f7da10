"use client"

import * as React from "react"

import "@apollo/ui/style.css"

import { ThemeProvider as ApolloThemeProvider } from "@apollo/ui"
import { ThemeProvider as NextThemesProvider } from "next-themes"

export function ThemeProvider({
  children,
  ...props
}: React.ComponentProps<typeof NextThemesProvider>) {
  return (
    <ApolloThemeProvider>
      <NextThemesProvider {...props}>{children}</NextThemesProvider>
    </ApolloThemeProvider>
  )
}
