import {
  Component<PERSON><PERSON>,
  PropsWith<PERSON>hildren,
  ReactNode,
  useEffect,
  useState,
} from "react"
import Markdown from "react-markdown"
import remarkGfm from "remark-gfm"

import { cn } from "@/lib/utils"

export type ChatBubbleProps = PropsWithChildren<
  {
    side?: "left" | "right"
    flat?: boolean
    isLoading?: boolean
    animate?: boolean
    isToolExecution?: boolean
    isThought?: boolean
    isExecuting?: boolean
  } & ComponentProps<"div">
>

function TypingText({
  children,
  speed = 30,
}: {
  children: string
  speed?: number
}) {
  const [displayedText, setDisplayedText] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    if (currentIndex < children.length) {
      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + children[currentIndex])
        setCurrentIndex((prev) => prev + 1)
      }, speed)
      return () => clearTimeout(timeout)
    }
  }, [currentIndex, children, speed])

  useEffect(() => {
    setDisplayedText("")
    setCurrentIndex(0)
  }, [children])

  return (
    <span className="animate-in fade-in duration-300">
      {displayedText}
      {currentIndex < children.length && (
        <span className="animate-pulse">|</span>
      )}
    </span>
  )
}

export function StyledMarkdown({
  children,
  animate = false,
}: {
  children: string
  animate?: boolean
}) {
  if (animate) {
    return (
      <div className="animate-in fade-in slide-in-from-bottom-2 duration-500">
        <TypingText speed={20}>{children}</TypingText>
      </div>
    )
  }

  return (
    <div className="animate-in fade-in slide-in-from-bottom-2 duration-300">
      <Markdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Headings
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-gray-100">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-200">
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-200">
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 className="text-xs font-medium mb-2 text-gray-600 dark:text-gray-300">
              {children}
            </h6>
          ),

          // Paragraphs
          p: ({ children }) => (
            <p className="mb-2 last:mb-0 text-gray-700 dark:text-gray-300 leading-relaxed">
              {children}
            </p>
          ),

          // Text formatting
          strong: ({ children }) => (
            <strong className="font-semibold text-gray-900 dark:text-white">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="italic text-gray-700 dark:text-gray-300">
              {children}
            </em>
          ),

          // Code
          code: ({ children, className }) => {
            const isInline = !className
            if (isInline) {
              return (
                <code className="bg-gray-100 dark:bg-gray-800 text-red-600 dark:text-red-400 px-1.5 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              )
            }
            return (
              <code className="block bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre">
                {children}
              </code>
            )
          },

          pre: ({ children }) => (
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4">
              {children}
            </pre>
          ),

          // Lists
          ul: ({ children }) => (
            <ul className="list-disc pl-6 mb-4 space-y-1">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal pl-6 mb-4 space-y-1">{children}</ol>
          ),
          li: ({ children }) => (
            <li className="text-gray-700 dark:text-gray-300">{children}</li>
          ),

          // Links
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
            >
              {children}
            </a>
          ),

          // Blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-2 mb-4 italic text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-r">
              {children}
            </blockquote>
          ),

          // Tables
          table: ({ children }) => (
            <div className="overflow-x-auto mb-4 rounded-lg border border-gray-300 dark:border-gray-600">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-gray-50 dark:bg-gray-800">{children}</thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">
              {children}
            </td>
          ),

          // Horizontal rule
          hr: () => (
            <hr className="border-t border-gray-300 dark:border-gray-600 my-4" />
          ),
        }}
      >
        {children}
      </Markdown>
    </div>
  )
}

function MessageContainer({
  children,
  colorScheme,
  icon,
}: {
  children: React.ReactNode
  colorScheme: {
    background: string
    border: string
    iconBg: string
    textColor: string
  }
  icon: React.ReactNode
}) {
  return (
    <div
      className={`flex items-start gap-3 p-3 ${colorScheme.background} ${colorScheme.border} rounded-lg shadow-sm`}
    >
      <div
        className={`flex-shrink-0 w-6 h-6 ${colorScheme.iconBg} rounded-full flex items-center justify-center mt-0.5`}
      >
        {icon}
      </div>
      <div className="flex-1 min-w-0">{children}</div>
    </div>
  )
}

function ThoughtMessage({ children }: { children: string }) {
  const colorScheme = {
    background:
      "bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",
    border: "border border-amber-200 dark:border-amber-700/50",
    iconBg: "bg-amber-500 dark:bg-amber-400",
    textColor: "text-amber-800 dark:text-amber-200",
  }

  const icon = (
    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
        clipRule="evenodd"
      />
    </svg>
  )

  return (
    <MessageContainer colorScheme={colorScheme} icon={icon}>
      <div className="text-xs font-medium text-amber-700 dark:text-amber-300 mb-1">
        💭 AI Thought
      </div>
      <p className={`text-sm ${colorScheme.textColor} leading-relaxed italic`}>
        {children}
      </p>
    </MessageContainer>
  )
}

function ToolExecutionMessage({ children }: { children: string }) {
  const colorScheme = {
    background:
      "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
    border: "border border-blue-200 dark:border-blue-700/50",
    iconBg: "bg-blue-500 dark:bg-blue-400",
    textColor: "text-blue-900 dark:text-blue-100",
  }

  const icon = (
    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
        clipRule="evenodd"
      />
    </svg>
  )

  return (
    <MessageContainer colorScheme={colorScheme} icon={icon}>
      <p
        className={`text-sm font-medium ${colorScheme.textColor} leading-relaxed`}
      >
        {children}
      </p>
    </MessageContainer>
  )
}

function ExecutingMessage({ children }: { children: string }) {
  const colorScheme = {
    background:
      "bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20",
    border: "border border-orange-200 dark:border-orange-700/50",
    iconBg: "bg-orange-500 dark:bg-orange-400",
    textColor: "text-orange-900 dark:text-orange-100",
  }

  const icon = (
    <svg
      className="w-3 h-3 text-white animate-spin"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  )

  return (
    <MessageContainer colorScheme={colorScheme} icon={icon}>
      <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">
        ⚙️ Executing
      </div>
      <p
        className={`text-sm font-medium ${colorScheme.textColor} leading-relaxed`}
      >
        {children}
      </p>
    </MessageContainer>
  )
}

function LoadingSkeleton() {
  return (
    <div className="flex flex-col space-y-2 animate-pulse p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
      <div className="flex space-x-2">
        <div className="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-bounce"></div>
        <div
          className="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-bounce"
          style={{ animationDelay: "0.1s" }}
        ></div>
        <div
          className="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-bounce"
          style={{ animationDelay: "0.2s" }}
        ></div>
      </div>
      <div className="text-xs text-gray-500 dark:text-gray-400">
        Pilot is thinking...
      </div>
    </div>
  )
}

function processChildren(children: ReactNode, animate = false): ReactNode {
  if (typeof children === "string") {
    return <StyledMarkdown animate={animate}>{children}</StyledMarkdown>
  }

  if (Array.isArray(children)) {
    return children.map((child, index) => {
      if (typeof child === "string") {
        return (
          <StyledMarkdown key={index} animate={animate}>
            {child}
          </StyledMarkdown>
        )
      }
      return child
    })
  }

  return children
}

export function ChatBubble({
  children,
  flat,
  side = "left",
  isLoading = false,
  animate = false,
  isToolExecution = false,
  isThought = false,
  isExecuting = false,
}: ChatBubbleProps) {
  const shouldAnimate = animate && flat

  // Shared wrapper for special message types
  const renderSpecialMessage = (
    MessageComponent: React.ComponentType<{ children: string }>
  ) => (
    <div
      className={cn(
        "flex flex-col justify-start items-start max-w-[90%] transition-all duration-300 min-w-fit mb-2",
        side === "left" ? "self-start" : "self-end",
        shouldAnimate &&
          "animate-in slide-in-from-bottom-4 fade-in duration-500"
      )}
    >
      <MessageComponent>{children as string}</MessageComponent>
    </div>
  )

  // Special rendering for thought messages
  if (isThought) {
    return renderSpecialMessage(ThoughtMessage)
  }

  // Special rendering for executing messages
  if (isExecuting) {
    return renderSpecialMessage(ExecutingMessage)
  }

  // Special rendering for tool execution messages
  if (isToolExecution) {
    return renderSpecialMessage(ToolExecutionMessage)
  }

  return (
    <div
      className={cn(
        "flex flex-col justify-start items-start p-2 px-3 rounded-2xl shadow-md max-w-[90%] transition-all duration-300 min-w-fit",
        side === "left" ? "self-start" : "self-end",
        flat || side === "left"
          ? "dark:text-white max-w-full border border-gray-700 rounded-md"
          : "bg-white",
        "text-slate-700",
        shouldAnimate &&
          "animate-in slide-in-from-bottom-4 fade-in duration-500"
      )}
    >
      {isLoading ? (
        <LoadingSkeleton />
      ) : side === "left" ? (
        processChildren(children, shouldAnimate)
      ) : (
        children
      )}
    </div>
  )
}
