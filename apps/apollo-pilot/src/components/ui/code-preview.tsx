import { memo, useEffect, useState } from "react"
import {
  SandpackCodeEditor,
  SandpackFileExplorer,
  SandpackLayout,
  SandpackPreview,
  SandpackProvider,
} from "@codesandbox/sandpack-react"
import { nightOwl } from "@codesandbox/sandpack-themes"

interface CodePreviewProps {
  files?: Record<string, string>
  activeFile?: string
}

const defaultFiles = {
  "/App.tsx": `
  import { Button } from '@apollo/ui'
  export default function App() {
  return <Button>Hello Apollo UI 234</Button>
}
  `,
  "index.tsx": `import React, { StrictMode } from "react"
import { createRoot } from "react-dom/client"



// @ts-ignore
import App from "./App"
import { ThemeProvider } from "@apollo/ui"
import "@apollo/ui/style.css"
const root = createRoot(document.getElementById("root"))
root.render(
  <StrictMode>
      <ThemeProvider>
        <App />
      </ThemeProvider>
  </StrictMode>
)`,
}

function CodePreviewComponent({
  files = defaultFiles,
  activeFile,
}: CodePreviewProps) {
  const [isLoadedApollo, setIsLoadedApollo] = useState(false)
  const [apolloUIFiles, setApolloUIFiles] = useState<{
    javascript: string
    css: string
  }>({
    javascript: "",
    css: "",
  })

  useEffect(() => {
    // Load Apollo UI styles and scripts dynamically
    const loadApolloUI = async () => {
      try {
        const response = await fetch("/api/get-apollo")
        const data = await response.json()

        if (data.success) {
          // Create a style element for CSS

          setIsLoadedApollo(true)
          setApolloUIFiles({
            javascript: data.files.javascript,
            css: data.files.css,
          })
        } else {
          console.error("Failed to load Apollo UI files:", data.error)
        }
      } catch (error) {
        console.error("Error loading Apollo UI files:", error)
      }
    }

    loadApolloUI()
  }, [])

  if (!isLoadedApollo) {
    return <CodePreviewSkeleton />
  }

  return (
    <div
      id="code-preview"
      className="flex flex-col h-full justify-start items-start gap-2 w-full rounded-2xl overflow-hidden border max-h-[70vh]"
    >
      <div className="flex-1 flex flex-col justify-start items-start self-stretch ">
        <SandpackProvider
          theme={nightOwl}
          className="flex-1 h-full! w-full! rounded-2xl! overflow-hidden!"
          options={{
            autoReload: true,
            autorun: true,
            activeFile: activeFile,
          }}
          files={{
            ...files,
            "/node_modules/@apollo/ui/package.json": {
              hidden: true,
              code: JSON.stringify({
                name: "apollo-ui",
                main: "./index.js",
              }),
            },
            "/node_modules/@apollo/ui/index.js": {
              hidden: true,
              code: apolloUIFiles.javascript,
            },
            "/node_modules/@apollo/ui/style.css": {
              hidden: true,
              code: apolloUIFiles.css,
            },
          }}
          template="react-ts"
        >
          <SandpackLayout className="self-stretch h-full flex flex-row justify-start items-start ">
            <SandpackFileExplorer className="self-stretch h-full! w-[300px] !flex flex-row justify-start items-start" />
            <SandpackCodeEditor
              closableTabs
              showTabs
              showLineNumbers
              className="self-stretch overflow-auto max-h-[80vh] h-full! max-w-[30%]!"
            />
            <SandpackPreview className="self-stretch h-full! flex flex-row" />
          </SandpackLayout>
        </SandpackProvider>
      </div>
    </div>
  )
}

export const CodePreview = memo(CodePreviewComponent)

// Skeleton component for code preview with dark mode support
function CodePreviewSkeleton() {
  return (
    <div className="flex flex-col h-full justify-start items-start gap-2 w-full rounded-2xl overflow-hidden border border-slate-200 dark:border-slate-700">
      <div className="flex-1 flex flex-row justify-start items-start self-stretch">
        {/* File Explorer Skeleton */}
        <div className="w-64 h-full border-r border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 p-4">
          <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-4 w-3/4 mb-3 rounded"></div>
          <div className="space-y-2">
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-3 w-1/2 rounded"></div>
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-3 w-2/3 rounded ml-4"></div>
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-3 w-1/2 rounded ml-4"></div>
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-3 w-3/4 rounded"></div>
          </div>
        </div>

        {/* Code Editor Skeleton */}
        <div className="flex-1 h-full border-r border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
          {/* Tabs */}
          <div className="h-10 border-b border-slate-200 dark:border-slate-700 bg-slate-100 dark:bg-slate-800 flex items-center px-4">
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-6 w-20 rounded mr-4"></div>
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-6 w-24 rounded"></div>
          </div>
          {/* Code lines */}
          <div className="p-4 space-y-2">
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-3/4 rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-full rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-5/6 rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-2/3 rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-full rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-4/5 rounded"></div>
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-4 w-1/2 rounded"></div>
          </div>
        </div>

        {/* Preview Skeleton */}
        <div className="w-96 h-full bg-white dark:bg-slate-900 p-4">
          <div className="h-8 border-b border-slate-200 dark:border-slate-700 mb-4 flex items-center">
            <div className="animate-pulse bg-slate-300 dark:bg-slate-600 h-4 w-16 rounded"></div>
          </div>
          <div className="space-y-4">
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-8 w-32 rounded"></div>
            <div className="animate-pulse bg-slate-100 dark:bg-slate-800 h-32 w-full rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}
