"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON>B<PERSON>on, Textarea, Typography } from "@apollo/ui"
import { Atom, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

import { ChatBubble, StyledMarkdown } from "./chat-bubble"
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "./resizable"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "./tabs"

interface ChatMessage {
  id: string
  text: string
  side?: "left" | "right"
  flat?: boolean
  isLoading?: boolean
  isToolExecution?: boolean
  isThought?: boolean
  isExecuting?: boolean
}

interface DocumentItem {
  id: string
  title: string
  content: string
  isActive?: boolean
}

interface PanelLayoutProps extends React.PropsWithChildren {
  messages?: ChatMessage[]
  inputValue?: string
  onInputChange?: (value: string) => void
  onSendMessage?: () => void
  isLoading?: boolean
  activeTab?: "code" | "other" | "document"
  onTabChange?: (tab: "code" | "other" | "document") => void
  hasCodeUpdates?: boolean
  hasDocumentUpdates?: boolean
  otherContent?: React.ReactNode
  documentTabs?: DocumentItem[]
  activeDocumentId?: string | null
  onDocumentTabChange?: (documentId: string) => void
  isLoadingDocuments?: boolean
}

export function PanelLayout({
  children,
  messages = [],
  inputValue = "",
  onInputChange,
  onSendMessage,
  isLoading = false,
  activeTab = "code",
  onTabChange,
  hasCodeUpdates = false,
  hasDocumentUpdates = false,
  otherContent,
  documentTabs,
  activeDocumentId,
  onDocumentTabChange,
  isLoadingDocuments = false,
}: PanelLayoutProps) {
  const [internalActiveTab, setInternalActiveTab] = useState<
    "code" | "other" | "document"
  >(activeTab)

  // Use internal state only if external activeDocumentId is not provided
  const [internalActiveDocumentId, setInternalActiveDocumentId] =
    useState<string>(() => {
      // Find the active document or default to the first one
      const activeDoc = documentTabs?.find((doc) => doc.isActive)
      return activeDoc?.id || documentTabs?.[0]?.id || ""
    })

  // Update internalActiveDocumentId when documentTabs changes
  useEffect(() => {
    if (documentTabs && documentTabs.length > 0 && !activeDocumentId) {
      const activeDoc = documentTabs.find((doc) => doc.isActive)
      const newActiveId = activeDoc?.id || documentTabs[0].id
      setInternalActiveDocumentId(newActiveId)
    }
  }, [documentTabs, activeDocumentId])

  const handleTabChange = (tab: "code" | "other" | "document") => {
    setInternalActiveTab(tab)
    onTabChange?.(tab)
  }

  const handleDocumentTabChange = (documentId: string) => {
    if (onDocumentTabChange) {
      onDocumentTabChange(documentId)
    } else {
      setInternalActiveDocumentId(documentId)
    }
  }

  const currentTab = onTabChange ? activeTab : internalActiveTab
  const currentActiveDocumentId = activeDocumentId || internalActiveDocumentId
  return (
    <div className="flex flex-col justify-start items-start h-screen">
      <div className="border-b border-slate-700 p-4 self-stretch">
        <div className="flex flex-row items-center gap-4">
          <Image width={40} height={40} src="/logo.svg" alt="Apollo Logo" />
          <Typography level="h3" className="text-slate-100">
            Apollo Pilot
            <span className="ml-2 align-middle text-xs bg-violet-600 rounded-sm px-2 font-bold">
              AI
            </span>
          </Typography>
        </div>
      </div>
      <div className="flex flex-row justify-start items-start self-stretch flex-1">
        <ResizablePanelGroup direction="horizontal">
          <ResizablePanel
            defaultSize={20}
            maxSize={20}
            className="flex flex-col relative  justify-start items-start"
          >
            <div
              className="self-stretch flex-1 flex-col items-stretch flex py-6 gap-4 max-h-[calc(100vh-280px)] overflow-y-auto  p-4"
              ref={(ref) => ref?.scrollTo(0, ref.scrollHeight)}
            >
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-slate-400">
                  <Typography level="body2">Start a conversation...</Typography>
                </div>
              ) : (
                <>
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={cn(
                        "animate-in slide-in-from-bottom-2 fade-in duration-300",
                        {
                          "self-start": message.side === "left",
                          "self-end": message.side === "right",
                        }
                      )}
                    >
                      <ChatBubble
                        side={message.side}
                        flat={message.flat}
                        isLoading={message.isLoading}
                        isToolExecution={message.isToolExecution}
                        isThought={message.isThought}
                        isExecuting={message.isExecuting}
                      >
                        {message.text}
                      </ChatBubble>
                    </div>
                  ))}
                </>
              )}
            </div>
            <div className="flex flex-col justify-start items-stretch border border-b- border-slate-700 p-4 rounded-2xl gap-2 absolute bottom-0 w-full">
              {/* Loading indicator */}
              {isLoading && (
                <div className="mb-2 flex items-center space-x-2 text-slate-400 text-sm">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                  </div>
                  <span>Pilot is thinking...</span>
                </div>
              )}
              <Textarea
                fullWidth
                placeholder="Type your message here..."
                value={inputValue}
                onChange={(e) =>
                  onInputChange?.((e.target as HTMLTextAreaElement).value)
                }
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault()
                    if (!isLoading && inputValue.trim()) {
                      onSendMessage?.()
                    }
                  }
                }}
                disabled={isLoading}
              />
              <div className="flex flex-row justify-between items-center self-stretch">
                <div className="flex flex-row justify-start items-center gap-1">
                  {/* <IconButton onClick={() => alert("Test")}>
                    <ArrowUp />
                  </IconButton>
                  <IconButton onClick={() => alert("Test")}>
                    <ArrowUp />
                  </IconButton>
                  <IconButton onClick={() => alert("Test")}>
                    <ArrowUp />
                  </IconButton> */}
                </div>
                <div className="flex flex-row justify-end items-center gap-2">
                  <IconButton
                    onClick={onSendMessage}
                    disabled={isLoading || !inputValue.trim()}
                    className="rounded-full!  border-2! bg-white! hover:bg-[var(--apl-colors-border-primary-default)]! hover:border-[var(--apl-colors-border-primary-default)]! hover:text-white!"
                  >
                    {isLoading ? (
                      <Loader2 className="animate-spin" />
                    ) : (
                      <Atom />
                    )}
                  </IconButton>
                </div>
              </div>
            </div>
          </ResizablePanel>
          <ResizableHandle withHandle className="bg-slate-700" />
          <ResizablePanel className="flex flex-col" defaultSize={80}>
            <Tabs
              value={currentTab}
              onValueChange={(value) =>
                handleTabChange(value as "code" | "other" | "document")
              }
              className="flex flex-col h-full"
            >
              <div className="border-b border-slate-700 bg-slate-800 p-2">
                <TabsList className="bg-slate-700/50 w-fit">
                  <TabsTrigger
                    value="code"
                    className="relative data-[state=active]:bg-slate-600 data-[state=active]:text-white"
                  >
                    Code
                    {/* Notification dot */}
                    {hasCodeUpdates && currentTab !== "code" && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-violet-500 rounded-full animate-pulse"></div>
                    )}
                  </TabsTrigger>
                  {(documentTabs && documentTabs.length > 0) ||
                  isLoadingDocuments ? (
                    <TabsTrigger
                      value="document"
                      className="relative data-[state=active]:bg-slate-600 data-[state=active]:text-white"
                    >
                      Documentation
                      {/* Notification dot */}
                      {hasDocumentUpdates && currentTab !== "document" && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-violet-500 rounded-full animate-pulse"></div>
                      )}
                    </TabsTrigger>
                  ) : null}
                  <TabsTrigger
                    value="other"
                    className="data-[state=active]:bg-slate-600 data-[state=active]:text-white"
                  >
                    Other
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="code" className="flex-1 p-10 m-0">
                {children}
              </TabsContent>
              {((documentTabs && documentTabs.length > 0) ||
                isLoadingDocuments) && (
                <TabsContent value="document" className="flex-1 m-0">
                  {isLoadingDocuments ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="flex flex-col items-center gap-4">
                        <Loader2 className="w-8 h-8 animate-spin text-slate-400" />
                        <Typography level="body2" className="text-slate-400">
                          Loading component documentation...
                        </Typography>
                      </div>
                    </div>
                  ) : (
                    <Tabs
                      value={currentActiveDocumentId || undefined}
                      onValueChange={handleDocumentTabChange}
                      className="flex flex-col h-full"
                    >
                      <div className="border-b border-slate-600 bg-slate-900/50 p-2">
                        <TabsList className="bg-slate-600/30 w-fit">
                          {documentTabs?.map((doc) => (
                            <TabsTrigger
                              key={doc.id}
                              value={doc.id}
                              className="text-xs data-[state=active]:bg-slate-500 data-[state=active]:text-white"
                            >
                              {doc.title}
                            </TabsTrigger>
                          ))}
                        </TabsList>
                      </div>

                      {documentTabs?.map((doc) => (
                        <TabsContent
                          key={doc.id}
                          value={doc.id}
                          className="flex-1 p-10 m-0 overflow-y-auto max-h-[calc(100vh-280px)]"
                        >
                          <StyledMarkdown>{doc.content}</StyledMarkdown>
                        </TabsContent>
                      ))}
                    </Tabs>
                  )}
                </TabsContent>
              )}
              <TabsContent value="other" className="flex-1 p-10 m-0">
                {otherContent || children}
              </TabsContent>
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  )
}
