import { FunctionDeclaration, Type } from "@google/genai"

import { getF<PERSON>ComponentJSON } from "../figma-parser"

// Tool name constants to avoid hardcoding
export const TOOL_NAMES = {
  UPDATE_APP_CODE: "update_app_code",
  ADD_COMPONENT_FILE: "add_component_file",
  UPDATE_INDEX_FILE: "update_index_file",
  READ_CURRENT_CODE: "read_current_code",
  LIST_PROJECT_FILES: "list_project_files",
  GET_COMPONENTS_LIST: "get_components_list",
  GET_COMPONENT_DOCUMENTATION: "get_component_documentation",
  SHOW_DOCUMENTATION: "show_documentation",
  PARSE_FIGMA_TO_DSL: "parse_figma_to_dsl",
} as const

// Tool categories for easier management
export const TOOL_CATEGORIES = {
  MODIFICATION_TOOLS: [
    TOOL_NAMES.UPDATE_APP_CODE,
    TOOL_NAMES.ADD_COMPONENT_FILE,
    TOOL_NAMES.UPDATE_INDEX_FILE,
  ],
  READ_TOOLS: [
    TOOL_NAMES.READ_CURRENT_CODE,
    TOOL_NAMES.LIST_PROJECT_FILES,
    TOOL_NAMES.GET_COMPONENTS_LIST,
    TOOL_NAMES.GET_COMPONENT_DOCUMENTATION,
    TOOL_NAMES.PARSE_FIGMA_TO_DSL,
  ],
  UI_TOOLS: [TOOL_NAMES.SHOW_DOCUMENTATION],
} as const

// Utility functions for tool categorization
export function isModificationTool(toolName: string): boolean {
  return TOOL_CATEGORIES.MODIFICATION_TOOLS.includes(
    toolName as (typeof TOOL_CATEGORIES.MODIFICATION_TOOLS)[number]
  )
}

export function isReadTool(toolName: string): boolean {
  return TOOL_CATEGORIES.READ_TOOLS.includes(
    toolName as (typeof TOOL_CATEGORIES.READ_TOOLS)[number]
  )
}

export function getAllToolNames(): string[] {
  return Object.values(TOOL_NAMES)
}

/**
 * Checks if a given tool name is a valid registered tool
 */
export function isValidTool(toolName: string): boolean {
  return getAllToolNames().includes(toolName)
}

/**
 * Gets all tools in a specific category
 */
export function getToolsByCategory(
  category: keyof typeof TOOL_CATEGORIES
): readonly string[] {
  return TOOL_CATEGORIES[category]
}

/**
 * Counts successful operations by category in a list of function results
 */
export function countSuccessfulOperationsByCategory(
  results: FunctionResult[],
  category: keyof typeof TOOL_CATEGORIES
): number {
  const toolsInCategory = getToolsByCategory(category)
  return results.filter(
    (result) => result.success && toolsInCategory.includes(result.action)
  ).length
}

/**
 * Filters function results by tool category
 */
export function filterResultsByCategory(
  results: FunctionResult[],
  category: keyof typeof TOOL_CATEGORIES,
  successOnly: boolean = false
): FunctionResult[] {
  const toolsInCategory = getToolsByCategory(category)
  return results.filter(
    (result) =>
      toolsInCategory.includes(result.action) &&
      (!successOnly || result.success)
  )
}

// Types for function calling
export interface FunctionResult {
  success: boolean
  action: string
  data?: Record<string, unknown>
  message?: string
  error?: string
}

// Figma-related types
export interface ComponentJSON {
  component: string
  props?: Record<string, unknown>
  children?: ComponentJSON[]
  text?: string
  variant?: string
}

export interface PageJSON {
  pageName: string
  components: ComponentJSON[]
}

export interface ComponentJSONResponse {
  rawData?: Record<string, unknown>
  json: ComponentJSON | PageJSON[]
  dsl?: string
}

export interface FigmaCanvas {
  id: string
  name: string
  type: "CANVAS"
}

export interface FigmaDocumentNode {
  id: string
  name: string
  type: string
  children?: FigmaDocumentNode[]
}

export interface FigmaFileResponse {
  name: string
  lastModified: string
  thumbnailUrl: string
  document: {
    children: FigmaDocumentNode[]
  }
  rawData: Record<string, unknown>
}

export interface GenerateHtmlResult {
  canvases: FigmaCanvas[]
  fileName: string
  lastModified: string
  thumbnailUrl: string
  rawData: Record<string, unknown>
}

export interface PsuedoCodeResponse {
  pageName: string
  code: string
}

// Session cache for components list to avoid repeated API calls
const componentsCache = new Map<
  string,
  {
    data: {
      components: Array<{
        name: string
        category: string
        description: string
      }>
      totalCount: number
      categories: string[]
    }
    timestamp: number
    expiresIn: number // milliseconds
  }
>()

const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Cache cleanup function to prevent memory leaks
export function cleanupExpiredCache(): void {
  const now = Date.now()
  for (const [key, cached] of componentsCache.entries()) {
    if (now >= cached.timestamp + cached.expiresIn) {
      componentsCache.delete(key)
      console.log(`[Cache Cleanup] Removed expired cache entry: ${key}`)
    }
  }
}

// Auto cleanup every 10 minutes
setInterval(cleanupExpiredCache, 10 * 60 * 1000)

// Function declarations for code manipulation
export const codeUpdateFunctions: FunctionDeclaration[] = [
  {
    name: TOOL_NAMES.UPDATE_APP_CODE,
    description:
      "Updates any file's code in the code preview. Can update App.tsx, component files, or any other code file.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        filename: {
          type: Type.STRING,
          description:
            "Name of the file to update (e.g., 'App.tsx', 'Header.tsx', 'index.tsx'). Defaults to 'App.tsx' if not provided.",
        },
        code: {
          type: Type.STRING,
          description:
            "The complete code content to replace the current file content. Should be valid JSX/TSX code that imports from '@apollo/ui'.",
        },
        description: {
          type: Type.STRING,
          description: "Brief description of what was changed or created",
        },
      },
      required: ["code", "description"],
    },
  },
  {
    name: TOOL_NAMES.ADD_COMPONENT_FILE,
    description: "Creates a new component file in the code preview",
    parameters: {
      type: Type.OBJECT,
      properties: {
        filename: {
          type: Type.STRING,
          description:
            "Name of the new file (e.g., 'Header.tsx', 'LoginForm.tsx')",
        },
        code: {
          type: Type.STRING,
          description: "The complete component code content",
        },
        description: {
          type: Type.STRING,
          description: "Brief description of the component being created",
        },
      },
      required: ["filename", "code", "description"],
    },
  },
  {
    name: TOOL_NAMES.UPDATE_INDEX_FILE,
    description:
      "Updates the index.tsx file that renders the main App component",
    parameters: {
      type: Type.OBJECT,
      properties: {
        code: {
          type: Type.STRING,
          description: "The complete index.tsx code content",
        },
        description: {
          type: Type.STRING,
          description:
            "Brief description of what was changed in the index file",
        },
      },
      required: ["code", "description"],
    },
  },
  {
    name: TOOL_NAMES.READ_CURRENT_CODE,
    description:
      "Reads the current code files in the code preview to understand the existing implementation before making changes",
    parameters: {
      type: Type.OBJECT,
      properties: {
        filename: {
          type: Type.STRING,
          description:
            "Optional specific filename to read (e.g., 'App.tsx', 'index.tsx'). If not provided, returns all current files.",
        },
      },
      required: [],
    },
  },
  {
    name: TOOL_NAMES.LIST_PROJECT_FILES,
    description:
      "Lists all files currently available in the code preview (codeFiles state). This shows the files that are actively being worked on in the code editor. Useful for understanding what files are available for editing or reference when adding new components or features.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        includePattern: {
          type: Type.STRING,
          description:
            "Optional glob pattern to filter files (e.g., '**/*.tsx', '**/*.ts', '*.tsx'). If not provided, lists all files.",
        },
        maxDepth: {
          type: Type.NUMBER,
          description:
            "Optional maximum directory depth to traverse. Default is unlimited.",
        },
      },
      required: [],
    },
  },
  {
    name: TOOL_NAMES.GET_COMPONENTS_LIST,
    description:
      "Fetches a list of all available components in the Apollo Design System documentation. Returns component names, categories, and example information.",
    parameters: {
      type: Type.OBJECT,
      properties: {},
      required: [],
    },
  },
  {
    name: TOOL_NAMES.GET_COMPONENT_DOCUMENTATION,
    description:
      "Fetches the complete documentation for a specific component, including code examples. The ComponentPreviewUI tags are automatically replaced with actual code snippets.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        component: {
          type: Type.STRING,
          description:
            "Name of the component to get documentation for (e.g., 'chip', 'button', 'typography'). Use lowercase names.",
        },
      },
      required: ["component"],
    },
  },
  {
    name: TOOL_NAMES.SHOW_DOCUMENTATION,
    description:
      "Shows documentation in the documentation tab. This tool allows the AI to display component documentation or other reference materials to help users understand components or build UI. The documentation will be displayed as separate tabs within the Documentation section.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        documents: {
          type: Type.ARRAY,
          description: "Array of documents to show in the documentation tab",
          items: {
            type: Type.OBJECT,
            properties: {
              id: {
                type: Type.STRING,
                description:
                  "Component name or unique identifier for the document (e.g., 'button', 'input', 'tabs'). This should match the component name used in get_component_documentation calls.",
              },
              title: {
                type: Type.STRING,
                description:
                  "Display title for the document tab (e.g., 'Button', 'Input Component')",
              },
              content: {
                type: Type.STRING,
                description:
                  "Markdown content of the document. This should include complete documentation with examples.",
              },
              isActive: {
                type: Type.BOOLEAN,
                description:
                  "Whether this document should be the active tab by default. Only one document should be active.",
              },
            },
            required: ["id", "title", "content"],
          },
        },
        switchToDocTab: {
          type: Type.BOOLEAN,
          description:
            "Whether to automatically switch to the Documentation tab after showing the documents. Default is true.",
        },
      },
      required: ["documents"],
    },
  },
  {
    name: TOOL_NAMES.PARSE_FIGMA_TO_DSL,
    description:
      "Parses a Figma design URL and converts it to DSL (Domain Specific Language) format. If the URL contains a node-id parameter, only that specific component/node will be parsed to DSL. Otherwise, the entire file will be processed. This tool extracts design components from Figma and generates a structured representation that can be used for code generation.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        figmaUrl: {
          type: Type.STRING,
          description:
            "The Figma URL to parse. Should be a valid Figma design URL (e.g., 'https://www.figma.com/design/fileKey/fileName' or 'https://www.figma.com/file/fileKey/fileName'). Can include node-id parameter for specific components - when node-id is present, only that specific node will be converted to DSL instead of the entire file.",
        },
      },
      required: ["figmaUrl"],
    },
  },
]

// Function implementations

export function updateAppCode(
  code: string,
  description: string,
  filename?: string
): FunctionResult {
  const targetFile = filename || "App.tsx"
  // Normalize filename to include leading slash for consistency
  const normalizedFilename = targetFile.startsWith("/")
    ? targetFile
    : `/${targetFile}`

  return {
    success: true,
    action: "update_app_code",
    data: {
      code,
      description,
      filename: normalizedFilename,
    },
    message: `Updated ${targetFile}: ${description}`,
  }
}

export function addComponentFile(
  filename: string,
  code: string,
  description: string
): FunctionResult {
  return {
    success: true,
    action: "add_component_file",
    data: { filename, code, description },
    message: `Created ${filename}: ${description}`,
  }
}

export function updateIndexFile(
  code: string,
  description: string
): FunctionResult {
  return {
    success: true,
    action: "update_index_file",
    data: { code, description },
    message: `Updated index.tsx: ${description}`,
  }
}

export function readCurrentCode(
  filename?: string,
  currentFiles?: Record<string, string>
): FunctionResult {
  if (!currentFiles) {
    return {
      success: false,
      action: "read_current_code",
      error: "No current files available to read",
    }
  }

  if (filename) {
    // Read specific file
    const filePath = filename.startsWith("/") ? filename : `/${filename}`
    const fileContent = currentFiles[filePath]

    if (!fileContent) {
      return {
        success: false,
        action: "read_current_code",
        error: `File ${filename} not found`,
      }
    }

    return {
      success: true,
      action: "read_current_code",
      data: { filename, code: fileContent },
      message: `Read current content of ${filename}`,
    }
  } else {
    // Read all files
    return {
      success: true,
      action: "read_current_code",
      data: { files: currentFiles },
      message: `Read all current files: ${Object.keys(currentFiles).join(", ")}`,
    }
  }
}

export function listProjectFiles(
  includePattern?: string,
  maxDepth?: number,
  codeFiles?: Record<string, string>
): FunctionResult {
  if (!codeFiles) {
    return {
      success: false,
      action: "list_project_files",
      error: "No code files data available",
    }
  }

  try {
    // Get all filenames from codeFiles
    let files = Object.keys(codeFiles)

    // Simple pattern matching function
    function matchesPattern(filePath: string, pattern: string): boolean {
      // Convert glob pattern to regex
      const regexPattern = pattern
        .replace(/\*\*/g, ".*")
        .replace(/\*/g, "[^/]*")
        .replace(/\?/g, "[^/]")

      const regex = new RegExp(`^${regexPattern}$`)
      return regex.test(filePath)
    }

    // Apply pattern filter if provided
    if (includePattern) {
      files = files.filter((filePath) =>
        matchesPattern(filePath, includePattern)
      )
    }

    // Apply maxDepth filter if provided
    if (maxDepth !== undefined) {
      files = files.filter((filePath) => {
        // Count depth by counting slashes (ignoring leading slash)
        const normalizedPath = filePath.startsWith("/")
          ? filePath.slice(1)
          : filePath
        const depth = normalizedPath.split("/").length - 1
        return depth <= maxDepth
      })
    }

    // Sort files for better readability
    files.sort()

    return {
      success: true,
      action: "list_project_files",
      data: {
        files,
        count: files.length,
        pattern: includePattern,
        maxDepth,
      },
      message: `Found ${files.length} files${includePattern ? ` matching pattern '${includePattern}'` : ""}${maxDepth !== undefined ? ` (max depth: ${maxDepth})` : ""}`,
    }
  } catch (error) {
    return {
      success: false,
      action: "list_project_files",
      error: `Failed to list project files: ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}

export async function getComponentsList(
  sessionId?: string
): Promise<FunctionResult> {
  try {
    // Check cache first if sessionId is provided
    if (sessionId) {
      const cacheKey = `components_${sessionId}`
      const cached = componentsCache.get(cacheKey)

      if (cached && Date.now() < cached.timestamp + cached.expiresIn) {
        console.log(
          `[Cache Hit] Returning cached components list for session ${sessionId}`
        )
        return {
          success: true,
          action: "get_components_list",
          data: cached.data,
          message: `Found ${cached.data.totalCount} components across ${cached.data.categories?.length || 0} categories (cached)`,
        }
      }
    }

    console.log(`[Cache Miss] Fetching components list from API`)
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3031"}/api/components`
    )

    if (!response.ok) {
      return {
        success: false,
        action: "get_components_list",
        error: `Failed to fetch components list: ${response.statusText}`,
      }
    }

    const data = await response.json()

    // Cache the result if sessionId is provided
    if (sessionId) {
      const cacheKey = `components_${sessionId}`
      componentsCache.set(cacheKey, {
        data: {
          components: data.components,
          totalCount: data.totalCount,
          categories: data.categories,
        },
        timestamp: Date.now(),
        expiresIn: CACHE_DURATION,
      })
      console.log(`[Cache Set] Cached components list for session ${sessionId}`)
    }

    return {
      success: true,
      action: "get_components_list",
      data: {
        components: data.components,
        totalCount: data.totalCount,
        categories: data.categories,
      },
      message: `Found ${data.totalCount} components across ${data.categories?.length || 0} categories`,
    }
  } catch (error) {
    return {
      success: false,
      action: "get_components_list",
      error: `Failed to fetch components list: ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}

export async function getComponentDocumentation(
  component: string
): Promise<FunctionResult> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3031"}/api/get-document?component=${encodeURIComponent(component)}`
    )

    if (!response.ok) {
      if (response.status === 404) {
        return {
          success: false,
          action: "get_component_documentation",
          error: `Component '${component}' documentation not found. Use get_components_list to see available components.`,
        }
      }
      return {
        success: false,
        action: "get_component_documentation",
        error: `Failed to fetch documentation for '${component}': ${response.statusText}`,
      }
    }

    const data = await response.json()

    return {
      success: true,
      action: "get_component_documentation",
      data: {
        component: data.component,
        markdown: data.markdown,
      },
      message: `Retrieved documentation for '${component}' component with code examples`,
    }
  } catch (error) {
    return {
      success: false,
      action: "get_component_documentation",
      error: `Failed to fetch documentation for '${component}': ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}

export async function parseFigmaToDSL(
  figmaUrl: string
): Promise<FunctionResult> {
  try {
    // Import figma-tools functions

    const { dsl } = await getFigmaComponentJSON(figmaUrl)
    return {
      success: true,
      action: "parse_figma_to_dsl",
      data: {
        figmaUrl,
        dslOutput: dsl,
      },
      message: `Successfully parsed Figma URL to DSL format 
      \`\`\`dsl
      ${dsl}
      \`\`\`dsl
      `,
    }
  } catch (error) {
    // Handle missing access token error specifically
    if (
      error instanceof Error &&
      error.message.includes("FIGMA_ACCESS_TOKEN")
    ) {
      return {
        success: false,
        action: "parse_figma_to_dsl",
        error: `${error.message}\n\nTo use real Figma API integration:\n1. Get your Personal Access Token from https://www.figma.com/developers/api#access-tokens\n2. Set the FIGMA_ACCESS_TOKEN environment variable\n3. Restart the application`,
      }
    }

    return {
      success: false,
      action: "parse_figma_to_dsl",
      error: `Failed to parse Figma URL: ${error instanceof Error ? error.message : "Unknown error"}`,
    }
  }
}

export function showDocumentation(
  documents: Array<{
    id: string
    title: string
    content: string
    isActive?: boolean
  }>,
  switchToDocTab: boolean = true
): FunctionResult {
  // Validate documents array
  if (!documents || documents.length === 0) {
    return {
      success: false,
      action: "show_documentation",
      error: "No documents provided to display",
    }
  }

  // Validate each document has required fields
  for (const doc of documents) {
    if (!doc.id || !doc.title || !doc.content) {
      return {
        success: false,
        action: "show_documentation",
        error: `Invalid document: missing required fields (id, title, content)`,
      }
    }
  }

  // Ensure only one document is marked as active
  const activeDocuments = documents.filter((doc) => doc.isActive)
  if (activeDocuments.length > 1) {
    // If multiple are marked active, only keep the first one active
    documents.forEach((doc, index) => {
      doc.isActive = index === documents.findIndex((d) => d.isActive)
    })
  } else if (activeDocuments.length === 0) {
    // If none are marked active, make the first one active
    documents[0].isActive = true
  }

  return {
    success: true,
    action: "show_documentation",
    data: {
      documents,
      switchToDocTab,
    },
    message: `Showing ${documents.length} document${documents.length > 1 ? "s" : ""} in documentation tab: ${documents.map((d) => d.title).join(", ")}`,
  }
}

// Tool execution handler
export function executeToolFunction(
  name: string,
  args: Record<string, unknown>,
  currentFiles?: Record<string, string>,
  sessionId?: string
): FunctionResult | Promise<FunctionResult> {
  switch (name) {
    case TOOL_NAMES.READ_CURRENT_CODE:
      return readCurrentCode(args.filename as string | undefined, currentFiles)
    case TOOL_NAMES.UPDATE_APP_CODE:
      return updateAppCode(
        args.code as string,
        args.description as string,
        args.filename as string | undefined
      )
    case TOOL_NAMES.ADD_COMPONENT_FILE:
      return addComponentFile(
        args.filename as string,
        args.code as string,
        args.description as string
      )
    case TOOL_NAMES.UPDATE_INDEX_FILE:
      return updateIndexFile(args.code as string, args.description as string)
    case TOOL_NAMES.LIST_PROJECT_FILES:
      return listProjectFiles(
        args.includePattern as string | undefined,
        args.maxDepth as number | undefined,
        currentFiles
      )
    case TOOL_NAMES.GET_COMPONENTS_LIST:
      return getComponentsList(sessionId)
    case TOOL_NAMES.GET_COMPONENT_DOCUMENTATION:
      return getComponentDocumentation(args.component as string)
    case TOOL_NAMES.SHOW_DOCUMENTATION:
      return showDocumentation(
        args.documents as Array<{
          id: string
          title: string
          content: string
          isActive?: boolean
        }>,
        args.switchToDocTab as boolean | undefined
      )
    case TOOL_NAMES.PARSE_FIGMA_TO_DSL:
      return parseFigmaToDSL(args.figmaUrl as string)

    default:
      return {
        success: false,
        action: "unknown",
        error: `Unknown function: ${name}`,
      }
  }
}
