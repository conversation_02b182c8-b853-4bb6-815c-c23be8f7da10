export interface ComponentJSON {
  component: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  props?: Record<string, any>
  children?: ComponentJSON[]
  text?: string
  variant?: string
}

export interface PageJSON {
  pageName: string
  components: ComponentJSON[]
}

export interface ComponentJSONResponse {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rawData?: any
  json: ComponentJSON | PageJSON[]
  dsl?: string // Added DSL property to store the pseudo-code representation
}
