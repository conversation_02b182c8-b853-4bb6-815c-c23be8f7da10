import { Component<PERSON><PERSON><PERSON>, <PERSON><PERSON>SO<PERSON> } from "../dto/generate-json.dto"

/**
 * Generates DSL (Domain-Specific Language) code from Figma component JSON
 * This creates a readable, pseudo-code representation of the UI components
 * in a compact format that's easy to understand
 * @param component The ComponentJSON object to convert to DSL
 * @param indentLevel The current indentation level (for formatting)
 * @returns A string containing the DSL representation
 */
function generateComponentDSL(
  component: ComponentJSON,
  indentLevel = 0
): string {
  const indent = "  ".repeat(indentLevel)

  // Start building the component definition
  let dsl = `${indent}${component.component}`

  // Handle the text content first if it exists (for components like Typography, Button, etc.)
  const propEntries = component.props ? Object.entries(component.props) : []
  let textFromProps = ""

  // Find and extract text from props if it exists
  if (component.props && component.props.text) {
    textFromProps = component.props.text
    propEntries.splice(
      propEntries.findIndex(([key]) => key === "text"),
      1
    )
  }

  // Use component.text directly if it exists, otherwise use text from props
  const displayText = component.text || textFromProps

  // Build the prop list
  const propsPart: string[] = []

  // Add the text as the first argument in parentheses if it exists
  if (displayText) {
    propsPart.push(`"${displayText}"`)
  }

  // Process styling and layout properties
  const styleProps: string[] = []
  const layoutProps: string[] = []

  // Add other props
  for (const [key, value] of propEntries) {
    // Categorize props as style or layout related
    if (isStyleProp(key)) {
      styleProps.push(`${key}="${value}"`)
    } else if (isLayoutProp(key)) {
      layoutProps.push(`${key}="${value}"`)
    } else if (typeof value === "boolean" && value === true) {
      // For boolean props that are true, just add the property name without a value
      propsPart.push(key)
    } else if (typeof value === "string") {
      propsPart.push(`${key}="${value}"`)
    } else {
      propsPart.push(`${key}=${value}`)
    }
  }

  // Handle style object if it exists in props
  if (component.props?.style) {
    const styleObj = component.props.style
    for (const [styleKey, styleValue] of Object.entries(styleObj)) {
      styleProps.push(`${styleKey}="${String(styleValue)}"`)
    }
  }

  // Add style and layout properties to the props list if they exist
  if (styleProps.length > 0) {
    propsPart.push(`style={${styleProps.join(", ")}}`)
  }

  if (layoutProps.length > 0) {
    propsPart.push(`layout={${layoutProps.join(", ")}}`)
  }

  // Add parentheses with props if there are any
  if (propsPart.length > 0) {
    dsl += `(${propsPart.join(", ")})`
  }

  // Add children with proper indentation
  if (component.children && component.children.length > 0) {
    component.children.forEach((child) => {
      dsl += "\n" + generateComponentDSL(child, indentLevel + 1)
    })
  }

  return dsl
}

/**
 * Determines if a property is style-related
 * @param propName The property name to check
 * @returns boolean indicating if it's a style property
 */
function isStyleProp(propName: string): boolean {
  const styleProps = [
    "backgroundColor",
    "background-color",
    "color",
    "fontSize",
    "font-size",
    "fontWeight",
    "font-weight",
    "textAlign",
    "text-align",
    "border",
    "borderRadius",
    "border-radius",
    "margin",
    "padding",
    "opacity",
    "boxShadow",
    "box-shadow",
    "backgroundImage",
    "background-image",
  ]
  return styleProps.includes(propName)
}

/**
 * Determines if a property is layout-related
 * @param propName The property name to check
 * @returns boolean indicating if it's a layout property
 */
function isLayoutProp(propName: string): boolean {
  const layoutProps = [
    "display",
    "flexDirection",
    "flex-direction",
    "justifyContent",
    "justify-content",
    "alignItems",
    "align-items",
    "gap",
    "width",
    "height",
    "minWidth",
    "min-width",
    "maxWidth",
    "max-width",
    "minHeight",
    "min-height",
    "maxHeight",
    "max-height",
    "position",
    "top",
    "right",
    "bottom",
    "left",
    "flex",
    "flexWrap",
    "flex-wrap",
    "gridTemplateColumns",
    "grid-template-columns",
    "gridTemplateRows",
    "grid-template-rows",
  ]
  return layoutProps.includes(propName)
}

/**
 * Generates DSL code for a page of components
 * @param page The PageJSON object to convert to DSL
 * @returns A string containing the DSL representation
 */
function generatePageDSL(page: PageJSON): string {
  let dsl = `// ${page.pageName}\n`

  // Process components directly
  page.components.forEach((component) => {
    dsl += generateComponentDSL(component, 0) + "\n"
  })

  return dsl
}

/**
 * Generates DSL code for multiple pages
 * @param pages An array of PageJSON objects to convert to DSL
 * @returns A string containing the DSL representation
 */
export function generateDSL(pages: PageJSON[]): string {
  let dsl = ""

  pages.forEach((page) => {
    dsl += generatePageDSL(page)
    if (pages.length > 1) {
      dsl += "\n"
    }
  })

  return dsl
}
