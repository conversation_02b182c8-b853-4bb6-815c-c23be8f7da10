/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios"

import {
  ComponentJSON,
  ComponentJSONResponse,
  PageJSON,
} from "../dto/generate-json.dto"
import { generateDSL } from "./dslGenerator"

// Types to represent Figma API responses and our pseudo code structure
interface FigmaFileResponse {
  document: FigmaDocument
  name: string
  lastModified: string
  thumbnailUrl: string
}

interface FigmaDocument {
  children: FigmaNode[]
}

interface FigmaNode {
  id: string
  name: string
  type: string
  children?: FigmaNode[]
  componentId?: string
  characters?: string
  componentProperties?: Record<string, { value: any }>
  layoutMode?: string
  itemSpacing?: number
  paddingLeft?: number
  paddingRight?: number
  paddingTop?: number
  paddingBottom?: number
  layoutSizingHorizontal?: string
  layoutSizingVertical?: string
  layoutAlign?: string
  // Adding style properties
  fills?: Array<{
    type: string
    color?: {
      r: number
      g: number
      b: number
      a: number
    }
    opacity?: number
    visible?: boolean
    blendMode?: string
  }>
  strokes?: Array<{
    type: string
    color?: {
      r: number
      g: number
      b: number
      a: number
    }
    opacity?: number
    weight?: number
  }>
  strokeWeight?: number
  cornerRadius?: number
  rectangleCornerRadii?: number[]
  style?: {
    fontFamily?: string
    fontWeight?: number
    fontSize?: number
    letterSpacing?: number
    lineHeight?: number | { unit: string; value: number }
    textDecoration?: string
    textCase?: string
  }
  effects?: Array<{
    type: string
    visible?: boolean
    radius?: number
    color?: {
      r: number
      g: number
      b: number
      a: number
    }
    offset?: { x: number; y: number }
    spread?: number
  }>
  opacity?: number
  absoluteBoundingBox?: {
    x: number
    y: number
    width: number
    height: number
  }
}

interface ComponentNode {
  name: string
  type: string
  props?: Record<string, any>
  children?: ComponentNode[]
  text?: string
  layout?: {
    type?: string
    spacing?: number
    padding?: {
      left?: number
      right?: number
      top?: number
      bottom?: number
    }
    alignItems?: string
    justifyContent?: string
  }
  style?: {
    backgroundColor?: string
    border?: {
      color?: string
      width?: number
      radius?: number | number[]
    }
    shadow?: {
      color?: string
      blur?: number
      spread?: number
      x?: number
      y?: number
    }[]
    opacity?: number
    width?: number
    height?: number
    font?: {
      family?: string
      size?: number
      weight?: number
      letterSpacing?: number
      lineHeight?: number | string
      textDecoration?: string
      textCase?: string
      color?: string
    }
  }
}

interface PageStructure {
  pageName: string
  components: ComponentNode[]
}

/**
 * Convert Figma RGBA values to CSS rgba string
 * @param color Figma color object
 * @param opacity Optional opacity override
 * @returns CSS rgba string
 */
function rgbaToString(
  color?: { r: number; g: number; b: number; a: number },
  opacity?: number
): string | undefined {
  if (!color) return undefined

  // Convert Figma 0-1 range to 0-255 for RGB
  const r = Math.round(color.r * 255)
  const g = Math.round(color.g * 255)
  const b = Math.round(color.b * 255)

  // Use provided opacity or color's alpha
  const a = opacity !== undefined ? opacity : color.a

  return `rgba(${r}, ${g}, ${b}, ${a})`
}

/**
 * Extract style information from a Figma node
 * @param node Figma node
 * @returns Style object with extracted properties
 */
function extractStyleInformation(node: FigmaNode): ComponentNode["style"] {
  const style: ComponentNode["style"] = {}

  // Extract dimensions if available
  if (node.absoluteBoundingBox) {
    style.width = node.absoluteBoundingBox.width
    style.height = node.absoluteBoundingBox.height
  }

  // Extract opacity
  if (node.opacity !== undefined) {
    style.opacity = node.opacity
  }

  // Extract background color from fills
  if (node.fills && node.fills.length > 0) {
    // Find the first visible solid fill
    const solidFill = node.fills.find(
      (fill) =>
        fill.type === "SOLID" && (fill.visible === undefined || fill.visible)
    )

    if (solidFill && solidFill.color) {
      style.backgroundColor = rgbaToString(solidFill.color, solidFill.opacity)
    }
  }

  // Extract border information
  if (node.strokes && node.strokes.length > 0) {
    const stroke = node.strokes[0] // Use the first stroke

    if (stroke.color) {
      style.border = {
        color: rgbaToString(stroke.color, stroke.opacity),
        width: node.strokeWeight,
      }

      // Extract border radius
      if (node.cornerRadius !== undefined) {
        style.border.radius = node.cornerRadius
      } else if (
        node.rectangleCornerRadii &&
        node.rectangleCornerRadii.length > 0
      ) {
        // If we have individual corner radii
        style.border.radius = node.rectangleCornerRadii
      }
    }
  } else if (
    node.cornerRadius !== undefined ||
    (node.rectangleCornerRadii && node.rectangleCornerRadii.length > 0)
  ) {
    // If we have border radius but no stroke
    style.border = {
      radius:
        node.cornerRadius !== undefined
          ? node.cornerRadius
          : node.rectangleCornerRadii,
    }
  }

  // Extract shadow effects
  if (node.effects && node.effects.length > 0) {
    const shadowEffects = node.effects.filter(
      (effect) =>
        (effect.type === "DROP_SHADOW" || effect.type === "INNER_SHADOW") &&
        (effect.visible === undefined || effect.visible)
    )

    if (shadowEffects.length > 0) {
      style.shadow = shadowEffects.map((effect) => ({
        color: rgbaToString(effect.color),
        blur: effect.radius,
        spread: effect.spread,
        x: effect.offset?.x,
        y: effect.offset?.y,
      }))
    }
  }

  // Extract font information for text nodes
  if (node.type === "TEXT" && node.style) {
    style.font = {
      family: node.style.fontFamily,
      size: node.style.fontSize,
      weight: node.style.fontWeight,
      letterSpacing: node.style.letterSpacing,
      textDecoration: node.style.textDecoration,
      textCase: node.style.textCase,
    }

    // Handle line height which can be auto (number) or fixed (object)
    if (node.style.lineHeight !== undefined) {
      if (typeof node.style.lineHeight === "number") {
        style.font.lineHeight = node.style.lineHeight
      } else if (typeof node.style.lineHeight === "object") {
        // Convert to "value + unit" format
        const lh = node.style.lineHeight as { unit: string; value: number }
        style.font.lineHeight = `${lh.value}${lh.unit === "PIXELS" ? "px" : "%"}`
      }
    }

    // Extract text color from fills
    if (node.fills && node.fills.length > 0) {
      const textFill = node.fills.find(
        (fill) =>
          fill.type === "SOLID" && (fill.visible === undefined || fill.visible)
      )

      if (textFill && textFill.color) {
        style.font.color = rgbaToString(textFill.color, textFill.opacity)
      }
    }
  }

  return Object.keys(style).length > 0 ? style : undefined
}

/**
 * Parse a Figma file from a URL and generate JSON structure
 * @param figmaUrl Figma file URL
 * @returns Array of objects containing pageName and components in JSON format
 */
export async function parseFigmaUrl(
  figmaUrl: string
): Promise<Array<{ pageName: string; components: ComponentJSON[] }>> {
  try {
    // Extract the file key from the Figma URL
    const fileKeyMatch = figmaUrl.match(
      /figma.com\/(file|design)\/([a-zA-Z0-9]+)/
    )
    if (!fileKeyMatch) {
      throw new Error("Invalid Figma URL format")
    }

    const fileKey = fileKeyMatch[2]

    // Extract node ID from URL if present
    const nodeIdMatch = figmaUrl.match(/node-id=([^&]+)/)
    let nodeIds: string[] | undefined = undefined

    if (nodeIdMatch) {
      // Decode URL-encoded node ID and split by comma for multiple nodes
      const decodedNodeId = decodeURIComponent(nodeIdMatch[1])
      nodeIds = decodedNodeId
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id.length > 0)
        .map((id) => {
          // Convert from URL format (2076-4180) to API format (2076:4180)
          return id.replace(/-/g, ":")
        })
    }

    // Fetch Figma data, passing node IDs if available
    const figmaData = await fetchFigmaFile(fileKey, nodeIds)

    // Process data based on whether we're focusing on specific nodes
    const pageStructures = extractPages(figmaData, nodeIds)

    // Convert to JSON format
    return pageStructures.map((page) => ({
      pageName: page.pageName,
      components: convertToComponentJSON(page.components),
    }))
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to parse Figma file: ${error.message}`)
    }
    throw error
  }
}

/**
 * Fetch Figma file data using the Figma API
 * @param fileKey Figma file key
 * @returns Figma file data
 */
async function fetchFigmaFile(
  fileKey: string,
  ids?: string[]
): Promise<FigmaFileResponse> {
  const idsParam = ids && ids?.length > 0 ? `&ids=${ids?.join(",")}` : ""
  const endpoint = `https://api.figma.com/v1/files/${fileKey}?plugin_data=shared${idsParam}`
  const token = process.env.FIGMA_ACCESS_TOKEN ?? ""

  if (!token) {
    throw new Error(
      "Figma API token is missing. Set FIGMA_API_TOKEN in environment variables."
    )
  }

  try {
    const response = await axios.get<FigmaFileResponse>(endpoint, {
      headers: {
        "X-Figma-Token": token,
      },
    })

    console.log("result", response.data)

    return response.data
  } catch (error) {
    console.log("error", error)
    if (axios.isAxiosError(error)) {
      throw new Error(`Figma API request failed: ${error.message}`)
    }
    throw error
  }
}

/**
 * Extract pages and components from Figma data
 *
 * Key improvements:
 * - Handles specific node IDs from URL parameters
 * - More flexible page detection (not limited to [Page] prefix)
 * - Better fallback mechanisms when no pages are found
 * - Enhanced debugging output
 * - Supports multiple node IDs and URL decoding
 *
 * @param figmaData Figma file data
 * @param nodeIds Optional specific node IDs to extract
 * @returns Array of page structures
 */
function extractPages(
  figmaData: FigmaFileResponse,
  nodeIds?: string[]
): PageStructure[] {
  const pages: PageStructure[] = []

  // If specific node IDs are provided, extract them directly
  if (nodeIds && nodeIds.length > 0) {
    console.log(`Looking for specific nodes: ${nodeIds.join(", ")}`)
    const specificNodes = findNodesByIds(figmaData.document, nodeIds)

    console.log(
      `Found ${specificNodes.length} specific nodes:`,
      specificNodes.map((node) => `${node.name} (${node.type})`)
    )

    if (specificNodes.length === 0) {
      console.warn(`No nodes found for IDs: ${nodeIds.join(", ")}`)
      console.log(
        "Available top-level nodes:",
        figmaData.document.children.map(
          (child) => `${child.name} (${child.id}) - ${child.type}`
        )
      )
      // Fallback: treat the entire document as a page
      return [
        {
          pageName: figmaData.name || "Figma Document",
          components: extractAllComponents(figmaData.document),
        },
      ]
    }

    specificNodes.forEach((node) => {
      // For specific nodes, treat them as pages regardless of type
      if (
        node.type === "FRAME" ||
        node.type === "COMPONENT" ||
        node.type === "INSTANCE"
      ) {
        console.log(`Processing specific node: ${node.name} (${node.type})`)
        pages.push({
          pageName: node.name || "Untitled Page",
          components: extractComponents(node),
        })
      } else {
        console.log(
          `Converting non-frame node to page: ${node.name} (${node.type})`
        )
        // For non-frame nodes, wrap them in a page structure
        pages.push({
          pageName: node.name || "Untitled Page",
          components: [mapToComponentNode(node)],
        })
      }
    })

    return pages
  }

  // Process each canvas (Figma page) when no specific nodes are requested
  console.log(
    `Processing ${figmaData.document.children.length} top-level children`
  )

  figmaData.document.children.forEach((canvas) => {
    if (canvas.type !== "CANVAS") {
      console.log(`Skipping non-canvas node: ${canvas.type}`)
      return
    }

    console.log(`Processing canvas: ${canvas.name}`)

    // Find frames in the canvas that represent actual UI pages
    // Made more flexible - accept any frame, not just those with [Page] prefix
    const uiPages = canvas.children?.filter((node) => isUIPage(node)) || []

    console.log(`Found ${uiPages.length} UI pages in canvas: ${canvas.name}`)

    // Process each UI page
    uiPages.forEach((uiPage) => {
      console.log(`Processing UI page: ${uiPage.name}`)
      pages.push({
        pageName: uiPage.name,
        components: extractComponents(uiPage),
      })
    })

    // Fallback: if no UI pages found but canvas has children, include all direct children
    if (uiPages.length === 0 && canvas.children && canvas.children.length > 0) {
      console.log(
        `No UI pages found in canvas ${canvas.name}, including all frames as potential pages`
      )
      const allFrames = canvas.children.filter((node) => node.type === "FRAME")

      allFrames.forEach((frame) => {
        pages.push({
          pageName: frame.name || "Untitled Frame",
          components: extractComponents(frame),
        })
      })
    }
  })

  // Ultimate fallback: if no pages found, extract everything from document
  if (pages.length === 0) {
    console.log("No pages found, using document as fallback")
    pages.push({
      pageName: figmaData.name || "Document",
      components: extractAllComponents(figmaData.document),
    })
  }

  console.log(`Total pages extracted: ${pages.length}`)
  return pages
}

/**
 * Find nodes by their IDs in the document tree
 * @param rootNode Root node to search from (can be document or any node)
 * @param targetIds Array of node IDs to find
 * @returns Array of found nodes
 */
function findNodesByIds(
  rootNode: FigmaDocument | FigmaNode,
  targetIds: string[]
): FigmaNode[] {
  const foundNodes: FigmaNode[] = []
  const checkedNodes: string[] = []

  // Helper function to search recursively
  function searchInNode(node: FigmaNode) {
    checkedNodes.push(node.id)

    // Check if current node matches any target ID
    if (targetIds.includes(node.id)) {
      console.log(`Found target node: ${node.name} (${node.id}) - ${node.type}`)
      foundNodes.push(node)
    }

    // Recursively search children
    if (node.children) {
      for (const child of node.children) {
        searchInNode(child)
      }
    }
  }

  // Start search from all children if it's a document, or the node itself
  if ("children" in rootNode && Array.isArray(rootNode.children)) {
    for (const child of rootNode.children) {
      searchInNode(child)
    }
  }

  console.log(
    `Searched ${checkedNodes.length} nodes, found ${foundNodes.length} matching target IDs`
  )
  if (foundNodes.length === 0) {
    console.log(`Target IDs not found: ${targetIds.join(", ")}`)
    console.log("First 10 checked node IDs:", checkedNodes.slice(0, 10))
  }

  return foundNodes
}

/**
 * Check if a frame is likely to be a UI page rather than a component
 * More flexible approach - accepts frames that could be UI pages
 */
function isUIPage(node: FigmaNode): boolean {
  // Accept FRAME and COMPONENT types as potential UI pages
  if (!["FRAME", "COMPONENT"].includes(node.type)) {
    return false
  }

  // Accept frames with [Page] prefix (explicit marking)
  if (node.name.startsWith("[Page]")) {
    return true
  }

  // Accept frames that have children (likely contain UI elements)
  if (node.children && node.children.length > 0) {
    return true
  }

  // Accept frames with meaningful names (not default Frame names)
  if (node.name && !node.name.match(/^Frame\s*\d*$/i)) {
    return true
  }

  return false
}

/**
 * Extract components from a node
 * @param node Figma node
 * @returns Array of component nodes
 */
function extractComponents(node: FigmaNode): ComponentNode[] {
  if (!node.children) return []

  return node.children
    .filter((child) => isRelevantNode(child))
    .map((child) => mapToComponentNode(child))
}

/**
 * Extract all components from a document or any node tree
 * @param docOrNode Document or node to extract from
 * @returns Array of component nodes
 */
function extractAllComponents(
  docOrNode: FigmaDocument | FigmaNode
): ComponentNode[] {
  const components: ComponentNode[] = []

  // Helper function to recursively extract components
  function extractFromNode(node: FigmaNode) {
    if (isRelevantNode(node)) {
      components.push(mapToComponentNode(node))
    } else if (node.children) {
      // If not a relevant node itself, check its children
      node.children.forEach((child) => extractFromNode(child))
    }
  }

  // Start extraction from all children
  if ("children" in docOrNode && Array.isArray(docOrNode.children)) {
    docOrNode.children.forEach((child) => extractFromNode(child))
  }

  return components
}

/**
 * Check if a node is relevant for our component structure
 */
function isRelevantNode(node: FigmaNode): boolean {
  // Core UI node types that we want to capture
  const relevantTypes = [
    "FRAME",
    "INSTANCE",
    "TEXT",
    "GROUP",
    "RECTANGLE",
    "ELLIPSE",
    "LINE",
    "VECTOR",
    "COMPONENT",
    "COMPONENT_SET",
  ]

  return relevantTypes.includes(node.type)
}

/**
 * Map a Figma node to our component structure
 * @param node Figma node
 * @returns Component node
 */
function mapToComponentNode(node: FigmaNode): ComponentNode {
  const component: ComponentNode = {
    name: getComponentName(node),
    type: mapNodeTypeToComponentType(node),
  }

  // Add properties based on node type
  if (node.componentProperties) {
    component.props = extractProps(node)
  }

  // Add text content for text nodes
  if (node.type === "TEXT" && node.characters) {
    component.text = node.characters
  }

  // Add layout information
  if (node.layoutMode) {
    component.layout = {
      type: node.layoutMode,
      spacing: node.itemSpacing,
      padding: {
        left: node.paddingLeft,
        right: node.paddingRight,
        top: node.paddingTop,
        bottom: node.paddingBottom,
      },
      alignItems: mapLayoutAlign(node.layoutAlign),
      justifyContent: mapLayoutMode(node.layoutMode),
    }
  }

  // Extract style information
  component.style = extractStyleInformation(node)

  // Add children recursively
  if (node.children && node.children.length > 0) {
    component.children = node.children
      .filter((child) => isRelevantNode(child))
      .map((child) => mapToComponentNode(child))
  }

  return component
}

/**
 * Extract component properties
 */
function extractProps(node: FigmaNode): Record<string, any> {
  const props: Record<string, any> = {}

  if (node.componentProperties) {
    for (const [key, prop] of Object.entries(node.componentProperties)) {
      if (prop && typeof prop === "object" && "value" in prop) {
        // Clean up property name (remove variant info)
        const propName = key.split("#")[0]
        props[propName] = prop.value
      }
    }
  }

  return props
}

/**
 * Map Figma node type to a component type that makes sense in our design system
 */
function mapNodeTypeToComponentType(node: FigmaNode): string {
  if (node.type === "INSTANCE") {
    // Try to extract component type from name
    return cleanComponentName(node.name)
  }

  switch (node.type) {
    case "FRAME":
      return node.layoutMode === "HORIZONTAL"
        ? "Row"
        : node.layoutMode === "VERTICAL"
          ? "Column"
          : "Container"
    case "TEXT":
      return "Text"
    case "RECTANGLE":
      return "Box"
    case "ELLIPSE":
      return "Circle"
    case "LINE":
      return "Line"
    case "VECTOR":
      return "Icon"
    case "GROUP":
      return "Group"
    case "COMPONENT":
      return cleanComponentName(node.name)
    case "COMPONENT_SET":
      return "ComponentSet"
    default:
      return cleanComponentName(node.type)
  }
}

/**
 * Clean up component name for better readability
 */
function cleanComponentName(name: string): string {
  // Remove state indicators
  let cleanName = name
    .replace(/\s*\/\s*[A-Za-z]+$/, "")
    .replace(/\s+/g, "")
    .replace(/^[a-z]/, (c) => c.toUpperCase())

  // Handle special characters and invalid component names
  // Replace any non-alphanumeric characters (except for common ones like dash or underscore)
  cleanName = cleanName.replace(/[^a-zA-Z0-9_-]/g, "")

  // Ensure it always starts with a letter
  if (!/^[a-zA-Z]/.test(cleanName)) {
    cleanName = "Component" + cleanName
  }

  // If empty after cleaning, provide a fallback
  if (!cleanName) {
    cleanName = "Component"
  }

  // Standardize layout names (Frame, Frame2, etc.)
  if (/^Frame\d*$/.test(cleanName)) {
    return "Layout"
  }

  return cleanName
}

/**
 * Get component name from node, with fallback based on type
 */
function getComponentName(node: FigmaNode): string {
  // Handle layout components first - standardize Frame, Frame2, etc. to just "Layout"
  if (
    node.type === "FRAME" ||
    (node.name && /frame/i.test(node.name)) ||
    (node.type === "INSTANCE" && /frame/i.test(node.name))
  ) {
    return "Layout"
  }

  if (node.type === "INSTANCE") {
    return cleanComponentName(node.name)
  }

  // For non-instances, use the node name or a fallback based on type
  if (node.name && node.name !== "") {
    return cleanComponentName(node.name)
  }

  switch (node.type) {
    case "FRAME":
      return "Layout"
    case "TEXT":
      return "Text"
    case "RECTANGLE":
      return "Box"
    case "ELLIPSE":
      return "Circle"
    case "LINE":
      return "Line"
    case "VECTOR":
      return "Icon"
    case "GROUP":
      return "Group"
    case "COMPONENT":
      return "Component"
    case "COMPONENT_SET":
      return "ComponentSet"
    default:
      return cleanComponentName(node.type)
  }
}

/**
 * Map layout align to CSS-like values
 */
function mapLayoutAlign(align?: string): string {
  if (!align) return "start"

  switch (align) {
    case "MIN":
      return "start"
    case "CENTER":
      return "center"
    case "MAX":
      return "end"
    case "STRETCH":
      return "stretch"
    default:
      return "start"
  }
}

/**
 * Map layout mode to CSS-like values
 */
function mapLayoutMode(mode?: string): string {
  if (!mode) return "start"

  switch (mode) {
    case "HORIZONTAL":
      return "space-between"
    case "VERTICAL":
      return "space-between"
    default:
      return "start"
  }
}

/**
 * Map text case to CSS-like values
 */
function mapTextCase(textCase?: string): string | undefined {
  if (!textCase) return undefined

  switch (textCase) {
    case "UPPER":
      return "uppercase"
    case "LOWER":
      return "lowercase"
    case "TITLE":
      return "capitalize"
    default:
      return undefined
  }
}

/**
 * Convert component nodes to JSON structure
 * @param components Array of component nodes
 * @returns Array of components in JSON format
 */
function convertToComponentJSON(components: ComponentNode[]): ComponentJSON[] {
  return components.map((component) => {
    const json: ComponentJSON = {
      component: component.name,
    }

    // Handle props and layout together
    const props: Record<string, any> = { ...component.props }

    // Add layout properties to props object
    if (component.layout) {
      if (component.layout.type) {
        props.direction =
          component.layout.type.toLowerCase() === "horizontal"
            ? "horizontal"
            : "vertical"
      }

      if (component.layout.spacing) {
        props.gap = component.layout.spacing.toString()
      }

      if (component.layout.alignItems) {
        props.alignItems = component.layout.alignItems
      }

      if (component.layout.justifyContent) {
        props.justifyContent = component.layout.justifyContent
      }

      // Format padding as "top right bottom left" string
      if (component.layout.padding) {
        const p = component.layout.padding
        const values = [p.top ?? 0, p.right ?? 0, p.bottom ?? 0, p.left ?? 0]
        props.padding = values.join(" ")
      }
    }

    // Add style properties to props
    if (component.style) {
      // Add background color if available
      if (component.style.backgroundColor) {
        props.backgroundColor = component.style.backgroundColor
      }

      // Add border properties
      if (component.style.border) {
        if (component.style.border.color) {
          props.borderColor = component.style.border.color
        }

        if (component.style.border.width) {
          props.borderWidth = component.style.border.width
        }

        if (component.style.border.radius !== undefined) {
          // Handle both single value and array of corner radii
          if (Array.isArray(component.style.border.radius)) {
            props.borderRadius = component.style.border.radius.join(" ")
          } else {
            props.borderRadius = component.style.border.radius
          }
        }
      }

      // Add shadow if available
      if (component.style.shadow && component.style.shadow.length > 0) {
        // Format CSS-like box shadow string
        props.boxShadow = component.style.shadow
          .map((shadow) => {
            const x = shadow.x ?? 0
            const y = shadow.y ?? 0
            const blur = shadow.blur ?? 0
            const spread = shadow.spread ?? 0
            const color = shadow.color ?? "rgba(0,0,0,0.2)"
            return `${x}px ${y}px ${blur}px ${spread}px ${color}`
          })
          .join(", ")
      }

      // Add dimensions if available
      if (component.style.width !== undefined) {
        props.width = component.style.width
      }

      if (component.style.height !== undefined) {
        props.height = component.style.height
      }

      // Add opacity if available
      if (component.style.opacity !== undefined) {
        props.opacity = component.style.opacity
      }

      // Add font styles for text components
      if (component.style.font) {
        const font = component.style.font

        if (font.family) props.fontFamily = font.family
        if (font.size) props.fontSize = font.size
        if (font.weight) props.fontWeight = font.weight
        if (font.letterSpacing) props.letterSpacing = font.letterSpacing
        if (font.lineHeight) props.lineHeight = font.lineHeight
        if (font.textDecoration) props.textDecoration = font.textDecoration
        if (font.textCase) props.textTransform = mapTextCase(font.textCase)
        if (font.color) props.color = font.color
      }
    }

    // Add props if not empty
    if (Object.keys(props).length > 0) {
      json.props = props
    }

    // Handle text components
    if (component.text) {
      // For Typography components, add text directly as a property
      if (component.name === "Typography" || component.name === "Text") {
        json.text = component.text

        // Add a default variant if this is a Typography component
        if (component.name === "Typography" && !json.variant) {
          json.variant = "HeadLine1"
        }
      }
      // For other components, like Button, add text as a prop
      else {
        if (!json.props) json.props = {}
        json.props.text = component.text
      }
    }

    // Convert children recursively
    if (component.children && component.children.length > 0) {
      json.children = convertToComponentJSON(component.children)
    }

    return json
  })
}

/**
 * Get the JSON representation of the Figma components
 * @param figmaLink Figma file URL
 * @returns Promise with component JSON and raw data
 */
export async function getFigmaComponentJSON(
  figmaLink: string
): Promise<ComponentJSONResponse> {
  try {
    // Extract the file key from the Figma URL
    const fileKeyMatch = figmaLink.match(
      /figma.com\/(file|design)\/([a-zA-Z0-9]+)/
    )
    if (!fileKeyMatch) {
      throw new Error("Invalid Figma URL format")
    }

    const fileKey = fileKeyMatch[2]

    // Extract node ID from URL if present
    const nodeIdMatch = figmaLink.match(/node-id=([^&]+)/)
    let nodeIds: string[] | undefined = undefined

    if (nodeIdMatch) {
      // Decode URL-encoded node ID and split by comma for multiple nodes
      const decodedNodeId = decodeURIComponent(nodeIdMatch[1])
      nodeIds = decodedNodeId
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id.length > 0)
        .map((id) => {
          // Convert from URL format (2076-4180) to API format (2076:4180)
          return id.replace(/-/g, ":")
        })
    }

    // Fetch Figma data, passing node IDs if available
    const figmaData = await fetchFigmaFile(fileKey, nodeIds)

    // Process data based on whether we're focusing on specific nodes
    const pageStructures = extractPages(figmaData, nodeIds)

    // Convert to JSON format
    const pagesJSON = pageStructures.map((page) => ({
      pageName: page.pageName,
      components: convertToComponentJSON(page.components),
    }))

    // Always return the array of pages, regardless of how many there are
    const json: PageJSON[] = pagesJSON

    // Generate DSL code from the JSON
    const dsl = generateDSL(json)

    // Return the JSON structure, DSL, and the raw data
    return {
      json,
      dsl,
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to generate component JSON: ${error.message}`)
    }
    throw new Error("Unknown error occurred")
  }
}
