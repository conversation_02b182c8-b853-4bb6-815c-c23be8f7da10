---
slug: 2025-big-update
title: Apollo - Big Update!
authors: [vitaya]
tags: [apollo]
---

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

# Apollo - Big Update!

### 🎉 THANK YOU!

I want to say **Thank You!!!** to everyone who helped make our design system a reality! 🚀✨  
And a huge thanks to everyone using Apollo as well – your support means everything! ❤️

This year, we're planning to make Apollo even better. 🚀💡

### 🔥 Overview

Here are the things we're working on to make it happen! ✅

- ⚙️ **[Working Process](#solid-process)** – Define how to work and communicate between each teams
- 📖 **[Empower Document](#empower-document)** – Enhanced Playground, Dark Mode 🌙, Blog, and much more... 🚀
- 🚀 **[Figma Code Connect](#figma-code-connect)** – Build UI even faster ⚡
- 🛠️ **[Apollo Improvement](#apollo-improvement)** – More flexibility, built on up-to-date libraries 🔄
- 🎨 **[Theming](#theming)** – More customizable UI with flexible theming 🎭

### ⚙️ Working Process

Last year, we faced many issues with communication and design system updates.

To prevent chaos from happening again, we're defining a better workflow to improve communication and visibility.

This includes:

- Properly structured changelogs for packages
- Migration guides for breaking changes
- Improved onboarding documentation for contributors
- Issue/Request Tracking Tools to be able to follow up.

### 📖 Empower Document

We have decided to use a documentation library called “Docusaurus,” which has a lot of useful features.

Here're some of features.

- Darkmode
- Blog
- Document Versioning

This helps us focus solely on documentation and content.

This will accelerate our ability to deliver components more quickly.

We have also integrated the documentation with "Sandpack" as a new playground engine, which runs the code on CodeSandbox.
This means we can test and play with components in many different ways.

Here's an examples ✨

<ComponentPreviewUI
  name="accordion-demo"
  code={`import { Accordion } from '@apollo/ui'; 
  const App = () => {
    return <div><Accordion /></div>
  }; 
  
  export default App`}
/>

### 🚀 Figma Code Connect

To reduce the gap between the documentation and Figma UI,  
we're planning to provide **Figma Code Connect**, which will show code snippets generated from the UI.

![Code Connect Example](./images/code-connect-example.png)

### 🛠️ Apollo Improvement

:::caution
We’re avoiding breaking changes. The only updates will be in the package name and the underlying libraries we use to build the components.

All `@design-systems/*` packages will still be supported and maintained for bug fixes. Once we've fully migrated the components to the new package, we recommend switching to `@apollo/*`.

You can have both packages in the project and gradually update the import paths.
:::

`@apollo/ui` is rebuilding components with [base-ui](https://base-ui.com/) and PureCSS for greater independence combined with `@apollo/token` that offers an expanded collection of design tokens and TailwindCSS presets beyond `@design-systems/tokens`.

- `@apollo/ui` - The collection of components is being rebuilt using [base-ui](https://base-ui.com/) since [@mui/base](https://mui.com/base-ui) was deprecated. Additionally, we are using PureCSS instead of any CSS library to make the library more independent.
- `@apollo/token` - The collection of design tokens and TailwindCSS presets already provides the design tokens. This package will include more design tokens than `@design-systems/tokens`.

### 🎨 Theming

Apollo is used in many projects, each with its own styling. To make customization easier, we're adding a theming feature that allows styling through design token variables.

```tsx
// APP X1
const x1Theme = createTheme({
    colors: {
        --apl-primary-color: gray;
        --apl-secondary-color: black;
        --apl-button-background-color: var(--apl-primary-color);
    }
})

<ThemeProvider theme={x1Theme}>{children}</ThemeProvider>

// APP X2
const x2Theme = createTheme({
    colors: {
        --apl-primary-color: blue;
        --apl-secondary-color: violet;
        --apl-button-background-color: var(--apl-primary-color);
    }
})

<ThemeProvider theme={x2Theme}>{children}</ThemeProvider>
```

### 🗓️ Timeline

By the end of Q2, we plan to integrate all existing components into the new package and documentation, with everything fully settled.

By the way, We’ll be gradually releasing alpha packages to test them before the full release.

### 🙏 Your Feedback and Idea are welcome

We have many plans in place, but your feedback and ideas are just as important! 💡  
You can support us by sharing your thoughts—create a small card on our Trello board

[👉 CLICK HERE](https://trello.com/invite/b/67a228ca443589c9795e1758/ATTI8ee4d94dabd67575e9b61375e7665aadF9886E2F/design-system-feedback)
