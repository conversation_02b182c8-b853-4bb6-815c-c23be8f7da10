import { useCallback, useState } from "react"
import { Autocomplete } from "@apollo/ui"

const allOptions = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE" },
  { label: "Italy", value: "IT" },
]

export default function AutocompleteDemo() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(allOptions)
  const [loading, setLoading] = useState(false)

  const handleSearch = useCallback(async (search: string) => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setOptions(
        allOptions.filter((option) =>
          option.label.toLowerCase().includes(search.toLowerCase())
        )
      )
    } finally {
      setLoading(false)
    }
  }, [])

  return (
    <div style={styles}>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        loading={loading}
        label="Country"
        placeholder="Search countries"
        helperText="Type to search countries"
        debounceMs={300}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
