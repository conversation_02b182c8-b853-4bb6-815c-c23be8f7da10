import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE" },
  { label: "Italy", value: "IT" },
]

export default function AutocompleteDemo() {
  return (
    <div style={styles}>
      <Autocomplete
        options={options}
        label="Country"
        placeholder="Select a country"
        helperText="Choose your country"
      />
      <Autocomplete
        size="small"
        options={options}
        label="Country"
        placeholder="Select a country"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
