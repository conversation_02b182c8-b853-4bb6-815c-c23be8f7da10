import { useState } from "react"
import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
]

export default function AutocompleteDemo() {
  const [search, setSearch] = useState("")
  const [value, setValue] = useState<string>()

  return (
    <div style={styles}>
      <div>Current search: {search}</div>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        search={search}
        onSearch={setSearch}
        label="Controlled Search"
        placeholder="Type to search"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
