import { useState } from "react"
import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK", disabled: true },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE", disabled: true },
  { label: "Italy", value: "IT" },
]

export default function AutocompleteDemo() {
  const [value, setValue] = useState<string>()

  return (
    <div style={styles}>
      <Autocomplete
        options={[]}
        value={value}
        onChange={setValue}
        label="Custom Empty State"
        noOptionsComponent={
          <div style={{ padding: 16, textAlign: "center" }}>
            <p>No options available</p>
            <button>Add new option</button>
          </div>
        }
      />
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        label="Disabled Options"
        placeholder="Some options are disabled"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
