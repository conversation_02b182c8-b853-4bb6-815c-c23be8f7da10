import { useState } from "react"
import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
]

export default function AutocompleteDemo() {
  const [lastEvent, setLastEvent] = useState("")

  return (
    <div style={styles}>
      <div>Last event: {lastEvent}</div>
      <Autocomplete
        options={options}
        label="Event Handling"
        placeholder="Focus/Blur to see events"
        onFocus={() => setLastEvent("Input focused")}
        onBlur={() => setLastEvent("Input blurred")}
        onChange={() => setLastEvent("Value changed")}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
