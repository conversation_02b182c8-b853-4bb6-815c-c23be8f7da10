import { useCallback, useState } from "react"
import { Autocomplete } from "@apollo/ui"

const allOptions = Array.from({ length: 50 }, (_, i) => ({
  label: `Option ${i + 1}`,
  value: `value-${i + 1}`,
}))

export default function AutocompleteDemo() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(allOptions.slice(0, 10))
  const [loadingMore, setLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const handleLoadMore = useCallback(async () => {
    setLoadingMore(true)
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const newOptions = allOptions.slice(options.length, options.length + 10)
      setOptions([...options, ...newOptions])
      setHasMore(options.length + 10 < allOptions.length)
    } finally {
      setLoadingMore(false)
    }
  }, [options])

  return (
    <div style={styles}>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onLoadMore={handleLoadMore}
        loadingMore={loadingMore}
        hasMore={hasMore}
        label="Infinite Scroll"
        placeholder="Select an option"
        helperText="Scroll to load more options"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
