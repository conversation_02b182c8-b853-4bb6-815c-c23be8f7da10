import { useCallback, useState } from "react"
import { Autocomplete } from "@apollo/ui"
import { Loading3Quarters } from "@design-systems/apollo-icons"

const allOptions = Array.from({ length: 20 }, (_, i) => ({
  label: `Option ${i + 1}`,
  value: `value-${i + 1}`,
}))

export default function AutocompleteDemo() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(allOptions.slice(0, 5))
  const [loading, setLoading] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const handleSearch = useCallback(async (search: string) => {
    setLoading(true)
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setOptions(
        allOptions
          .filter((opt) =>
            opt.label.toLowerCase().includes(search.toLowerCase())
          )
          .slice(0, 5)
      )
    } finally {
      setLoading(false)
    }
  }, [])

  const handleLoadMore = useCallback(async () => {
    setLoadingMore(true)
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const newOptions = allOptions.slice(options.length, options.length + 5)
      setOptions([...options, ...newOptions])
      setHasMore(options.length + 5 < allOptions.length)
    } finally {
      setLoadingMore(false)
    }
  }, [options])

  return (
    <div style={styles}>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        onLoadMore={handleLoadMore}
        loading={loading}
        loadingMore={loadingMore}
        hasMore={hasMore}
        label="Custom Loading States"
        placeholder="Type to search..."
        loadingComponent={
          <div
            style={{
              padding: "16px",
              display: "flex",
              alignItems: "center",
              gap: "8px",
              justifyContent: "center",
            }}
          >
            <Loading3Quarters className="animate-spin" />
            <span>Searching options...</span>
          </div>
        }
        loadMoreLabel={
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              padding: "8px",
            }}
          >
            <Loading3Quarters className="animate-spin" size={16} />
            <span>Loading more results...</span>
          </div>
        }
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "300px",
} as const
