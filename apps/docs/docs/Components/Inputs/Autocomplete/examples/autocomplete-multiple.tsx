import { useState } from "react"
import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE" },
  { label: "Italy", value: "IT" },
]

export default function AutocompleteDemo() {
  const [value1, setValue1] = useState<string[]>()
  const [value2, setValue2] = useState<string[]>()

  return (
    <div style={styles}>
      <Autocomplete
        multiple
        options={options}
        value={value1}
        fullWidth
        onChange={setValue1}
        label="Default Select All"
        placeholder="Select countries"
        helperText="Uses default 'Select All' text"
        showSelectAll
      />
      <Autocomplete
        multiple
        options={options}
        value={value2}
        fullWidth
        onChange={setValue2}
        label="Custom Select All"
        placeholder="Select countries"
        helperText="Uses custom select all text"
        showSelectAll
        selectAllText="Choose all countries 🌎"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "100%",
} as const
