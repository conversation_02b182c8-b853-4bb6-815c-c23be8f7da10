import { Autocomplete } from "@apollo/ui"

const options = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
]

export default function AutocompleteDemo() {
  return (
    <div style={styles}>
      <Autocomplete
        options={options}
        label="Required"
        required
        placeholder="This field is required"
      />
      <Autocomplete
        options={options}
        label="Disabled"
        disabled
        placeholder="This field is disabled"
      />
      <Autocomplete
        options={options}
        label="Error State"
        error
        helperText="This is an error message"
      />
      <Autocomplete
        options={options}
        fullWidth
        label="Full Width"
        placeholder="This input takes full width"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  width: "100%",
} as const
