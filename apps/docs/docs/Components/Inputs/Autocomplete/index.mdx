---
title: Autocomplete
id: component:autocomplete
slug: /components/autocomplete
---

import AsyncDemo from "!!raw-loader!./examples/autocomplete-async"
import BasicDemo from "!!raw-loader!./examples/autocomplete-basic"
import ControlledDemo from "!!raw-loader!./examples/autocomplete-controlled"
import CustomizationDemo from "!!raw-loader!./examples/autocomplete-customization"
import EventsDemo from "!!raw-loader!./examples/autocomplete-events"
import InfiniteDemo from "!!raw-loader!./examples/autocomplete-infinite"
import LoadingDemo from "!!raw-loader!./examples/autocomplete-loading"
import MultipleDemo from "!!raw-loader!./examples/autocomplete-multiple"
import StatesDemo from "!!raw-loader!./examples/autocomplete-states"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Autocomplete component is a versatile input that enables users to select one or multiple values from a list of options. It supports features like search, async loading, infinite scroll, and multiple selection modes.

<ComponentPreviewUI name="autocomplete-preview" code={BasicDemo} />

## Props

| Name                      | Type                                                                   | Required | Default        | Description                                          |
| ------------------------- | ---------------------------------------------------------------------- | -------- | -------------- | ---------------------------------------------------- |
| **`options`**             | `Array<{ label: string, value: any, disabled?: boolean }>`             | ✓        | -              | Array of options to display in the dropdown          |
| **`value`**               | `any \| any[]`                                                         |          | -              | Selected value(s). Type depends on `multiple` prop   |
| **`onChange`**            | `(value: any \| any[], event?: React.MouseEvent<HTMLElement>) => void` | ✓        | -              | Callback fired when value changes                    |
| **`multiple`**            | `boolean`                                                              |          | `false`        | If true, allows multiple selections                  |
| **`size`**                | `'small' \| 'medium'`                                                  |          | `'medium'`     | Size of the input                                    |
| **`disabled`**            | `boolean`                                                              |          | `false`        | Disables the input                                   |
| **`error`**               | `boolean`                                                              |          | `false`        | Shows error state                                    |
| **`loading`**             | `boolean`                                                              |          | `false`        | Shows loading state                                  |
| **`label`**               | `string`                                                               |          | -              | Label text                                           |
| **`labelDecorator`**      | `ReactNode`                                                            |          | -              | Additional content to display after the label        |
| **`helperText`**          | `string`                                                               |          | -              | Helper text below input                              |
| **`helperTextDecorator`** | `ReactNode`                                                            |          | -              | Additional content to display after the helper text  |
| **`placeholder`**         | `string`                                                               |          | -              | Input placeholder                                    |
| **`fullWidth`**           | `boolean`                                                              |          | `false`        | Makes input full width                               |
| **`disableSearch`**       | `boolean`                                                              |          | `false`        | Disables search functionality                        |
| **`debounceMs`**          | `number`                                                               |          | `300`          | Debounce time for search in milliseconds             |
| **`onSearch`**            | `(search: string) => Promise<void> \| void`                            |          | -              | Callback fired when search value changes             |
| **`onLoadMore`**          | `() => Promise<void> \| void`                                          |          | -              | Callback for infinite scroll loading                 |
| **`hasMore`**             | `boolean`                                                              |          | `false`        | Indicates if more items can be loaded                |
| **`loadingMore`**         | `boolean`                                                              |          | `false`        | Shows loading state during infinite scroll           |
| **`showSelectAll`**       | `boolean`                                                              |          | `false`        | Shows select all option for multiple select          |
| **`selectAllText`**       | `string`                                                               |          | `'Select All'` | Text for select all option                           |
| **`hideCheckbox`**        | `boolean`                                                              |          | `false`        | Hides checkbox for multiple select                   |
| **`menuLabelText`**       | `string`                                                               |          | -              | Text for menu label option                           |
| **`required`**            | `boolean`                                                              |          | `false`        | Whether the input is required                        |
| **`search`**              | `string`                                                               |          | -              | Controlled search value                              |
| **`loadMoreLabel`**       | `ReactNode`                                                            |          | -              | Label shown during load more operation               |
| **`loadingComponent`**    | `ReactNode`                                                            |          | -              | Custom loading component                             |
| **`noOptionsComponent`**  | `ReactNode`                                                            |          | -              | Custom component shown when no options are available |
| **`filterLogic`**         | `(options: Option[], search: string) => Option[]`                      |          | -              | Custom function to filter options based on search    |
| **`onFocus`**             | `(event: React.FocusEvent<HTMLInputElement>) => void`                  |          | -              | Callback fired when input is focused                 |
| **`onBlur`**              | `(event: React.FocusEvent<HTMLInputElement>) => void`                  |          | -              | Callback fired when input loses focus                |

## Examples

### Basic Usage

Basic single and small variants of the Autocomplete.

<ComponentPreviewUI name="autocomplete-basic" code={BasicDemo} />

### Multiple Selection

Multiple selection with select all functionality.

<ComponentPreviewUI name="autocomplete-multiple" code={MultipleDemo} />

### Async Search

Asynchronous search with loading states.

<ComponentPreviewUI name="autocomplete-async" code={AsyncDemo} />

### Infinite Scroll

Loading more options with infinite scroll.

<ComponentPreviewUI name="autocomplete-infinite" code={InfiniteDemo} />

### Customization

Custom loading, no options, and disabled states.

<ComponentPreviewUI
  name="autocomplete-customization"
  code={CustomizationDemo}
/>

### Input States

Various input states including required, disabled, error, and full width.

<ComponentPreviewUI name="autocomplete-states" code={StatesDemo} />

### Controlled Search

Example of controlling the search input value.

<ComponentPreviewUI name="autocomplete-controlled" code={ControlledDemo} />

### Event Handling

Demonstration of focus, blur and change events.

<ComponentPreviewUI name="autocomplete-events" code={EventsDemo} />

### Custom Loading States

Demonstration of custom loading states and infinite scroll labels.

<ComponentPreviewUI name="autocomplete-loading" code={LoadingDemo} />

## CSS

| Class Name                          | Description                                           |
| ----------------------------------- | ----------------------------------------------------- |
| `.ApolloAutocomplete-root`          | Styles applied to the root element                    |
| `.ApolloAutocomplete-inputRoot`     | Styles applied to the input root container            |
| `.ApolloAutocomplete-inputWrapper`  | Styles applied to the wrapper around input and chips  |
| `.ApolloAutocomplete-trigger`       | Styles applied to the trigger element                 |
| `.ApolloAutocomplete-dropdownRoot`  | Styles applied to the dropdown menu root              |
| `.ApolloAutocomplete-menuItem`      | Styles applied to each menu item                      |
| `.ApolloAutocomplete-checkboxItem`  | Styles applied to menu items with checkboxes          |
| `.ApolloAutocomplete-chipContainer` | Styles applied to the container of selected chips     |
| `.ApolloAutocomplete-chip`          | Styles applied to each selected value chip            |
| `.ApolloAutocomplete-clearButton`   | Styles applied to the clear selection button          |
| `.ApolloAutocomplete-toggleButton`  | Styles applied to the dropdown toggle button          |
| `.ApolloAutocomplete-chevronIcon`   | Styles applied to the chevron icon                    |
| `.ApolloAutocomplete-scrollTrigger` | Styles applied to the infinite scroll trigger element |
