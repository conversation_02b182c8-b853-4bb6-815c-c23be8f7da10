import { But<PERSON> } from "@apollo/ui"
import { Smile } from "@design-systems/apollo-icons"

export default function ButtonDemo() {
  return (
    <div style={styles}>
      <Button startDecorator={<Smile size={16} />}>Primary</Button>
      <Button endDecorator={<Smile size={16} />}>Primary</Button>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
