---
title: Button
id: component:button
slug: /components/button
---

import ColorDemo from "!!raw-loader!./examples/button-color"
import DecoratorDemo from "!!raw-loader!./examples/button-decorator"
import PreviewCode from "!!raw-loader!./examples/button-demo"
import DisabledDemo from "!!raw-loader!./examples/button-disabled"
import LinkDemo from "!!raw-loader!./examples/button-link"
import VariantDemo from "!!raw-loader!./examples/button-variant"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Button component is a flexible and customizable UI element for triggering actions. It supports various styles, sizes, colors, and can be rendered as different HTML elements like `button` or `a`.

<ComponentPreviewUI name="button-preview" code={PreviewCode} />

## Props

> **Note**: The props also extend the `HTMLAttributes` of the `button` or `anchor` element based on which one will be rendered.

| Name                 | Type                              | Required | Default   | Description                                                                                                                               |
| -------------------- | --------------------------------- | -------- | --------- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| **`color`**          | `'negative' \| 'primary'`           |          | `primary` | The color of the component. It supports those theme colors that make sense for this component.                                            |
| **`disabled`**       | `bool`                            |          | `false`   | If `true`, the component is disabled. <br />**Note**: This prop is not available if the component is rendered as a link.                  |
| **`endDecorator`**   | `node`                            |          | -         | Element placed after the children.                                                                                                        |
| **`fullWidth`**      | `bool`                            |          | `false`   | If `true`, the button will take up the full width of its container.                                                                       |
| **`size`**           | `'large' \| 'small'`             |          | `large`  | The size of the component.                                                                                                                |
| **`startDecorator`** | `node`                            |          | -         | Element placed before the children.                                                                                                       |
| **`variant`**        | `'outline' \| 'text' \| 'filled'` |          | `filled`   | The variant of the button.                                                                                                                |
| **`href`**           | `string`                          |          | -         | If provided, the button will be rendered as an anchor element.                                                                            |
| **`fileType`**       | `string`                          |          | -         | The type attribute of the anchor element, renamed to avoid conflict with button's `type` attribute. Example: `fileType="application/pdf"` |

## Examples

### Variants

Different styles of buttons including filled, outline, and text.

<ComponentPreviewUI name="button-variant" code={VariantDemo} />

### Color

Buttons with different color options like primary and negative.

<ComponentPreviewUI name="button-color" code={ColorDemo} />

### Decorators

Buttons with elements placed before or after the button text.

<ComponentPreviewUI name="button-decorators" code={DecoratorDemo} />

### Disabled

Buttons that are disabled and not interactive.

<ComponentPreviewUI name="button-disabled" code={DisabledDemo} />

### As Link

Buttons rendered as anchor elements using the `href` prop.

<ComponentPreviewUI name="button-link" code={LinkDemo} />

## CSS

| Class Name           | Description                          |
| -------------------- | ------------------------------------ |
| `.ApolloButton-root` | Styles applied to the root container |

## 🚨 Migration Guide from old version

How to migrate from `@design-systems/apollo-ui` to `@apollo/ui`.

### Changes

- **Breaking Changes**:

  - Removed `loading`, `loadingIndicator`, `loadingPosition`, `action`, and `as` props.
  - Updated `size` prop values to `'medium'` and `'small'`.
  - Added `href` prop to render the button as an anchor element.

- **New Features**:

  - Introduced new `href` prop for rendering the button as an anchor element.

- **Improvements**:
  - Enhanced styling and customization options.
  - Improved accessibility and performance.

### To migrate to the new Button component, follow these steps:

1. **Update Imports**:
   Ensure you are importing the Button component from the correct path.

   ```tsx
   import { Button } from "@apollo/ui"
   ```

2. **Update `size` Prop**:
   Replace any usage of the `size` prop with the new values `'large'` or `'small'`.

   ```tsx
   // Old
   <Button size="md">Button</Button>
   <Button size="lg">Button</Button>

   // New
   <Button size="small">Button</Button>
   <Button size="large">Button</Button>
   ```

3. **Remove Deprecated Props**:
   Remove any deprecated props such as `loading`, `loadingIndicator`, `loadingPosition`, `action`, and `as`.

   ```tsx
   // Old
   <Button loading loadingIndicator={<Spinner />} loadingPosition="start">Button</Button>

   // New
   <Button>Button</Button>
   ```

4. **Add `href` Prop**:
   If you need the button to render as an anchor element, add the `href` prop.

   ```tsx
   <Button href="https://example.com">Link Button</Button>
   ```

By following these steps, you can successfully migrate to the new Button component with updated props and functionality.
