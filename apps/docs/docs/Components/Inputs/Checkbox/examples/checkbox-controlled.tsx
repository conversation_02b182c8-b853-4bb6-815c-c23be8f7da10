import { useState } from "react"
import { Checkbox } from "@apollo/ui"

export default function CheckboxControlledDemo() {
  const [checked, setChecked] = useState(false)

  return (
    <div style={styles}>
      <Checkbox
        label={`Controlled Checkbox (${checked ? "Checked" : "Unchecked"})`}
        checked={checked}
        onChange={(event, isChecked) => setChecked(isChecked)}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  alignItems: "flex-start",
} as const
