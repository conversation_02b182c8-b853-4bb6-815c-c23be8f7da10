import { Checkbox } from "@apollo/ui"

export default function CheckboxDemo() {
  return (
    <div style={styles}>
      <div style={{ display: "flex", gap: 2 }}>
        <Checkbox />
        <Checkbox indeterminate />
        <Checkbox defaultChecked />
        <Checkbox disabled />
        <Checkbox checked disabled />
      </div>
      <Checkbox label="Default Checkbox" />
      <Checkbox label="Checked Checkbox" indeterminate />
      <Checkbox label="Checked Checkbox" checked />
      <Checkbox label="Disabled Checkbox" disabled />
      <Checkbox label="Checked and Disabled" checked disabled />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  alignItems: "flex-start",
} as const
