import { useState } from "react"
import { Checkbox } from "@apollo/ui"

export default function CheckboxIndeterminateDemo() {
  const [checkedItems, setCheckedItems] = useState([false, false, false])

  const parentChecked = checkedItems.every(Boolean)
  const parentIndeterminate = checkedItems.some(Boolean) && !parentChecked

  const handleParentChange = () => {
    setCheckedItems(checkedItems.map(() => !parentChecked))
  }

  const handleChildChange = (index: number) => {
    setCheckedItems(checkedItems.map((c, i) => (i === index ? !c : c)))
  }

  return (
    <div style={styles}>
      <Checkbox
        label="Select All"
        checked={parentChecked}
        indeterminate={parentIndeterminate}
        onChange={handleParentChange}
      />
      <div style={childStyles}>
        {checkedItems.map((checked, index) => (
          <Checkbox
            key={index}
            label={`Item ${index + 1}`}
            checked={checked}
            onChange={() => handleChildChange(index)}
          />
        ))}
      </div>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  alignItems: "flex-start",
} as const

const childStyles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  paddingLeft: "24px",
} as const
