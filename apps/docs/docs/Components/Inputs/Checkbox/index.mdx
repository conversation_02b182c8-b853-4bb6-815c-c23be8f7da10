---
title: Checkbox
id: component:checkbox
slug: /components/checkbox
---

import ControlledDemo from "!!raw-loader!./examples/checkbox-controlled"
import BasicDemo from "!!raw-loader!./examples/checkbox-demo"
import DisabledDemo from "!!raw-loader!./examples/checkbox-disabled"
import IndeterminateDemo from "!!raw-loader!./examples/checkbox-indeterminate"
import LabelDemo from "!!raw-loader!./examples/checkbox-label"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Checkbox component is used for selecting single or multiple options. It supports various states including checked, unchecked, indeterminate, and disabled states.

<ComponentPreviewUI name="checkbox-preview" code={BasicDemo} />

## Props

> **Note**: The props extend the [BaseUICheckbox.Root.Props](https://base-ui.com/react/components/checkbox#root).

| Name                 | Type                                       | Required | Default | Description                                          |
| -------------------- | ------------------------------------------ | -------- | ------- | ---------------------------------------------------- |
| **`label`**          | `ReactNode`                                |          | -       | The label content for the checkbox                   |
| **`checked`**        | `boolean`                                  |          | -       | If `true`, the checkbox will be checked (controlled) |
| **`defaultChecked`** | `boolean`                                  |          | `false` | The default checked state (uncontrolled)             |
| **`ref`**            | `Ref<HTMLInputElement>`                    |          | -       | Ref for the underlying input element                 |
| **`rootRef`**        | `Ref<HTMLLabelElement>`                    |          | -       | Ref for the root label element                       |
| **`wrapperRef`**     | `Ref<HTMLButtonElement>`                   |          | -       | Ref for the wrapper button element                   |
| **`onChange`**       | `(event: Event, checked: boolean) => void` |          | -       | Callback fired when the checkbox state changes       |

## Examples

### Basic Usage

Basic checkbox examples showing different states.

<ComponentPreviewUI name="checkbox-basic" code={BasicDemo} />

### With Labels

Checkboxes with different label configurations.

<ComponentPreviewUI name="checkbox-label" code={LabelDemo} />

### Disabled State

Examples of disabled checkboxes.

<ComponentPreviewUI name="checkbox-disabled" code={DisabledDemo} />

### Indeterminate State

Checkbox with indeterminate state, commonly used in select-all scenarios.

<ComponentPreviewUI name="checkbox-indeterminate" code={IndeterminateDemo} />

### Controlled Component

Example of a controlled checkbox component.

<ComponentPreviewUI name="checkbox-controlled" code={ControlledDemo} />

## CSS

| Class Name                 | Description                            |
| -------------------------- | -------------------------------------- |
| `.ApolloCheckbox-root`     | Styles applied to the root element     |
| `.ApolloCheckbox-checkbox` | Styles applied to the checkbox element |
| `.ApolloCheckbox-label`    | Styles applied to the label element    |
