import { useState } from "react"
import { DatePicker } from "@apollo/ui"

export default function DatePickerFormat() {
  const [date, setDate] = useState<Date | null>(null)

  return (
    <div style={styles}>
      <DatePicker
        label="Custom format"
        placeholder="Select date"
        format="dd/MM/bbbb"
        value={date}
        onChange={setDate}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  flexDirection: "column",
  gap: "16px",
  width: "100%",
  maxWidth: "320px",
} as any
