import { useState } from "react"
import { DatePicker } from "@apollo/ui"

export default function DatePickerViewMode() {
  const [dateMonthView, setDateMonthView] = useState<Date | null>(null)
  const [dateYearView, setDateYearView] = useState<Date | null>(null)

  return (
    <div style={styles.container}>
      <div style={styles.row}>
        <DatePicker
          label="Month picker"
          placeholder="Select month"
          showMonthYearPicker
          value={dateMonthView}
          onChange={setDateMonthView}
        />

        <DatePicker
          label="Year picker"
          placeholder="Select year"
          showYearPicker
          value={dateYearView}
          onChange={setDateYearView}
        />
      </div>
    </div>
  )
}

const styles = {
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    width: "100%",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    gap: "24px",
    flexWrap: "wrap",
  },
} as any
