---
title: DatePicker
id: component:datepicker
slug: /components/datepicker
---

import BasicDemo from "!!raw-loader!./examples/datepicker-basic"
import DisabledDemo from "!!raw-loader!./examples/datepicker-disabled"
import EraDemo from "!!raw-loader!./examples/datepicker-era"
import ErrorDemo from "!!raw-loader!./examples/datepicker-error"
import FormatDemo from "!!raw-loader!./examples/datepicker-format"
import MinMaxDemo from "!!raw-loader!./examples/datepicker-minmax"
import RangeDemo from "!!raw-loader!./examples/datepicker-range"
import ViewModeDemo from "!!raw-loader!./examples/datepicker-viewmode"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The DatePicker component provides an interactive calendar interface for selecting dates. It supports various features like date ranges, month/year views, different era formats, and custom date formatting.

<ComponentPreviewUI name="datepicker-basic" code={BasicDemo} />

## Props

> **Note**: The DatePicker component extends props from react-datepicker. For additional props, refer to the [react-datepicker documentation](https://reactdatepicker.com/).

| Name                                  | Type                           | Required | Default | Description                                                               |
| ------------------------------------- | ------------------------------ | -------- | ------- | ------------------------------------------------------------------------- |
| **`value`**                           | `Date \| null`                 |          | `null`  | The selected date value.                                                  |
| **`onChange`**                        | `(date: Date \| null) => void` |          |         | Function called when the selected date changes.                           |
| **`isRange`**                         | `boolean`                      |          | `false` | If `true`, enables date range selection.                                  |
| **`startDate`**                       | `Date \| null`                 |          | `null`  | The start date when using range selection.                                |
| **`endDate`**                         | `Date \| null`                 |          | `null`  | The end date when using range selection.                                  |
| **`label`**                           | `ReactNode`                    |          |         | The label for the date picker input.                                      |
| **`placeholder`**                     | `string`                       |          |         | The placeholder text for the input.                                       |
| **`helperText`**                      | `ReactNode`                    |          |         | Helper text displayed below the input.                                    |
| **`error`**                           | `boolean`                      |          | `false` | If `true`, displays an error state.                                       |
| **`disabled`**                        | `boolean`                      |          | `false` | If `true`, disables the date picker.                                      |
| **`format`**                          | `string`                       |          |         | Custom date format.                                                       |
| **`era`**                             | `'bd' \| 'ad'`                 |          | `'bd'`  | The era to use for dates (`bd` for Buddhist era, `ad` for Gregorian era). |
| **`locale`**                          | `'th' \| 'en'`                 |          | `'th'`  | The locale to use for the date picker.                                    |
| **`minDate`**                         | `Date`                         |          |         | The minimum selectable date.                                              |
| **`maxDate`**                         | `Date`                         |          |         | The maximum selectable date.                                              |
| **`showMonthYearPicker`**             | `boolean`                      |          | `false` | If `true`, displays a month picker instead of a day picker.               |
| **`showYearPicker`**                  | `boolean`                      |          | `false` | If `true`, displays a year picker instead of a day/month picker.          |
| **`hideCalendarMonth`**               | `boolean`                      |          | `false` | If `true`, hides the month dropdown in the calendar header.               |
| **`hideCalendarYear`**                | `boolean`                      |          | `false` | If `true`, hides the year dropdown in the calendar header.                |
| **`shouldBackToDateViewAfterSelect`** | `boolean`                      |          | `true`  | If `true`, returns to date view after selecting a month or year.          |
| **`inputProps`**                      | `InputProps`                   |          |         | Additional props passed to the underlying Input component.                |

## Examples

### Basic Usage

A simple date picker with default settings.

<ComponentPreviewUI name="datepicker-basic" code={BasicDemo} />

### Error State

A date picker displaying an error state.

<ComponentPreviewUI name="datepicker-error" code={ErrorDemo} />

### Disabled State

A disabled date picker that cannot be interacted with.

<ComponentPreviewUI name="datepicker-disabled" code={DisabledDemo} />

### Date Range Selection

A date picker configured for selecting a range of dates.

<ComponentPreviewUI name="datepicker-range" code={RangeDemo} />

### Different View Modes

Date pickers configured to show month and year views.

<ComponentPreviewUI name="datepicker-viewmode" code={ViewModeDemo} />

### Era Options

Date pickers using different era formats (Buddhist and Gregorian).

<ComponentPreviewUI name="datepicker-era" code={EraDemo} />

### Custom Format

A date picker with a custom date format.

<ComponentPreviewUI name="datepicker-format" code={FormatDemo} />

### Min/Max Date Constraints

A date picker with minimum and maximum selectable dates.

<ComponentPreviewUI name="datepicker-minmax" code={MinMaxDemo} />

## CSS

| Class Name                          | Description                                 |
| ----------------------------------- | ------------------------------------------- |
| `.ApolloDatePicker-root`            | Styles applied to the root element          |
| `.ApolloDatePicker-calendarHeader`  | Styles applied to the calendar header       |
| `.ApolloDatePicker-prevMonthButton` | Styles applied to the previous month button |
| `.ApolloDatePicker-nextMonthButton` | Styles applied to the next month button     |
| `.ApolloDatePicker-monthButton`     | Styles applied to the month button          |
| `.ApolloDatePicker-yearButton`      | Styles applied to the year button           |
| `.ApolloDatePicker-day`             | Styles applied to the day elements          |
| `.ApolloDatePicker-month`           | Styles applied to the month elements        |
| `.ApolloDatePicker-year`            | Styles applied to the year elements         |
