import { FloatButton } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

export default function FloatButtonDemo() {
  return (
    <div style={styles}>
      <FloatButton isExpanded label="Smile" icon={<Smile />} />
      <FloatButton label="Heart" icon={<Heart />} size="small" />
      <FloatButton
        label="Heart"
        color="danger"
        icon={<Heart />}
        size="medium"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
