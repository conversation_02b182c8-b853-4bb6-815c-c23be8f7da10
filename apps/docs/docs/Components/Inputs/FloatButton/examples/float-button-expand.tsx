import { FloatButton } from "@apollo/ui"
import { <PERSON> } from "@design-systems/apollo-icons"

export default function FloatButtonExpandDemo() {
  return (
    <div style={styles}>
      <FloatButton label="Expand" icon={<Link />} isExpanded />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
