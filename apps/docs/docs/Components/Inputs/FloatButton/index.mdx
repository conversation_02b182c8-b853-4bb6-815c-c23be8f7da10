---
title: FloatButton
id: component:float-button
slug: /components/float-button
---

import PreviewCode from "!!raw-loader!./examples/float-button-demo"
import DisabledDemo from "!!raw-loader!./examples/float-button-disabled"
import ExpandDemo from "!!raw-loader!./examples/float-button-expand"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

FloatButton is a button that uses an icon and a label. It can be a normal button or a link. It comes in various sizes and can be expanded.

<ComponentPreviewUI name="float-button-preview" code={PreviewCode} />

## Props

> **Note**: The props also extend the `HTMLAttributes` of the `button` or `anchor` element based on which one will be rendered.

| Name             | Type               | Required | Default | Description                                                                                                              |
| ---------------- | ------------------ | -------- | ------- | ------------------------------------------------------------------------------------------------------------------------ |
| **`icon`**       | `ReactNode`        | ✓        | -       | The icon to be displayed inside the button.                                                                              |
| **`label`**      | `ReactNode`        | ✓        | -       | The label to be displayed inside the button.                                                                             |
| **`iconSide`**   | `'start' \| 'end'` |          | `start` | The side of the icon relative to the label.                                                                              |
| **`isExpanded`** | `bool`             |          | `false` | If `true`, the button will be expanded.                                                                                  |
| **`disabled`**   | `bool`             |          | `false` | If `true`, the component is disabled. <br />**Note**: This prop is not available if the component is rendered as a link. |
| **`href`**       | `string`           |          | -       | If provided, the button will be rendered as an anchor element.                                                           |

## Examples

### Default

Float buttons with different sizes and icons.

<ComponentPreviewUI name="float-button-preview" code={PreviewCode} />

### Expanded

Float buttons that are expanded.

<ComponentPreviewUI name="float-button-expand" code={ExpandDemo} />

### Disabled

Float buttons that are disabled and not interactive.

<ComponentPreviewUI name="float-button-disabled" code={DisabledDemo} />

## CSS

| Class Name                 | Description                          |
| -------------------------- | ------------------------------------ |
| `.ApolloFloatButton-root`  | Styles applied to the root container |
| `.ApolloFloatButton-icon`  | Styles applied to the icon element   |
| `.ApolloFloatButton-label` | Styles applied to the label element  |
