import { IconButton } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

export default function IconButtonDemo() {
  return (
    <div style={styles}>
      <IconButton>
        <Smile />
      </IconButton>
      <IconButton size="small">
        <Heart />
      </IconButton>
      <IconButton size="large">
        <Heart />
      </IconButton>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
