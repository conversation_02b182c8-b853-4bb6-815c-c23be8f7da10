import { IconButton } from "@apollo/ui"
import { <PERSON> } from "@design-systems/apollo-icons"

export default function ButtonDemo() {
  return (
    <div style={styles}>
      <IconButton
        size="large"
        href="https://cjx-apollo-ui.netlify.app/"
        target="_blank"
      >
        <Link />
      </IconButton>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
