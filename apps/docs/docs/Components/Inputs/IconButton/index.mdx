---
title: IconButton
id: component:icon-button
slug: /components/icon-button
---

import ColorDemo from "!!raw-loader!./examples/icon-button-color"
import PreviewCode from "!!raw-loader!./examples/icon-button-demo"
import DisabledDemo from "!!raw-loader!./examples/icon-button-disabled"
import LinkDemo from "!!raw-loader!./examples/icon-button-link"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

IconButton is a button that uses an icon. It can be a normal button or a link. It comes in various sizes and colors.

<ComponentPreviewUI name="icon-button-preview" code={PreviewCode} />

## Props

> **Note**: The props also extend the `HTMLAttributes` of the `button` or `anchor` element based on which one will be rendered.

| Name           | Type                              | Required | Default   | Description                                                                                                              |
| -------------- | --------------------------------- | -------- | --------- | ------------------------------------------------------------------------------------------------------------------------ |
| **`color`**    | `'negative' \| 'primary'`         |          | `primary` | The color of the component. It supports those theme colors that make sense for this component.                           |
| **`disabled`** | `bool`                            |          | `false`   | If `true`, the component is disabled. <br />**Note**: This prop is not available if the component is rendered as a link. |
| **`size`**     | `'large' \| 'small'`              |          | `medium`  | The size of the component.                                                                                               |
| **`variant`**  | `'outline' \| 'icon' \| 'filled'` |          | `icon`    | The variant of the button.                                                                                               |
| **`href`**     | `string`                          |          | -         | If provided, the button will be rendered as an anchor element.                                                           |

## Examples

### Color

Icon buttons with different color options like primary and negative.

<ComponentPreviewUI name="icon-button-color" code={ColorDemo} />

### Disabled

Icon buttons that are disabled and not interactive.

<ComponentPreviewUI name="icon-button-disabled" code={DisabledDemo} />

### As Link

Icon buttons rendered as anchor elements using the `href` prop.

<ComponentPreviewUI name="icon-button-link" code={LinkDemo} />

## CSS

| Class Name               | Description                          |
| ------------------------ | ------------------------------------ |
| `.ApolloIconButton-root` | Styles applied to the root container |
