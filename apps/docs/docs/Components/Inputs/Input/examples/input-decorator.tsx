import { Input } from "@apollo/ui"

export default function InputDecoratorDemo() {
  return (
    <div style={styles}>
      <Input
        label="Input with Start Decorator"
        startDecorator={<span>Start</span>}
        placeholder="Enter text"
      />
      <Input
        label="Input with End Decorator"
        endDecorator={<span>End</span>}
        placeholder="Enter text"
      />
      <Input
        label="Input with Both Decorators"
        startDecorator={<span>Start</span>}
        endDecorator={<span>End</span>}
        placeholder="Enter text"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
} as any
