import { Input } from "@apollo/ui"

export default function InputDemo() {
  return (
    <div style={styles}>
      <Input label="Default Input" placeholder="Enter text" />
      <Input label="Input with Helper Text" helperText="Helper text" />
      <Input label="Input with Error" error helperText="Error text" />
      <Input label="Disabled Input" disabled placeholder="Enter text" />
      <Input label="Full Width Input" fullWidth placeholder="Enter text" />
      <Input
        label="Input with Decorators"
        startDecorator={<span>Start</span>}
        endDecorator={<span>End</span>}
        placeholder="Enter text"
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
} as any
