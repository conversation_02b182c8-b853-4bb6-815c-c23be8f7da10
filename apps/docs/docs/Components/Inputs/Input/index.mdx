---
title: Input
id: component:input
slug: /components/input
---

import DecoratorDemo from "!!raw-loader!./examples/input-decorator"
import PreviewCode from "!!raw-loader!./examples/input-demo"
import DisabledDemo from "!!raw-loader!./examples/input-disabled"
import ErrorDemo from "!!raw-loader!./examples/input-error"
import FullWidthDemo from "!!raw-loader!./examples/input-fullwidth"
import LabelHelperTextDemo from "!!raw-loader!./examples/input-label-helpertext"
import RequiredDemo from "!!raw-loader!./examples/input-required"
import SizeDemo from "!!raw-loader!./examples/input-size"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Input component is a flexible and customizable UI element for user input. It supports various styles, sizes, decorators, and can display helper text and error messages.

<ComponentPreviewUI name="input-preview" code={PreviewCode} />

## Props

> **Note**: The props also extend the `InputHTMLAttributes` of the `input` element.

| Name                 | Type                             | Required | Default  | Description                                                        |
| -------------------- | -------------------------------- | -------- | -------- | ------------------------------------------------------------------ |
| **`fullWidth`**      | `bool`                           |          | `false`  | If `true`, the input will take up the full width of its container. |
| **`size`**           | `'medium' \| 'small'`            |          | `medium` | The size of the component.                                         |
| **`startDecorator`** | `ReactNode`                      |          | -        | Element placed before the input.                                   |
| **`endDecorator`**   | `ReactNode`                      |          | -        | Element placed after the input.                                    |
| **`error`**          | `bool`                           |          | `false`  | If `true`, the input will display an error state.                  |
| **`label`**          | `ReactNode`                      |          | -        | The label for the input.                                           |
| **`labelDecorator`** | `ReactNode`                      |          | -        | Additional content to display after the label.                     |
| **`helperText`**     | `ReactNode`                      |          | -        | The helper text for the input.                                     |
| **`helperTextDecorator`** | `ReactNode`                      |          | -        | Additional content to display after the helper text.               |
| **`required`**       | `bool`                           |          | `false`  | If `true`, the input will display a required indicator.            |
| **`hasCharacterCount`** | `bool`                           |          | `false`  | If `true`, the input will display a count of characters.           |
| **`maxLength`**      | `number`                         |          | -        | The maximum number of characters allowed.                          |
| **`rootRef`**        | `Ref<HTMLDivElement>`            |          | -        | Ref for the root element.                                          |
| **`rootProps`**      | `HTMLAttributes<HTMLDivElement>` |          | -        | Props for the root element.                                        |
| **`fieldProps`**     | `FieldProps`                     |          | -        | Props for the field element.                                       |

## Examples

### Sizes

Different sizes of inputs including medium and small.

<ComponentPreviewUI name="input-size" code={SizeDemo} />

### Decorators

Inputs with elements placed before or after the input field.

<ComponentPreviewUI name="input-decorators" code={DecoratorDemo} />

### Disabled

Inputs that are disabled and not interactive.

<ComponentPreviewUI name="input-disabled" code={DisabledDemo} />

### Error State

Inputs that display an error state.

<ComponentPreviewUI name="input-error" code={ErrorDemo} />

### Full Width

Inputs that take up the full width of their container.

<ComponentPreviewUI name="input-fullwidth" code={FullWidthDemo} />

### Label and Helper Text

Inputs with a label and helper text displayed.

<ComponentPreviewUI name="input-label-helpertext" code={LabelHelperTextDemo} />

### Required

Inputs that display a required indicator.

<ComponentPreviewUI name="input-required" code={RequiredDemo} />

## CSS

| Class Name                    | Description                                   |
| ----------------------------- | --------------------------------------------- |
| `.ApolloInput-fieldRoot`      | Styles applied to the field root              |
| `.ApolloInput-controlRoot`    | Styles applied to the control root            |
| `.ApolloInput-startDecorator` | Styles applied to the start decorator element |
| `.ApolloInput-endDecorator`   | Styles applied to the end decorator element   |
