import { useState } from "react"
import { Radio, RadioGroup, Typography } from "@apollo/ui"

export default function RadioControlledDemo() {
  const [selected, setSelected] = useState<string>("apple")

  return (
    <div style={styles}>
      <div>
        <RadioGroup
          name="fruits"
          value={selected}
          onValueChange={(value) => setSelected(value as string)}
        >
          <Radio value="apple">Apple</Radio>
          <Radio value="banana">Banana</Radio>
          <Radio value="cherry">Cherry</Radio>
        </RadioGroup>
        <Typography>Selected: {selected}</Typography>
      </div>
    </div>
  )
}

const styles = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
}
