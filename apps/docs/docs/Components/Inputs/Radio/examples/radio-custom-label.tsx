import { Radio, RadioGroup, Typography } from "@apollo/ui"

export default function RadioCustomLabelDemo() {
  return (
    <div style={styles}>
      <RadioGroup name="fruits" defaultValue="apple">
        <Radio value="apple">
          <Typography level="h2">Huge Word</Typography>
        </Radio>
        <Radio value="banana">
          <Typography level="h3">Big Word</Typography>
        </Radio>
        <Radio value="cherry">Normal Word</Radio>
      </RadioGroup>
    </div>
  )
}

const styles = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
}
