---
title: Radio
id: component:radio
slug: /components/radio
---

import BasicDemo from "!!raw-loader!./examples/radio-basic"
import ControlledDemo from "!!raw-loader!./examples/radio-controlled"
import CustomLabelDemo from "!!raw-loader!./examples/radio-custom-label"
import DisabledDemo from "!!raw-loader!./examples/radio-disabled"
import HorizontalDemo from "!!raw-loader!./examples/radio-horizontal"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The `Radio` and `RadioGroup` components allow users to select one option from a set. They support various layouts, states, and custom labels.

<ComponentPreviewUI name="radio-preview" code={BasicDemo} />

## Props

### RadioGroup

| Name                | Type                         | Required | Default      | Description                    |
| ------------------- | ---------------------------- | -------- | ------------ | ------------------------------ |
| **`name`**          | `string`                     |          | `null`       | Name attribute for the group.  |
| **`defaultValue`**  | `string`                     |          | `null`       | Initial selected value.        |
| **`value`**         | `string`                     |          | `null`       | Controlled selected value.     |
| **`onValueChange`** | `function`                   |          | `null`       | Callback when value changes.   |
| **`disabled`**      | `boolean`                    |          | `false`      | Disables all radio buttons.    |
| **`readOnly`**      | `boolean`                    |          | `false`      | Marks the group as read-only.  |
| **`required`**      | `boolean`                    |          | `false`      | Marks the group as required.   |
| **`direction`**     | `'horizontal' \| 'vertical'` |          | `'vertical'` | Layout direction of the group. |

### Radio

| Name           | Type      | Required | Default | Description                |
| -------------- | --------- | -------- | ------- | -------------------------- |
| **`value`**    | `string`  |          | `null`  | Associated value.          |
| **`disabled`** | `boolean` |          | `false` | Disables the radio button. |
| **`readOnly`** | `boolean` |          | `false` | Marks as read-only.        |
| **`required`** | `boolean` |          | `false` | Marks as required.         |

## Examples

### Basic Usage

A simple example of a `RadioGroup` with three options.

<ComponentPreviewUI name="radio-basic" code={BasicDemo} />

### Controlled Radio

A controlled `RadioGroup` where the selected value is managed by state.

<ComponentPreviewUI name="radio-controlled" code={ControlledDemo} />

### Disabled

A `RadioGroup` where all options are disabled.

<ComponentPreviewUI name="radio-disabled" code={DisabledDemo} />

### Horizontal Layout

A `RadioGroup` with a horizontal layout.

<ComponentPreviewUI name="radio-horizontal" code={HorizontalDemo} />

### Custom Labels

A `RadioGroup` with custom labels using the `Typography` component.

<ComponentPreviewUI name="radio-custom-label" code={CustomLabelDemo} />

## CSS

| Class Name               | Description                           |
| ------------------------ | ------------------------------------- |
| `.ApolloRadio-labelText` | Styles applied to the label text.     |
| `.ApolloRadio-root`      | Styles applied to the root container. |
