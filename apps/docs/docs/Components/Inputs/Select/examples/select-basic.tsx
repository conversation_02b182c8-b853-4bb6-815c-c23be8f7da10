import { Select } from "@apollo/ui"

export default function SelectBasic() {
  return (
    <Select placeholder="Select a fruit">
      <Select.Option label="Apple" value="apple" />
      <Select.Option label="Banana" value="banana" />
      <Select.Option label="Cherry" value="cherry" />
      <Select.Option label="Dragon Fruit" value="dragonFruit" />
      <Select.Option label="Elderberry" value="elderberry" />
    </Select>
  )
}
