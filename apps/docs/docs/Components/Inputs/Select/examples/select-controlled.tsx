import { useState } from "react"
import { But<PERSON>, Select } from "@apollo/ui"

export default function SelectControlled() {
  const [selectedCountry, setSelectedCountry] = useState("ca")

  return (
    <div>
      <Select
        label="Countries"
        value={selectedCountry}
        onChange={setSelectedCountry}
        helperText={`Current selection: ${selectedCountry}`}
      >
        <Select.Option label="Australia" value="au" />
        <Select.Option label="Brazil" value="br" />
        <Select.Option label="Canada" value="ca" />
        <Select.Option label="Denmark" value="dk" />
        <Select.Option label="Egypt" value="eg" />
      </Select>

      <div style={{ marginTop: "16px", display: "flex", gap: "8px" }}>
        <Button onClick={() => setSelectedCountry("br")}>Set to Brazil</Button>
        <Button onClick={() => setSelectedCountry("dk")}>Set to Denmark</Button>
      </div>
    </div>
  )
}
