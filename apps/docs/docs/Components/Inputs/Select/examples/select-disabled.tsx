import { Select } from "@apollo/ui"

export default function SelectDisabled() {
  return (
    <Select
      label="Languages"
      helperText="Selection currently disabled"
      disabled
      placeholder="Select a language"
    >
      <Select.Option label="English" value="en" />
      <Select.Option label="Spanish" value="es" />
      <Select.Option label="French" value="fr" />
      <Select.Option label="German" value="de" />
      <Select.Option label="Italian" value="it" />
    </Select>
  )
}
