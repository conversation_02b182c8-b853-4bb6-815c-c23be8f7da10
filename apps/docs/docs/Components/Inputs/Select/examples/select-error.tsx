import { Select } from "@apollo/ui"

export default function SelectError() {
  return (
    <Select
      label="Countries"
      helperText="Please select a valid country"
      error
      placeholder="Select a country"
    >
      <Select.Option label="Australia" value="au" />
      <Select.Option label="Brazil" value="br" />
      <Select.Option label="Canada" value="ca" />
      <Select.Option label="Denmark" value="dk" />
      <Select.Option label="Egypt" value="eg" />
    </Select>
  )
}
