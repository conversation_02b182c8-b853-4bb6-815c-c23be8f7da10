import { Button, Select } from "@apollo/ui"
import { Controller, useForm } from "react-hook-form"

export default function SelectForm() {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      occupation: "",
    },
  })

  const onSubmit = (data) => {
    alert(JSON.stringify(data, null, 2))
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="occupation"
        control={control}
        rules={{ required: "Please select an occupation" }}
        render={({ field }) => (
          <Select
            label="Occupation"
            helperText={
              errors.occupation
                ? errors.occupation.message
                : "Select your occupation"
            }
            error={!!errors.occupation}
            required
            value={field.value}
            onChange={field.onChange}
            placeholder="Choose your occupation"
          >
            <Select.Option label="Engineer" value="engineer" />
            <Select.Option label="Designer" value="designer" />
            <Select.Option label="Product Manager" value="product_manager" />
            <Select.Option label="Developer" value="developer" />
            <Select.Option label="Marketer" value="marketer" />
          </Select>
        )}
      />
      <div style={{ marginTop: "16px" }}>
        <Button type="submit">Submit</Button>
      </div>
    </form>
  )
}
