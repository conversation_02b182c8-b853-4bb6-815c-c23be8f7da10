import { Select } from "@apollo/ui"

export default function SelectFullWidth() {
  return (
    <Select
      label="Vehicle"
      helperText="Select your preferred vehicle"
      fullWidth
      placeholder="Select a vehicle"
    >
      <Select.Option label="Car" value="car" />
      <Select.Option label="Motorcycle" value="motorcycle" />
      <Select.Option label="Bicycle" value="bicycle" />
      <Select.Option label="Bus" value="bus" />
      <Select.Option label="Train" value="train" />
    </Select>
  )
}
