import { Select } from "@apollo/ui"

export default function SelectLabelHelperText() {
  return (
    <Select
      label="Fruits"
      helperText="Select your favorite fruit"
      placeholder="Choose a fruit"
    >
      <Select.Option label="Apple" value="apple" />
      <Select.Option label="Banana" value="banana" />
      <Select.Option label="Cherry" value="cherry" />
      <Select.Option label="Dragon Fruit" value="dragonFruit" />
      <Select.Option label="Elderberry" value="elderberry" />
    </Select>
  )
}
