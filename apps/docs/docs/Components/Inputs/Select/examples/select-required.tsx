import { Select } from "@apollo/ui"

export default function SelectRequired() {
  return (
    <Select
      label="Country"
      helperText="This field is required"
      required
      placeholder="Select your country"
    >
      <Select.Option label="Australia" value="au" />
      <Select.Option label="Brazil" value="br" />
      <Select.Option label="Canada" value="ca" />
      <Select.Option label="Denmark" value="dk" />
      <Select.Option label="Egypt" value="eg" />
    </Select>
  )
}
