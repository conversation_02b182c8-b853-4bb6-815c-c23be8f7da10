import { useState } from "react"
import { Select } from "@apollo/ui"

const options = [
  { label: "Option 1", value: 10 },
  { label: "Option 2", value: 20 },
  { label: "Option 3", value: 30 },
  { label: "Option 4", value: 40 },
  { label: "Option 5", value: 50 },
  { label: "Option 6", value: 60 },
  { label: "Option 7", value: 70 },
  { label: "Option 8", value: 80 },
  { label: "Option 9", value: 90 },
  { label: "Option 10", value: 100 },
  { label: "Option BReak", value: { foo: "bar" } },
]

export default function SelectBasic() {
  const [value, setValue] = useState(20)
  return (
    <Select
      value={value}
      onChange={(value: any) => {
        console.log("Selected value:", value)
        setValue(value)
      }}
    >
      {options.map((option, index) => (
        <Select.Option key={index} value={option.value} label={option.label} />
      ))}
    </Select>
  )
}
