import { Switch } from "@apollo/ui"

export default function SwitchBasicDemo() {
  return (
    <div style={styles}>
      <Switch
        defaultChecked
        label="Default Checked"
        actionText="On/Off"
        onCheckedChange={(checked) => console.log("Checked:", checked)}
      />
      <Switch
        label="Unchecked"
        actionText="On/Off"
        onCheckedChange={(checked) => console.log("Unchecked:", checked)}
      />
      <Switch
        defaultChecked
        onCheckedChange={(checked) =>
          console.log("No Label or ActionText:", checked)
        }
      />
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
} as const
