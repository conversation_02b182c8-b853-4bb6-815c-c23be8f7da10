import { useState } from "react"
import { Switch } from "@apollo/ui"

export default function SwitchControlledDemo() {
  const [isChecked, setIsChecked] = useState(false)

  return (
    <div style={styles}>
      <Switch
        checked={isChecked}
        label="Controlled Switch"
        actionText="On/Off"
        onCheckedChange={(checked) => {
          setIsChecked(checked)
          console.log("Controlled Switch Checked:", checked)
        }}
      />
      <Switch
        checked={isChecked}
        onCheckedChange={(checked) => {
          setIsChecked(checked)
          console.log("Controlled Switch Without Label or ActionText:", checked)
        }}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
  gap: "16px",
} as const
