import { useState } from "react"
import { But<PERSON>, Switch } from "@apollo/ui"
import { Controller, useForm } from "react-hook-form"

export default function SwitchFormDemo() {
  const [latestSubmit, setLatestSubmit] = useState<any>({ switchField: false })
  const { control, handleSubmit } = useForm({
    defaultValues: {
      switchField: false,
    },
  })

  const onSubmit = (data: any) => {
    console.log("Form Data:", data)
    setLatestSubmit(data)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} style={styles}>
      <Controller
        name="switchField"
        control={control}
        render={({ field: { value, ...field } }) => (
          <Switch
            {...field}
            checked={value}
            label="Form Controlled Switch"
            actionText="On/Off"
            onCheckedChange={(checked) => {
              field.onChange(checked)
              console.log("React Hook Form Switch Checked:", checked)
            }}
          />
        )}
      />
      <Controller
        name="switchField"
        control={control}
        render={({ field: { value, ...field } }) => (
          <Switch
            {...field}
            checked={value}
            onCheckedChange={(checked) => {
              field.onChange(checked)
              console.log(
                "React Hook Form Switch Without Label or ActionText:",
                checked
              )
            }}
          />
        )}
      />
      {JSON.stringify(latestSubmit, null, 2)}
      <Button size="small" type="submit">
        Submit
      </Button>
    </form>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  height: "100vh",
} as const
