---
title: Switch
id: component:switch
slug: /components/switch
---

import BasicDemo from "!!raw-loader!./examples/switch-basic"
import ControlledDemo from "!!raw-loader!./examples/switch-controlled"
import DisabledDemo from "!!raw-loader!./examples/switch-disabled"
import FormDemo from "!!raw-loader!./examples/switch-form"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Switch component is a toggle control that allows users to switch between two states, such as on and off. It supports controlled and uncontrolled modes, labels, and can be integrated with forms.

<ComponentPreviewUI name="switch-preview" code={BasicDemo} />

## Props

| Name                  | Type                         | Required | Default | Description                                                               |
| --------------------- | ---------------------------- | -------- | ------- | ------------------------------------------------------------------------- |
| **`checked`**         | `boolean`                    |          | -       | If `true`, the switch is checked (controlled).                            |
| **`defaultChecked`**  | `boolean`                    |          | `false` | The default checked state (uncontrolled).                                 |
| **`disabled`**        | `boolean`                    |          | `false` | If `true`, the switch is disabled.                                        |
| **`label`**           | `ReactNode`                  |          | -       | The label content for the switch.                                         |
| **`labelDecorator`**  | `ReactNode`                  |          | -       | Additional content to display after the label.                            |
| **`required`**        | `boolean`                    |          | `false` | Whether the input is required                                             |
| **`actionText`**      | `string`                     |          | -       | Text displayed next to the switch to indicate its state (e.g., "On/Off"). |
| **`onCheckedChange`** | `(checked: boolean) => void` |          | -       | Callback fired when the checked state changes.                            |

## Examples

### Basic Usage

Basic examples of switches in their default and unchecked states.

<ComponentPreviewUI name="switch-basic" code={BasicDemo} />

### Controlled Component

Example of a controlled switch where the state is managed externally.

<ComponentPreviewUI name="switch-controlled" code={ControlledDemo} />

### Disabled State

Examples of switches in disabled states.

<ComponentPreviewUI name="switch-disabled" code={DisabledDemo} />

### Integration with Forms

Example of integrating the switch with a form using `react-hook-form`.

<ComponentPreviewUI name="switch-form" code={FormDemo} />

## CSS

| Class Name                  | Description                                                                |
| --------------------------- | -------------------------------------------------------------------------- |
| `.ApolloSwitch-fieldRoot`   | Styles applied to the root container of the field wrapping the switch.     |
| `.ApolloSwitch-wrapperRoot` | Styles applied to the wrapper element containing the switch and its label. |
| `.ApolloSwitch-switchRoot`  | Styles applied to the root element of the switch component.                |
| `.ApolloSwitch-thumbRoot`   | Styles applied to the thumb element of the switch.                         |
| `.ApolloSwitch-actionText`  | Styles applied to the action text displayed next to the switch.            |
