---
title: Textarea
id: component:textarea
slug: /components/textarea
---

import PreviewCode from "!!raw-loader!./examples/textarea-demo"
import DisabledDemo from "!!raw-loader!./examples/textarea-disabled"
import ErrorDemo from "!!raw-loader!./examples/textarea-error"
import FullWidthDemo from "!!raw-loader!./examples/textarea-fullwidth"
import LabelHelperTextDemo from "!!raw-loader!./examples/textarea-label-helpertext"
import RequiredDemo from "!!raw-loader!./examples/textarea-required"
import SizeDemo from "!!raw-loader!./examples/textarea-size"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Textarea component is a flexible and customizable UI element for user input. It supports various styles, sizes, and can display helper text and error messages.

<ComponentPreviewUI name="textarea-preview" code={PreviewCode} />

## Props

> **Note**: The props also extend the [`TextareaAutosizeProps`](https://github.com/mui/material-ui/blob/master/packages/mui-base/src/TextareaAutosize/TextareaAutosize.types.ts) from `@mui/base` of the `textarea` element.

| Name                      | Type                             | Required | Default  | Description                                                           |
| ------------------------- | -------------------------------- | -------- | -------- | --------------------------------------------------------------------- |
| **`fullWidth`**           | `bool`                           |          | `false`  | If `true`, the textarea will take up the full width of its container. |
| **`size`**                | `'medium' \| 'small'`            |          | `medium` | The size of the component.                                            |
| **`error`**               | `bool`                           |          | `false`  | If `true`, the textarea will display an error state.                  |
| **`label`**               | `ReactNode`                      |          | -        | The label for the textarea.                                           |
| **`labelDecorator`**      | `ReactNode`                      |          | -        | Additional content to display after the label.                        |
| **`helperText`**          | `ReactNode`                      |          | -        | The helper text for the textarea.                                     |
| **`helperTextDecorator`** | `ReactNode`                      |          | -        | Additional content to display after the helper text.                  |
| **`required`**            | `bool`                           |          | `false`  | If `true`, the textarea will display a required indicator.            |
| **`rootRef`**             | `Ref<HTMLDivElement>`            |          | -        | Ref for the root element.                                             |
| **`rootProps`**           | `HTMLAttributes<HTMLDivElement>` |          | -        | Props for the root element.                                           |
| **`fieldProps`**          | `FieldProps`                     |          | -        | Props for the field element.                                          |

## Examples

### Sizes

Different sizes of textareas including medium and small.

<ComponentPreviewUI name="textarea-size" code={SizeDemo} />

### Disabled

Textareas that are disabled and not interactive.

<ComponentPreviewUI name="textarea-disabled" code={DisabledDemo} />

### Error State

Textareas that display an error state.

<ComponentPreviewUI name="textarea-error" code={ErrorDemo} />

### Full Width

Textareas that take up the full width of their container.

<ComponentPreviewUI name="textarea-fullwidth" code={FullWidthDemo} />

### Label and Helper Text

Textareas with a label and helper text displayed.

<ComponentPreviewUI
  name="textarea-label-helpertext"
  code={LabelHelperTextDemo}
/>

### Required

Textareas that display a required indicator.

<ComponentPreviewUI name="textarea-required" code={RequiredDemo} />

## CSS

| Class Name                    | Description                        |
| ----------------------------- | ---------------------------------- |
| `.ApolloTextarea-fieldRoot`   | Styles applied to the field root   |
| `.ApolloTextarea-controlRoot` | Styles applied to the control root |
| `.ApolloTextarea-textarea`    | Styles applied to the textarea     |
