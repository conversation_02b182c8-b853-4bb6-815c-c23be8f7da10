import { useState } from "react"
import { UploadBox } from "@apollo/ui"

export default function UploadBoxErrorState() {
  const [file, setFile] = useState<File | null>(null)
  const [fileState, setFileState] = useState(null)
  const [errorMessage, setErrorMessage] = useState<string | undefined>("There was an error uploading your file. Please try again.")

  const handleDeleteFile = () => {
    setFile(null)
    setFileState(null)
  }

  const handleUpload = (uploadedFile: File) => {
    setFile(uploadedFile)
    setFileState({ key: uploadedFile.name, errorMessage: "Cloud Error" })
  }

  return (
    <UploadBox
      label="Upload Document"
      helperText="Upload a document file"
      value={file}
      fileState={fileState}
      onDelete={handleDeleteFile}
      onUpload={handleUpload}
      errorMessage={errorMessage}
      errorOnClose={() => {
        setErrorMessage("")
      }}
    />
  )
}
