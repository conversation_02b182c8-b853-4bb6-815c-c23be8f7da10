import { useState } from "react"
import { UploadBox } from "@apollo/ui"

export default function UploadBoxFileTypeSizeLimit() {
  const [file, setFile] = useState<File | null>(null)

  const handleDeleteFile = () => {
    setFile(null)
  }

  return (
    <UploadBox
      label="Upload Image"
      helperText="Upload a JPG or PNG image (max 2MB)"
      allowedFilesExtension={["jpg", "png"]}
      maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
      value={file}
      onDelete={handleDeleteFile}
      onUpload={setFile}
    />
  )
}
