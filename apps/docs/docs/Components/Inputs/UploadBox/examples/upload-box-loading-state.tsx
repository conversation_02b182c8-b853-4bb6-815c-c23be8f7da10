import { useState } from "react"
import { UploadBox } from "@apollo/ui"

export default function UploadBoxLoadingState() {
  const [file, setFile] = useState<File | null>(null)
  const [fileState, setFileState] = useState(null)

  const handleUpload = (uploadedFile: File) => {
    setFile(uploadedFile)
    setFileState({ key: uploadedFile.name, uploading: true })

    // Simulate upload process
    setTimeout(() => {
      setFileState({ uploading: false })
    }, 3000)
  }

  const handleDeleteFile = () => {
    setFile(null)
  }

  const handleCancelUpload = () => {
    setFileState({ uploading: false })
  }

  return (
    <UploadBox
      label="Upload Document"
      helperText="Upload a document and see the loading state"
      value={file}
      fileState={fileState}
      onDelete={handleDeleteFile}
      onUpload={handleUpload}
      onCancelUpload={handleCancelUpload}
    />
  )
}
