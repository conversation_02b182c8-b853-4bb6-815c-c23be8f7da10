import { useState } from "react"
import { UploadBox } from "@apollo/ui"

export default function UploadBoxMultiple() {
  const [files, setFiles] = useState<File[] | null>(null)

  const handleDeleteFile = (deleteIndex: number) => {
    setFiles((prevFiles) =>
      prevFiles ? prevFiles.filter((_, index) => index !== deleteIndex) : null
    )
  }

  return (
    <UploadBox
      label="Upload Documents"
      helperText="Upload multiple document files"
      multiple
      value={files}
      onDelete={handleDeleteFile}
      onUpload={setFiles}
    />
  )
}
