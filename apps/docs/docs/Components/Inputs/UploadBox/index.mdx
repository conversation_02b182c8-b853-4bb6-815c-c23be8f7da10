---
title: UploadBox
id: component:uploadbox
slug: /components/uploadbox
---

import UploadBoxDemo from "!!raw-loader!./examples/upload-box-demo"
import UploadBoxDisabled from "!!raw-loader!./examples/upload-box-disabled"
import UploadBoxErrorState from "!!raw-loader!./examples/upload-box-error-state"
import UploadBoxFileLimitations from "!!raw-loader!./examples/upload-box-file-limitations"
import UploadBoxLoadingState from "!!raw-loader!./examples/upload-box-loading-state"
import UploadBoxMultiple from "!!raw-loader!./examples/upload-box-multiple"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

# UploadBox

UploadBox is a component for handling file uploads with built-in validation for file types and sizes. It provides a convenient way for users to upload single or multiple files with progress indicators and error handling.

<ComponentPreviewUI name="upload-box-preview" code={UploadBoxDemo} />

## Features

- Support for single or multiple file uploads
- File type validation with configurable allowed extensions
- File size limits
- Progress indicators for ongoing uploads
- Error state handling
- Customizable upload button text
- Responsive design with optional full-width mode

## Props

> **Note**: The props extend standard React props for div elements.

| Name                        | Type                                                                | Required | Default           | Description                                                                                                  |
| --------------------------- | ------------------------------------------------------------------- | -------- | ----------------- | ------------------------------------------------------------------------------------------------------------ |
| **`value`**                 | `UploadBoxFile<Multiple, FileType>`                                 |          | `undefined`       | The uploaded file(s). For single mode, it's a file object, for multiple mode, it's an array of file objects. |
| **`onUpload`**              | `(files: Multiple extends true ? File[] : File) => void`            |          | -                 | Callback fired when files are selected for upload.                                                           |
| **`onDelete`**              | `Multiple extends true ? (index: number) => void : () => void`      |          | -                 | Callback fired when a file is deleted. For multiple mode, it receives the file index.                        |
| **`onCancelUpload`**        | `(file: UploadBoxFile<Multiple, FileType>, index: number) => void`  |          | -                 | Callback fired when file upload is canceled.                                                                 |
| **`fileState`**             | `Multiple extends true ? UploadBoxFileState[] : UploadBoxFileState` |          | -                 | The state of the uploaded file(s), including upload status and error messages.                               |
| **`allowedFilesExtension`** | `string[]`                                                          |          | `[]`              | Array of allowed file extensions (without the dot).                                                          |
| **`maxFileSizeInBytes`**    | `number`                                                            |          | `52428800` (50MB) | Maximum file size in bytes.                                                                                  |
| **`multiple`**              | `boolean`                                                           |          | `false`           | If `true`, allows multiple file uploads.                                                                     |
| **`fileLimit`**             | `number`                                                            |          | `0` (no limit)    | Maximum number of files that can be uploaded in multiple mode.                                               |
| **`disabled`**              | `boolean`                                                           |          | `false`           | If `true`, the component is disabled and files cannot be uploaded.                                           |
| **`label`**                 | `ReactNode`                                                         |          | -                 | The label for the upload box.                                                                                |
| **`helperText`**            | `ReactNode`                                                         |          | -                 | Helper text shown below the label.                                                                           |
| **`errorMessage`**          | `ReactNode`                                                         |          | -                 | Error message to display when there's an error.                                                              |
| **`errorOnClose`**          | `ReactEventHandler`                                                 |          | -                 | Callback fired when the error message is closed.                                                             |
| **`required`**              | `boolean`                                                           |          | `false`           | If `true`, indicates this field is required.                                                                 |
| **`fullWidth`**             | `boolean`                                                           |          | `false`           | If `true`, the component takes up the full width of its container.                                           |
| **`uploadButtonText`**      | `string`                                                            |          | `"Upload File"`   | Text for the upload button.                                                                                  |

## Types

| Type                        | Definition                                                                                    | Description                                                                                                                                                |
| --------------------------- | --------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **`UploadBoxFileState`**    | `{ key: string; uploading?: boolean; errorMessage?: string }`                                 | Represents the state of an individual uploaded file, including a unique key, upload status, and error message if applicable.                               |
| **`UploadBoxFileType`**     | `Multiple extends boolean = false, FileType = any`                                            | Determines if the type should be an array of files (for `multiple` mode) or a single file.                                                                 |
| **`UploadBoxBaseFileType`** | `{ name: string }`                                                                            | The base file type, containing only a `name` property.                                                                                                     |
| **`UploadBoxFile`**         | `Multiple extends boolean = false, FileType extends { name: string } = UploadBoxBaseFileType` | Defines the file type, which can either be a single file or an array of files depending on `multiple`. Includes the `name` property of the base file type. |

## Examples

### Default

Basic example of using the `UploadBox` component to upload files.

<ComponentPreviewUI name="upload-box-demo" code={UploadBoxDemo} />

### Multiple Mode

Example of using `UploadBox` with the `multiple` prop enabled to upload multiple files.

<ComponentPreviewUI name="upload-box-multiple" code={UploadBoxMultiple} />

### File Type and Size Limitations

Example demonstrating how to restrict file types and file size in the `UploadBox` component.

<ComponentPreviewUI
  name="upload-box-file-limitations"
  code={UploadBoxFileLimitations}
/>

### Loading State

Example showing how to display loading state during file upload.

<ComponentPreviewUI
  name="upload-box-loading-state"
  code={UploadBoxLoadingState}
/>

### Error State

Example showing how the `UploadBox` displays error messages when there's an issue with file upload.

<ComponentPreviewUI name="upload-box-error-state" code={UploadBoxErrorState} />

### Disabled State

Example of a disabled `UploadBox` that doesn't allow file uploads.

<ComponentPreviewUI name="upload-box-disabled" code={UploadBoxDisabled} />

## CSS

| Class Name                             | Description                                            |
| -------------------------------------- | ------------------------------------------------------ |
| `.uploadBox-formControl`               | Styles applied to the form control root element        |
| `.uploadBox-uploadSection`             | Styles applied to the upload section container         |
| `.uploadBox-fullWidth`                 | Styles applied when the full width mode is enabled     |
| `.uploadBox-fileConditionContainer`    | Styles applied to the file conditions container        |
| `.uploadBox-fileConditionList`         | Styles applied to the list of file conditions          |
| `.uploadBox-uploadButton`              | Styles applied to the upload button                    |
| `.uploadBox-uploadedFileItem`          | Styles applied to each uploaded file item              |
| `.uploadBox-uploadedFileItemContent`   | Styles applied to the content of an uploaded file item |
| `.uploadBox-uploadedFileItemInfo`      | Styles applied to the file information section         |
| `.uploadBox-uploadedFileItemIcon`      | Styles applied to the file icon                        |
| `.uploadBox-deleteIcon`                | Styles applied to the delete button icon               |
| `.uploadBox-uploadingText`             | Styles applied to the uploading status text            |
| `.uploadBox-uploadedFileList`          | Styles applied to the list of uploaded files           |
| `.uploadBox-loadingIndicatorContainer` | Styles applied to the loading indicator container      |
| `.uploadBox-loadingIndicator`          | Styles applied to the loading indicator element        |
