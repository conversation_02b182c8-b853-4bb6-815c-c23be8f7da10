---
title: Badge
id: component:badge
slug: /components/badge
---

import BasicDemo from "!!raw-loader!./examples/badge-basic"
import ColorDemo from "!!raw-loader!./examples/badge-color"
import CustomDemo from "!!raw-loader!./examples/badge-custom"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Badge component is used to display a small piece of information or a status indicator. It can be used to display a count, a status, or a label.

<ComponentPreviewUI name="badge-basic" code={BasicDemo} />

## Props

> **Note**: The props extend the `HTMLAttributes` of the `span` element.

| Name             | Type                                                          | Required | Default   | Description                                               |
| ---------------- | ------------------------------------------------------------- | -------- | --------- | --------------------------------------------------------- |
| **`label`**      | `string`                                                      | ✓        | -         | The content or status of the badge                        |
| **`color`**      | `'default' \| 'process'` \| 'success' \| 'warning' \| 'error' |          | `default` | The color of the badge                                    |
| **`icon`**       | `ReactNode`                                                   |          | -         | The icon to display in the badge                          |
| **`ref`**        | `React.Ref<HTMLSpanElement>`                                  |          | -         | The ref to the root element of the badge                  |
| **`wrapperRef`** | `React.Ref<HTMLSpanElement>`                                  |          | -         | The ref to the wrapper element when children are provided |

## Examples

### Basic Badges

Basic badges without children as standalone, and with children.

<ComponentPreviewUI name="badge-basic" code={BasicDemo} />

### Color Badges

badge have different colors to indicate different statuses.

<ComponentPreviewUI name="badge-color" code={ColorDemo} />

### Custom Badges

badge can be customized with different colors and icons.

<ComponentPreviewUI name="badge-custom" code={CustomDemo} />

## CSS

| Class Name             | Description                                                      |
| ---------------------- | ---------------------------------------------------------------- |
| `.ApolloBadge-wrapper` | Styles applied to the wrapper element when children are provided |
| `.ApolloBadge-root`    | Styles applied to the root element                               |
| `.ApolloBadge-icon`    | Styles applied to the icon element                               |
| `.ApolloBadge-label`   | Styles applied to the label text                                 |
