---
title: Chip
id: component:chip
slug: /components/chip
---

import BasicDemo from "!!raw-loader!./examples/chip-basic"
import SizesDemo from "!!raw-loader!./examples/chip-sizes"
import ColorDemo from "!!raw-loader!./examples/chip-color"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Chip component is a compact element that represents an input, attribute, or action. It can be used to display information or actions in a confined space.

<ComponentPreviewUI name="chip-basic" code={BasicDemo} />

## Props

> **Note**: The props extend the `HTMLAttributes` of the `div` element.

| Name           | Type                             | Required | Default   | Description                                    |
| -------------- | -------------------------------- | -------- | --------- | ---------------------------------------------- |
| **`label`**    | `string`                         | ✓        | -         | The content of the chip                        |
| **`variant`**  | `'filled' \| 'outline'`          |          | `filled`  | The variant of the chip                        |
| **`size`**     | `'large' \| 'medium' \| 'small'` |          | `medium`  | The size of the chip                           |
| **`color`**    | `'primary' \| 'danger'`          |          | `primary` | The color of the chip                          |
| **`disabled`** | `boolean`                        |          | `false`   | If `true`, the chip will be disabled           |
| **`onClose`**  | `() => void`                     |          | -         | Callback fired when the delete icon is clicked |
| **`onCheck`**  | `() => void`                     |          | -         | Callback fired when the check icon is clicked  |
| **`onClick`**  | `() => void`                     |          | -         | Callback fired when the chip is clicked        |

## Examples

### Basic Chips

Basic chips with different states including closeable and disabled variants.

<ComponentPreviewUI name="chip-basic" code={BasicDemo} />

### Sizes

Chips are available in two sizes: medium and small.

<ComponentPreviewUI name="chip-sizes" code={SizesDemo} />

### Color

Chip with different color options like primary and danger.

<ComponentPreviewUI name="chip-color" code={ColorDemo} />

## CSS

| Class Name              | Description                             |
| ----------------------- | --------------------------------------- |
| `.ApolloChip-root`      | Styles applied to the root element      |
| `.ApolloChip-label`     | Styles applied to the label text        |
| `.ApolloChip-closeIcon` | Styles applied to the close button icon |
| `.ApolloChip-checkIcon` | Styles applied to the check button icon |
