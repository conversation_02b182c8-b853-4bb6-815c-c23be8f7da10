import React from "react"
import { ProductCard, Typography } from "@apollo/ui"
import { Heart } from "@design-systems/apollo-icons"

export default function ProductCardExtra() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=4"
        extra={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Typography
              level="body2"
              style={{
                backgroundColor: "#3b82f6",
                color: "white",
                padding: "4px 8px",
                borderRadius: "4px",
              }}
            >
              New
            </Typography>
            <Heart size={20} />
          </div>
        }
        body={
          <Typography level="body2">
            Extra content can be used for tags, ratings, or other information.
          </Typography>
        }
      />
    </div>
  )
}
