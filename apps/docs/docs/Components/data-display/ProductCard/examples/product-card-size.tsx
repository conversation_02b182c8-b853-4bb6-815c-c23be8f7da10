import React from "react"
import { ProductCard, Typography } from "@apollo/ui"

export default function ProductCardSize() {
  return (
    <div
      style={{
        padding: "16px",
        gap: "16px",
        width: "100%",
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Typography level="body2">Default (160px)</Typography>
        <ProductCard
          title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
          imageSrc="https://picsum.photos/200/300?random=1"
        />
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Typography level="body2">Custom size (200px)</Typography>
        <ProductCard
          title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
          size="200px"
          imageSrc="https://picsum.photos/200/300?random=2"
        />
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          width: "300px",
        }}
      >
        <Typography level="body2">Fill container</Typography>
        <ProductCard
          title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
          size="fill"
          imageSrc="https://picsum.photos/200/300?random=3"
          body={
            <Typography level="body2">
              This card fills its container width
            </Typography>
          }
        />
      </div>
    </div>
  )
}
