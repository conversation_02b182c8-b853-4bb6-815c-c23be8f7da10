import React, { ComponentType } from "react"
import { ProductCard } from "@apollo/ui"

export default function ProductCardWithCustomImageComponent() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        ImageComponent={CustomImageComponent}
        imageProps={{ layout: "fill", objectFit: "contain" }}
        imageSrc="https://picsum.photos/200/300?random=8"
      />
    </div>
  )
}

const CustomImageComponent = (props: {
  layout: string
  objectFit: string
  src: string
  alt: string
}) => {
  return (
    <img
      src={props.src}
      alt={props.alt}
      id={props.layout}
      aria-name={props.objectFit}
      style={{
        width: "100%",
        height: "100%",
        objectFit: "cover",
      }}
    />
  )
}
