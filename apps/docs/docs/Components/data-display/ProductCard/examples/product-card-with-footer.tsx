import React from "react"
import { Button, ProductCard } from "@apollo/ui"

export default function ProductCardWithFooter() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=10"
        footer={
          <Button fullWidth variant="filled" color="primary">
            เก็บคูปอง
          </Button>
        }
      />
    </div>
  )
}
