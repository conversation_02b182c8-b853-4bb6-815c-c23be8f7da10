import React from "react"
import { ProductCard, Typography } from "@apollo/ui"

export default function ProductCardWithImageOverlay() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=6"
        imageOverlay="SALE"
      />

      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=7"
        imageOverlay={
          <div
            style={{
              position: "absolute",
              inset: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography level="h4" style={{ color: "white" }}>
              SOLD OUT
            </Typography>
          </div>
        }
      />
    </div>
  )
}
