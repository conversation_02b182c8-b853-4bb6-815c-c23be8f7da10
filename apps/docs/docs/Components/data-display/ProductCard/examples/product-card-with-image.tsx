import React from "react"
import { ProductCard, Typography } from "@apollo/ui"

export default function ProductCardWithImage() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=5"
      />

      <ProductCard title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น" />

      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        noImage={
          <div
            style={{
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "#ef4444",
              color: "white",
            }}
          >
            <Typography level="body1">CUSTOM NO IMAGE</Typography>
          </div>
        }
      />
    </div>
  )
}
