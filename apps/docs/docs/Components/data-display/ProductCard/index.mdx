---
title: ProductCard
id: component:product-card
slug: /components/product-card
---

import ProductCardDefaultDemo from "!!raw-loader!./examples/product-card-demo"
import ProductCardExtraDemo from "!!raw-loader!./examples/product-card-extra"
import ProductCardSizeDemo from "!!raw-loader!./examples/product-card-size"
import ProductCardWithBodyDemo from "!!raw-loader!./examples/product-card-with-body"
import ProductCardWithCustomImageComponentDemo from "!!raw-loader!./examples/product-card-with-custom-image-component"
import ProductCardWithFooterDemo from "!!raw-loader!./examples/product-card-with-footer"
import ProductCardWithImageDemo from "!!raw-loader!./examples/product-card-with-image"
import ProductCardWithImageOverlayDemo from "!!raw-loader!./examples/product-card-with-image-overlay"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The ProductCard component is a flexible, styled card designed to display product information in a consistent layout. It supports various features including images, titles, descriptions, and custom content areas.

<ComponentPreviewUI name="product-card-preview" code={ProductCardDefaultDemo} />

## Props

| Name                 | Type                                           | Required | Default   | Description                                                          |
| -------------------- | ---------------------------------------------- | -------- | --------- | -------------------------------------------------------------------- |
| **`title`**          | `ReactNode`                                    | ✓        | -         | Title of the product card                                            |
| **`size`**           | `string \| "fill"`                             |          | `"160px"` | Size of the card. When set to "fill", card fills its container width |
| **`extra`**          | `ReactNode`                                    |          | -         | Extra content to render above the title                              |
| **`body`**           | `ReactNode`                                    |          | -         | Main content body of the product card                                |
| **`footer`**         | `ReactNode`                                    |          | -         | Footer content of the product card                                   |
| **`imageSrc`**       | `string`                                       |          | -         | Source URL for the product image                                     |
| **`imageProps`**     | `Partial<ImgHTMLAttributes<HTMLImageElement>>` |          | -         | Additional props for the image component                             |
| **`noImage`**        | `ReactNode`                                    |          | -         | Content to display when there is no image                            |
| **`ImageComponent`** | `ComponentType<ImageProps> \| "img"`           |          | `"img"`   | Component to use for rendering images                                |
| **`imageOverlay`**   | `ReactNode`                                    |          | -         | Overlay content to display on top of the image                       |

> **Note**: The component also accepts all standard HTML div attributes as it extends `HTMLAttributes<HTMLDivElement>`.

## Examples

### Default

By default, the product card will display with its default properties and will function correctly with just a title.

<ComponentPreviewUI name="product-card-demo" code={ProductCardDefaultDemo} />

### Size

A product card can adjust its size based on the `size` prop. The card can either fill the available space (`"fill"`) or take up a specified size.

<ComponentPreviewUI name="product-card-size" code={ProductCardSizeDemo} />

### Extra

The extra prop allows for additional content to be displayed above the title, which can be useful for badges, ratings, or other supplementary information.

<ComponentPreviewUI name="product-card-extra" code={ProductCardExtraDemo} />

### With Image

Product cards can display images using the `imageSrc` prop. Without an image, the card will display a default placeholder, which can be customized using the `noImage` prop.

<ComponentPreviewUI
  name="product-card-with-image"
  code={ProductCardWithImageDemo}
/>

### With Image Overlay

The `imageOverlay` prop allows for content to be displayed over the image, which can be used for labels, badges, or status indicators.

<ComponentPreviewUI
  name="product-card-with-image-overlay"
  code={ProductCardWithImageOverlayDemo}
/>

### With Custom Image Component

The `ImageComponent` prop allows for custom image components to be used, which is useful when using image libraries like Next.js Image component.

<ComponentPreviewUI
  name="product-card-with-custom-image-component"
  code={ProductCardWithCustomImageComponentDemo}
/>

### With Body

The `body` prop provides space for detailed product information.

<ComponentPreviewUI
  name="product-card-with-body"
  code={ProductCardWithBodyDemo}
/>

### With Footer

The `footer` prop allows for additional content at the bottom of the card, typically used for actions like buttons or links.

<ComponentPreviewUI
  name="product-card-with-footer"
  code={ProductCardWithFooterDemo}
/>

## CSS

| Class Name                            | Description                                                |
| ------------------------------------- | ---------------------------------------------------------- |
| `.ApolloProductCard-root`             | Styles applied to the root container of the product card   |
| `.ApolloProductCard-mediaContainer`   | Styles applied to the media container within the card      |
| `.ApolloProductCard-imageContainer`   | Styles applied to the image container within the card      |
| `.ApolloProductCard-image`            | Styles applied to the image element within the card        |
| `.ApolloProductCard-noImageBox`       | Styles applied to the no image box element within the card |
| `.ApolloProductCard-imageOverlay`     | Styles applied to the overlay on top of the image          |
| `.ApolloProductCard-imageOverlayText` | Styles applied to the overlay text on the image            |
| `.ApolloProductCard-titleContainer`   | Styles applied to the container holding the title          |
| `.ApolloProductCard-extraContainer`   | Styles applied to the container holding the extra content  |
| `.ApolloProductCard-bodyContainer`    | Styles applied to the container holding the body content   |
| `.ApolloProductCard-footerContainer`  | Styles applied to the container holding the footer         |
