import { SortingIcon } from "@apollo/ui"

export default function SortingIconBasic() {
  return (
    <div style={styles}>
      <div style={columnStyle}>
        <span>Default</span>
        <SortingIcon status="default" />
      </div>
      <div style={columnStyle}>
        <span>Ascending</span>
        <SortingIcon status="asc" />
      </div>
      <div style={columnStyle}>
        <span>Descending</span>
        <SortingIcon status="desc" />
      </div>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "32px",
  height: "100dvh",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
} as const

const columnStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: "8px",
} as const
