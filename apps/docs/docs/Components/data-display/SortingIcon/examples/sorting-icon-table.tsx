import { useState } from "react"
import { SortingIcon } from "@apollo/ui"

export default function SortingIconTable() {
  const [sortStatus, setSortStatus] = useState<"default" | "asc" | "desc">(
    "default"
  )

  const handleSort = () => {
    if (sortStatus === "default") setSortStatus("asc")
    else if (sortStatus === "asc") setSortStatus("desc")
    else setSortStatus("default")
  }

  return (
    <div style={styles}>
      <table style={tableStyles}>
        <thead>
          <tr>
            <th style={headerStyles} onClick={handleSort}>
              <div style={headerCellStyles}>
                Product Name
                <SortingIcon status={sortStatus} />
              </div>
            </th>
            <th style={headerStyles}>Price</th>
            <th style={headerStyles}>Stock</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product A</td>
            <td>$10.00</td>
            <td>100</td>
          </tr>
          <tr>
            <td>Product B</td>
            <td>$15.00</td>
            <td>50</td>
          </tr>
          <tr>
            <td>Product C</td>
            <td>$20.00</td>
            <td>75</td>
          </tr>
        </tbody>
      </table>
      <div style={instructionStyles}>
        Click on "Product Name" header to change sort status
      </div>
    </div>
  )
}

const styles = {
  display: "flex",
  height: "100dvh",
  justifyContent: "center",
  flexDirection: "column" as const,
  alignItems: "center",
  gap: "16px",
}

const tableStyles = {
  width: "100%",
  borderCollapse: "collapse" as const,
  textAlign: "left" as const,
}

const headerStyles = {
  borderBottom: "1px solid #e0e0e0",
  padding: "12px 8px",
}

const headerCellStyles = {
  display: "flex",
  alignItems: "center",
  gap: "8px",
  cursor: "pointer",
}

const instructionStyles = {
  fontSize: "14px",
  color: "#666",
  fontStyle: "italic" as const,
}
