---
title: SortingIcon
id: component:sortingicon
slug: /components/sorting-icon
---

import BasicDemo from "!!raw-loader!./examples/sorting-icon-basic"
import TableDemo from "!!raw-loader!./examples/sorting-icon-table"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The SortingIcon component indicates the sorting status of a table column, providing users with a visual representation of the sorting state.

<ComponentPreviewUI name="sorting-icon-basic" code={BasicDemo} />

## Props

> **Note**: The props extend the `HTMLAttributes` of the `span` element.

| Name         | Type                             | Required | Default     | Description                            |
| ------------ | -------------------------------- | -------- | ----------- | -------------------------------------- |
| **`status`** | `'default' \| 'asc' \| 'desc'`   |          | `'default'` | The sorting status of the table column |
| **`ref`**    | `React.Ref<HTMLSpanElement>`     |          | -           | Ref for the root element               |

## Examples

### Basic Sorting Icons

The SortingIcon component has three states: default, ascending (asc), and descending (desc).

<ComponentPreviewUI name="sorting-icon-basic" code={BasicDemo} />

### Usage in Table Context

SortingIcon is commonly used in table headers to indicate the sorting status of a column.

<ComponentPreviewUI name="sorting-icon-table" code={TableDemo} />

## CSS

| Class Name                    | Description                                 |
| ----------------------------- | ------------------------------------------- |
| `.ApolloSortingIcon-root`     | Styles applied to the root container        |
| `.ApolloSortingIcon-iconASC`  | Styles applied to the ascending arrow icon  |
| `.ApolloSortingIcon-iconDESC` | Styles applied to the descending arrow icon |
