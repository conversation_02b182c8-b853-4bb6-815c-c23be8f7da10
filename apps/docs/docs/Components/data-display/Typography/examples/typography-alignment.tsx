import { Typography } from "@apollo/ui"

export default function TypographyAlignmentDemo() {
  return (
    <div style={styles}>
      <Typography style={{ width: "100%" }} align="left">
        Left Aligned
      </Typography>
      <Typography style={{ width: "100%" }} align="center">
        Center Aligned
      </Typography>
      <Typography style={{ width: "100%" }} align="right">
        Right Aligned
      </Typography>
      <Typography style={{ width: "100%" }} align="justify">
        Justify Aligned
      </Typography>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "flex-start",
  flexWrap: "wrap",
  width: "100%",
  height: "100%",
  padding: "16px",
} as any
