import { Typography } from "@apollo/ui"

export default function TypographyColorDemo() {
  return (
    <div style={styles}>
      <Typography color="primary">Primary Color</Typography>
      <Typography color="danger">Danger Color</Typography>
      <Typography color="warning">Warning Color</Typography>
      <Typography color="success">Success Color</Typography>
      <Typography color="process">Process Color</Typography>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "flex-start",
  flexWrap: "wrap",
  width: "100%",
  height: "100%",
  padding: "16px",
} as any
