import { Typography } from "@apollo/ui"

export default function TypographyDemo() {
  return (
    <div style={styles}>
      <Typography level="display1">Display 1</Typography>
      <Typography level="h1">Heading 1</Typography>
      <Typography level="body1">Body 1</Typography>
      <Typography level="body2">Body 2</Typography>
      <Typography level="caption">Caption</Typography>
      <Typography level="textlink" href="https://example.com">
        Text Link
      </Typography>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "flex-start",
  flexWrap: "wrap",
  width: "100%",
  height: "100%",
  padding: "16px",
} as any
