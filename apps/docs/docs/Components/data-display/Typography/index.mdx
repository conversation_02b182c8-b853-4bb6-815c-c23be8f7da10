---
title: Typography
id: component:typography
slug: /components/typography
---

import TypographyAlignmentDemo from "!!raw-loader!./examples/typography-alignment"
import TypographyColorDemo from "!!raw-loader!./examples/typography-color"
import TypographyDemo from "!!raw-loader!./examples/typography-demo"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Typography component is a flexible and customizable UI element for displaying text. It supports various styles, sizes, colors, and alignments.

<ComponentPreviewUI name="typography-preview" code={TypographyDemo} />

## Props

> **Note**: The props also extend the `HTMLAttributes` of the respective HTML element based on the `level` prop.

| Name               | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Required | Default   | Description                                          |
| ------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | --------- | ---------------------------------------------------- |
| **`level`**        | `'display1' \| 'display2' \| 'h1' \| 'h2' \| 'h3' \| 'h4' \| 'h5' \| 'body1' \| 'body2' \| 'caption' \| 'textlink' \| 'displayLarge' \| 'displayMedium' \| 'displaySmall' \| 'headlineLarge' \| 'headlineMedium' \| 'headlineSmall' \| 'titleLarge' \| 'titleMedium' \| 'titleSmall' \| 'bodyLarge' \| 'bodyLargeEmphasized' \| 'bodyMedium' \| 'bodyMediumEmphasized' \| 'bodySmall' \| 'bodySmallEmphasized' \| 'labelLarge' \| 'labelLargeEmphasized' \| 'labelMedium' \| 'labelMediumEmphasized' \| 'labelSmall' \| 'labelSmallEmphasized'` |          | `body1`   | The level of the typography element.                 |
| **`align`**        | `'left' \| 'center' \| 'right' \| 'justify'`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |          | `left`    | The alignment of the text.                           |
| **`color`**        | `'default' \| 'danger' \| 'warning' \| 'success' \| 'process' \| 'primary'`                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |          | `default` | The color of the text.                               |
| **`gutterBottom`** | `bool`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |          | `false`   | If `true`, the typography will have a bottom margin. |
| **`noWrap`**       | `bool`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |          | `false`   | If `true`, the text will not wrap.                   |
| **`children`**     | `ReactNode`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |          | -         | The content of the typography.                       |

## Examples

### Variants

Different styles of typography including display, heading, body, caption, and text link.

<ComponentPreviewUI name="typography-variant" code={TypographyDemo} />

### Color

Typography with different color options like primary, danger, warning, success, and process.

<ComponentPreviewUI name="typography-color" code={TypographyColorDemo} />

### Alignment

Typography with different alignment options like left, center, right, and justify.

<ComponentPreviewUI
  name="typography-alignment"
  code={TypographyAlignmentDemo}
/>

## CSS

| Class Name               | Description                          |
| ------------------------ | ------------------------------------ |
| `.ApolloTypography-root` | Styles applied to the root container |
