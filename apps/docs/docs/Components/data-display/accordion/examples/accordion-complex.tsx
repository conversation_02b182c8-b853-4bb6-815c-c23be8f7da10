import { Accordion, Button, Typography } from "@apollo/ui"

export default function ComplexAccordion() {
  return (
    <Accordion
      defaultOpen
      borderless
      fullWidth
      variant="error"
      iconPosition="start"
      iconVariant="primary"
      label={
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography level="caption" style={{ maxWidth: "100px" }}>
            011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
          </Typography>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "flex-end",
              gap: "8px",
            }}
          >
            <Typography level="caption">เปิดทำการ</Typography>
            <Button
              onClick={(e) => e.stopPropagation()}
              variant="outline"
              size="small"
            >
              เพิ่ม
            </Button>
          </div>
        </div>
      }
    >
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Model: CJ SUPERMARKET + BAO CAFÉ&WASH </span>
        <span>Open date: 30/08/2004 Close</span>
        <span>date: 30/08/2004</span>
      </div>
    </Accordion>
  )
}
