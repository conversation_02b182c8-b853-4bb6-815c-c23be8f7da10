import { useState } from "react"
import { Accordion } from "@apollo/ui"

export default function ControlledAccordion() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div style={{ maxWidth: "500px" }}>
      <div style={{ marginBottom: "10px" }}>
        <button onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? "Close Accordion" : "Open Accordion"}
        </button>
      </div>

      <Accordion
        label="Controlled Accordion"
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <p>
          This accordion's state is controlled externally. Use the button above
          to toggle it.
        </p>
      </Accordion>
    </div>
  )
}
