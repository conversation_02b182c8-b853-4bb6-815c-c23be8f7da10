---
title: Accordion
id: component:accordion
slug: /components/accordion
---

import BasicDemo from "!!raw-loader!./examples/accordion-basic"
import ComplexDemo from "!!raw-loader!./examples/accordion-complex"
import ControlledDemo from "!!raw-loader!./examples/accordion-controlled"
import DisabledDemo from "!!raw-loader!./examples/accordion-disabled"
import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The `Accordion` component is used to create collapsible content sections that can be expanded or collapsed to show or hide information. It helps manage space and information hierarchy in the UI.

<ComponentPreviewUI name="accordion-preview" code={BasicDemo} />

## Props

| Name               | Type                         | Required | Default     | Description                                          |
| ------------------ | ---------------------------- | -------- | ----------- | ---------------------------------------------------- |
| **`label`**        | `ReactNode`                  | ✓        | -           | Content for the accordion header                     |
| **`open`**         | `boolean`                    |          | -           | Controls the open state when component is controlled |
| **`defaultOpen`**  | `boolean`                    |          | `false`     | Initial open state for uncontrolled component        |
| **`onOpenChange`** | `(open: boolean) => void`    |          | -           | Callback when open state changes                     |
| **`iconPosition`** | `"start" \| "end" \| "both"` |          | `"end"`     | Position of the chevron icon                         |
| **`borderless`**   | `boolean`                    |          | `false`     | Removes the border styling                           |
| **`variant`**      | `"default" \| "error"`       |          | `"default"` | Visual variant of the accordion                      |
| **`iconVariant`**  | `"default" \| "primary"`     |          | `"default"` | Visual variant of the icon                           |
| **`fullWidth`**    | `boolean`                    |          | `false`     | Whether accordion takes full width of container      |
| **`keepMounted`**  | `boolean`                    |          | `false`     | Keep contents mounted when closed                    |
| **`ref`**          | `React.Ref<HTMLDivElement>`  |          | -           | Ref for the root element                             |

## Examples

### Basic Accordion

A simple accordion example that expands and collapses content.

<ComponentPreviewUI name="accordion-basic" code={BasicDemo} />

### Controlled Accordion

An example of a controlled accordion where the open state is managed externally.

<ComponentPreviewUI name="accordion-controlled" code={ControlledDemo} />

### Complex Accordion

A more complex example with custom label content, including typography and buttons. This showcases how to use the accordion with multiple customizations like `borderless`, `fullWidth`, `iconPosition`, and `iconVariant`.

<ComponentPreviewUI name="accordion-complex" code={ComplexDemo} />

## CSS

| Class Name                         | Description                           |
| ---------------------------------- | ------------------------------------- |
| `.ApolloAccordion-container`       | Styles applied to the root element    |
| `.ApolloAccordion-trigger`         | Styles applied to the trigger element |
| `.ApolloAccordion-trigger-content` | Styles applied to the trigger content |
| `.ApolloAccordion-panel`           | Styles applied to the panel           |
