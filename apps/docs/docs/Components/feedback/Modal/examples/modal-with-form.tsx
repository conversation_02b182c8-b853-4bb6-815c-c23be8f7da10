import React, { useState } from "react"
import { Button, Input, Modal, Typography } from "@apollo/ui"

export default function ModalWithForm() {
  const [open, setOpen] = useState(false)
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")

  const handleSubmit = () => {
    console.log("Form submitted:", { name, email })
    setOpen(false)
  }

  return (
    <>
      <Button variant="filled" onClick={() => setOpen(true)}>
        Open Form Modal
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header>
          <Typography level="h3">User Information</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <div className="flex flex-col gap-4">
            <Typography level="body1">
              Please fill in your information below:
            </Typography>
            <Input
              label="Name"
              placeholder="Enter your name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            <Input
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
        </Modal.Content>
        <Modal.Footer>
          <Button
            className="self-stretch"
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            className="self-stretch"
            variant="filled"
            onClick={handleSubmit}
            disabled={!name || !email}
          >
            Submit
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
