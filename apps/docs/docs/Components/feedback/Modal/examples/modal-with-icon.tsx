import React, { useState } from "react"
import { Button, <PERSON>dal, Typography } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"

export default function ModalWithIcon() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button variant="filled" onClick={() => setOpen(true)}>
        Open Modal with Icon
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header
          icon={<InfoCircle size={20} className="text-primary-500" />}
        >
          <Typography level="h3">Modal with Icon</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <Typography level="body1">
            This modal includes an icon in the header. Icons can be used to
            enhance the visual appearance and provide context about the modal's
            purpose.
          </Typography>
        </Modal.Content>
        <Modal.Footer>
          <Button
            className="self-stretch"
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            className="self-stretch"
            variant="filled"
            onClick={() => setOpen(false)}
          >
            OK
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
