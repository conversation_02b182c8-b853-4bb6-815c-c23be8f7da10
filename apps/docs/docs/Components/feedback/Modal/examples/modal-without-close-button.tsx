import React, { useState } from "react"
import { Button, Modal, Typography } from "@apollo/ui"

export default function ModalWithoutCloseButton() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button variant="filled" onClick={() => setOpen(true)}>
        Open Modal Without Close Button
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header>
          <Typography level="h3">No Close Button</Typography>
          {/* No Close Button */}
        </Modal.Header>
        <Modal.Content>
          <Typography level="body1">
            This modal doesn't have a close button in the header. Users can only
            close this modal using the buttons in the footer, which provides
            more control over how users can dismiss the modal.
          </Typography>
        </Modal.Content>
        <Modal.Footer>
          <Button
            className="self-stretch"
            variant="filled"
            onClick={() => setOpen(false)}
          >
            Close Modal
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
