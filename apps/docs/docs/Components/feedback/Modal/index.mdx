---
title: Modal
id: component:modal
slug: /components/modal
---

import ModalBasic from "!!raw-loader!./examples/modal-basic"
import ModalCustomButtons from "!!raw-loader!./examples/modal-custom-buttons"
import ModalCustomStyling from "!!raw-loader!./examples/modal-custom-styling"
import ModalWithForm from "!!raw-loader!./examples/modal-with-form"
import ModalWithIcon from "!!raw-loader!./examples/modal-with-icon"
import ModalWithoutCloseButton from "!!raw-loader!./examples/modal-without-close-button"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Modal component is a dialog box used to present content in a focused manner, requiring user attention or interaction. It's commonly used for confirmations, forms, or displaying important information without navigating away from the current page.

<ComponentPreviewUI name="modal-basic" code={ModalBasic} />

## Usage

Modal components in Apollo Design System use a compositional pattern, consisting of multiple subcomponents that work together:

- `Modal.Root`: The container component that provides context and functionality for the modal
- `Modal.Header`: The header section of the modal, typically containing a title and close button
- `Modal.Content`: The main content area of the modal
- `Modal.Footer`: The footer section, typically containing action buttons
- `Modal.CloseButton`: A button that closes the modal when clicked

## Props

### Modal.Root

| Name                       | Type                      | Required | Default | Description                                            |
| -------------------------- | ------------------------- | -------- | ------- | ------------------------------------------------------ |
| **`open`**                 | `boolean`                 |          | -       | Controls whether the modal is open or closed           |
| **`onOpenChange`**         | `(open: boolean) => void` |          | -       | Callback fired when the open state changes             |
| **`onOpenChangeComplete`** | `() => void`              |          | -       | Callback fired when the open state change is completed |
| **`defaultOpen`**          | `boolean`                 |          | -       | The default open state of the modal                    |
| **`dismissible`**          | `boolean`                 |          | `true`  | If `true`, the modal can be dismissed                  |
| **`className`**            | `string`                  |          | -       | Additional CSS class names for the modal               |
| **`rootProps`**            | `Dialog.Root.Props`       |          | -       | Props passed to the root Dialog component              |
| **`portalProps`**          | `Dialog.Portal.Props`     |          | -       | Props passed to the Dialog Portal component            |
| **`backdropProps`**        | `Dialog.Backdrop.Props`   |          | -       | Props passed to the Dialog Backdrop component          |
| **`popupProps`**           | `Dialog.Popup.Props`      |          | -       | Props passed to the Dialog Popup component             |
| **`children`**             | `ReactNode`               | ✅       | -       | The content of the modal                               |

### Modal.Header

| Name            | Type        | Required | Default | Description                               |
| --------------- | ----------- | -------- | ------- | ----------------------------------------- |
| **`icon`**      | `ReactNode` |          | -       | An optional icon to display in the header |
| **`className`** | `string`    |          | -       | Additional CSS class names for the header |
| **`children`**  | `ReactNode` | ✅       | -       | The content of the header                 |

### Modal.Content

| Name            | Type        | Required | Default | Description                                     |
| --------------- | ----------- | -------- | ------- | ----------------------------------------------- |
| **`className`** | `string`    |          | -       | Additional CSS class names for the content area |
| **`children`**  | `ReactNode` | ✅       | -       | The content of the modal content area           |

### Modal.Footer

| Name            | Type        | Required | Default | Description                                  |
| --------------- | ----------- | -------- | ------- | -------------------------------------------- |
| **`className`** | `string`    |          | -       | Additional CSS class names for the footer    |
| **`children`**  | `ReactNode` | ✅       | -       | The content of the footer, typically buttons |

### Modal.CloseButton

The `Modal.CloseButton` component has no specific props. It inherits props from the underlying Dialog.Close component.

## Examples

### Basic

A basic modal with header, content, and footer sections.

<ComponentPreviewUI name="modal-basic" code={ModalBasic} />

### Icon

A modal that includes an icon in the header.

<ComponentPreviewUI name="modal-with-icon" code={ModalWithIcon} />

### Custom Buttons

A modal with custom styled buttons in the footer.

<ComponentPreviewUI name="modal-custom-buttons" code={ModalCustomButtons} />

### Close Button

A modal that doesn't include a close button in the header, allowing more control over how users can dismiss it.

<ComponentPreviewUI
  name="modal-without-close-button"
  code={ModalWithoutCloseButton}
/>

### Custom Styling

A modal with custom styling applied to the header, content, and footer sections.

<ComponentPreviewUI name="modal-custom-styling" code={ModalCustomStyling} />

### Modal with Form

A modal containing a form that users can fill out and submit.

<ComponentPreviewUI name="modal-with-form" code={ModalWithForm} />

## CSS

| Class Name                 | Description                           |
| -------------------------- | ------------------------------------- |
| `.ApolloModal-popup`       | Styles applied to the popup container |
| `.ApolloModal-backdrop`    | Styles applied to the backdrop        |
| `.ApolloModal-header`      | Styles applied to the header section  |
| `.ApolloModal-content`     | Styles applied to the content section |
| `.ApolloModal-footer`      | Styles applied to the footer section  |
| `.ApolloModal-closeButton` | Styles applied to the close button    |

## Accessibility

- Modals are automatically accessible by keyboard, with focus trapped within the modal when it's open
- The Escape key can be used to close the modal
- Focus is automatically returned to the trigger element when the modal is closed
- Screen readers announce the modal as a dialog when it opens
