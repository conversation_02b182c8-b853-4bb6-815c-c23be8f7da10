import { useState } from "react"
import { CapsuleTab } from "@apollo/ui"

export default function CapsuleTabBasicDemo() {
  const [selectedTab, setSelectedTab] = useState<number>(0)

  return (
    <div style={styles}>
      <CapsuleTab
        tabs={[
          { label: "Tab 1", id: "tab1" },
          { label: "Tab 2", id: "tab2" },
          { label: "Tab 3", id: "tab3" },
        ]}
        selectedIndex={selectedTab}
        onSelect={(index) => setSelectedTab(index)}
      />
    </div>
  )
}

const styles = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  height: "100vh",
}
