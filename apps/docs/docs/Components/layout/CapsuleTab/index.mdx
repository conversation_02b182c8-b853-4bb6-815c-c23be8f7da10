---
title: CapsuleTab
id: component:capsule-tab
slug: /components/capsule-tab
---

import BasicDemo from "!!raw-loader!./examples/capsule-tab-basic"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The `CapsuleTab` component is used to create a tabbed interface with a capsule-style design. It allows users to switch between different views or content.

<ComponentPreviewUI name="capsule-tab-preview" code={BasicDemo} />

## Props

| Name            | Type                                      | Required | Default | Description                                  |
| --------------- | ----------------------------------------- | -------- | ------- | -------------------------------------------- |
| **`tabs`**      | `Array<{ id: string; label: ReactNode }>` | ✓        | -       | Array of tab objects with `id` and `label`.  |
| `selectedIndex` | `number`                                  | ✓        | -       | Index of the currently selected tab.         |
| **`onSelect`**  | `(index: number) => void`                 |          | -       | Callback when a tab is selected.             |
| **`className`** | `string`                                  |          | -       | Additional class names for the root element. |
| **`ref`**       | `React.Ref<HTMLDivElement>`               |          | -       | Ref for the root element.                    |

## Examples

### Basic Capsule Tabs

A simple example of capsule tabs with three options.

<ComponentPreviewUI name="capsule-tab-basic" code={BasicDemo} />

## CSS

| Class Name                       | Description                             |
| -------------------------------- | --------------------------------------- |
| `.ApolloCapsuleTab-root`         | Styles applied to the root element      |
| `.ApolloCapsuleTab-container`    | Styles applied to the container         |
| `.ApolloCapsuleTab-item`         | Styles applied to each tab item         |
| `.ApolloCapsuleTab-itemSelected` | Styles applied to the selected tab item |
