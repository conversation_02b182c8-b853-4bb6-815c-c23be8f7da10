import { useState } from "react"
import { Button, Tabs } from "@apollo/ui"

export default function TabsControlledDemo() {
  const [activeTab, setActiveTab] = useState("tab1")

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        padding: "20px",
        flexDirection: "column",
      }}
    >
      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
          <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
          <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
          <Tabs.Indicator />
        </Tabs.List>
        <Tabs.Panel value="tab1">Content for controlled tab 1</Tabs.Panel>
        <Tabs.Panel value="tab2">Content for controlled tab 2</Tabs.Panel>
        <Tabs.Panel value="tab3">Content for controlled tab 3</Tabs.Panel>
      </Tabs.Root>

      <div style={{ marginTop: "16px" }}>
        <div
          style={{
            display: "flex",
            gap: "8px",
            marginTop: "8px",
            justifyContent: "center",
          }}
        >
          <Button onClick={() => setActiveTab("tab1")}>Select Tab 1</Button>
          <Button onClick={() => setActiveTab("tab2")}>Select Tab 2</Button>
          <Button onClick={() => setActiveTab("tab3")}>Select Tab 3</Button>
        </div>
        <p style={{ marginTop: "8px", textAlign: "center" }}>
          Active tab value: {activeTab}
        </p>
      </div>
    </div>
  )
}
