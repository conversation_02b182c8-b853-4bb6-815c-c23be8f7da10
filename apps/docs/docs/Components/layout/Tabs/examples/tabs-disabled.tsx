import { Tabs } from "@apollo/ui"

export default function TabsDisabledDemo() {
  return (
    <div style={{ display: "flex", justifyContent: "center", padding: "20px" }}>
      <Tabs.Root defaultValue="tab1">
        <Tabs.List>
          <Tabs.Tab value="tab1">Enabled Tab</Tabs.Tab>
          <Tabs.Tab value="tab2" disabled>
            Disabled Tab
          </Tabs.Tab>
          <Tabs.Tab value="tab3">Enabled Tab</Tabs.Tab>
          <Tabs.Indicator />
        </Tabs.List>
        <Tabs.Panel value="tab1">Content for enabled tab 1</Tabs.Panel>
        <Tabs.Panel value="tab2">
          This content won't be easily accessible
        </Tabs.Panel>
        <Tabs.Panel value="tab3">Content for enabled tab 3</Tabs.Panel>
      </Tabs.Root>
    </div>
  )
}
