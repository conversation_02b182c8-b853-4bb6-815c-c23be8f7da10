import { Tabs } from "@apollo/ui"

export default function TabsFitContentDemo() {
  return (
    <div style={{ display: "flex", justifyContent: "center", padding: "20px" }}>
      <Tabs.Root fullWidth defaultValue="tab1">
        <Tabs.List>
          <Tabs.Tab fitContent value="tab1">
            Tab 1
          </Tabs.Tab>
          <Tabs.Tab fitContent value="tab2">
            Tab 2
          </Tabs.Tab>
          <Tabs.Tab fitContent value="tab3">
            Tab 3
          </Tabs.Tab>
          <Tabs.Indicator />
        </Tabs.List>
        <Tabs.Panel value="tab1">Content for Tab 1</Tabs.Panel>
        <Tabs.Panel value="tab2">Content for Tab 2</Tabs.Panel>
        <Tabs.Panel value="tab3">Content for Tab 3</Tabs.Panel>
      </Tabs.Root>
    </div>
  )
}
