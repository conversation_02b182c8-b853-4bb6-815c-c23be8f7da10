import { Tabs } from "@apollo/ui"

export default function TabsWithIconsDemo() {
  return (
    <div style={{ display: "flex", justifyContent: "center", padding: "20px" }}>
      <Tabs.Root defaultValue="icon1">
        <Tabs.List>
          <Tabs.Tab value="icon1">
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <span role="img" aria-label="home">
                🏠
              </span>
              <span>Home</span>
            </div>
          </Tabs.Tab>
          <Tabs.Tab value="icon2">
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <span role="img" aria-label="settings">
                ⚙️
              </span>
              <span>Settings</span>
            </div>
          </Tabs.Tab>
          <Tabs.Tab value="icon3">
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <span role="img" aria-label="profile">
                👤
              </span>
              <span>Profile</span>
            </div>
          </Tabs.Tab>
          <Tabs.Indicator />
        </Tabs.List>
        <Tabs.Panel value="icon1">Home content</Tabs.Panel>
        <Tabs.Panel keepMounted value="icon2">
          Settings content
        </Tabs.Panel>
        <Tabs.Panel value="icon3">Profile content</Tabs.Panel>
      </Tabs.Root>
    </div>
  )
}
