---
title: Tabs
id: component:tabs
slug: /components/tabs
---

import BasicDemo from "!!raw-loader!./examples/tabs-basic"
import ControlledDemo from "!!raw-loader!./examples/tabs-controlled"
import DisabledDemo from "!!raw-loader!./examples/tabs-disabled"
import FitContentDemo from "!!raw-loader!./examples/tabs-fit-content"
import FullWidthDemo from "!!raw-loader!./examples/tabs-full-width"
import IconsDemo from "!!raw-loader!./examples/tabs-with-icons"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The `Tabs` component is used to organize content into multiple sections and allow users to navigate between them.

<ComponentPreviewUI name="tabs-basic-preview" code={BasicDemo} />

## Props

### Tabs.Root

| Name            | Type                      | Required | Default | Description                                                                                                                   |
| --------------- | ------------------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------------------------- |
| `value`         | `string`                  |          | -       | The controlled value of the tab to activate. Should be used with `onValueChange`.                                             |
| `defaultValue`  | `string`                  |          | -       | The value of the tab that should be active when initially rendered. Use when you don't need to control the state of the tabs. |
| `onValueChange` | `(value: string) => void` |          | -       | Callback fired when the value changes.                                                                                        |
| `fullWidth`     | `boolean`                 |          | `false` | When true, tabs container will take up the full available width.                                                              |
| `className`     | `string`                  |          | -       | Additional CSS class for the root element.                                                                                    |

### Tabs.List

| Name        | Type     | Required | Default | Description                                |
| ----------- | -------- | -------- | ------- | ------------------------------------------ |
| `className` | `string` |          | -       | Additional CSS class for the list element. |

### Tabs.Tab

| Name         | Type                            | Required | Default    | Description                                                                 |
| ------------ | ------------------------------- | -------- | ---------- | --------------------------------------------------------------------------- |
| `value`      | `string`                        | ✓        | -          | A unique value for the tab. Used to associate with the corresponding panel. |
| `disabled`   | `boolean`                       |          | `false`    | When true, prevents the user from interacting with the tab.                 |
| `fitContent` | `boolean`                       |          | `false`    | When true, the tab width is determined by its content.                      |
| `align`      | `'left' \| 'center' \| 'right'` |          | `'center'` | Controls the text alignment within the tab.                                 |
| `className`  | `string`                        |          | -          | Additional CSS class for the tab element.                                   |

### Tabs.Indicator

| Name        | Type     | Required | Default | Description                                     |
| ----------- | -------- | -------- | ------- | ----------------------------------------------- |
| `className` | `string` |          | -       | Additional CSS class for the indicator element. |

### Tabs.Panel

| Name          | Type      | Required | Default | Description                                                            |
| ------------- | --------- | -------- | ------- | ---------------------------------------------------------------------- |
| `value`       | `string`  | ✓        | -       | A unique value that matches a corresponding tab.                       |
| `keepMounted` | `boolean` |          | -       | Whether to keep the HTML element in the DOM while the panel is hidden. |
| `className`   | `string`  |          | -       | Additional CSS class for the panel element.                            |

## Examples

### Basic Tabs

A simple example of tabs with three options.

<ComponentPreviewUI name="tabs-basic" code={BasicDemo} />

### Full Width Tabs

Use the `fullWidth` prop on the `Tabs.Root` component to make the tabs container fill the entire available width.

<ComponentPreviewUI name="tabs-full-width" code={FullWidthDemo} />

### Fit Content Tabs

Use the `fitContent` prop on individual tabs to make them only as wide as their content.

<ComponentPreviewUI name="tabs-fit-content" code={FitContentDemo} />

### Tabs with Icons

You can include icons or other elements within tabs.

<ComponentPreviewUI name="tabs-with-icons" code={IconsDemo} />

### Disabled Tabs

Use the `disabled` prop to disable individual tabs.

<ComponentPreviewUI name="tabs-disabled" code={DisabledDemo} />

### Controlled Tabs

Control the active tab from outside the component using the `value` and `onValueChange` props.

<ComponentPreviewUI name="tabs-controlled" code={ControlledDemo} />

## CSS

| Class Name              | Description                             |
| ----------------------- | --------------------------------------- |
| `.ApolloTabs-root`      | Styles applied to the root element      |
| `.ApolloTabs-list`      | Styles applied to the list element      |
| `.ApolloTabs-tab`       | Styles applied to each tab element      |
| `.ApolloTabs-indicator` | Styles applied to the indicator element |
| `.ApolloTabs-panel`     | Styles applied to each panel element    |
