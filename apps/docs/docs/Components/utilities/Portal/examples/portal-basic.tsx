import { Button, Portal } from '@apollo/ui';
import React from 'react'

const PortalBasicDemo = () => {
  const [show, setShow] = React.useState(false);
  const container = React.useRef(null);

  const handleClick = () => {
    setShow(!show);
  };

  return (
    <div>
      <Button onClick={handleClick}>
        {show ? 'Unmount children' : 'Mount children'}
      </Button>
      <div style={{ padding: '8px', margin: '4px', border: '1px solid', color: 'red' }}>
        This will be red.
        {show ? (
          <Portal container={container.current}>
            <span>This won't be red.</span>
          </Portal>
        ) : null}
      </div>
      <div style={{ padding: '8px', margin: '4px', border: '1px solid' }} ref={container} />
    </div>
  );
}

export default PortalBasicDemo