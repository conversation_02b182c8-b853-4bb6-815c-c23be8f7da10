---
title: Portal
id: component:portal
slug: /components/portal
---

import BasicDemo from "!!raw-loader!./examples/portal-basic"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The `Portal` component is a utility component that allows you to render its children into a different part of the DOM tree. This is useful for scenarios like modals, tooltips, and popovers, where you want to render content outside of the normal document flow.

:::note
Portal is a utility component built around [React's createPortal() API](https://react.dev/reference/react-dom/createPortal). It gives you the functionality of createPortal() in a convenient component form.
:::

<ComponentPreviewUI name="portal-basic" code={BasicDemo} />

## Props

| Name            | Type          | Required | Default         | Description                                               |
| --------------- | ------------- | -------- | --------------- | --------------------------------------------------------- |
| **`children`**  | `ReactNode`   | ✓        | -               | The content to be rendered within the Portal.             |
| **`container`** | `HTMLElement` |          | `document.body` | The DOM element into which the children will be rendered. |

## Examples

### Basic

<ComponentPreviewUI name="portal-basic" code={BasicDemo}/>