---
title: Report Bug
id: bug-report
slug: /contribution/bug-report
sidebar_position: 3
---

เพื่อให้สะดวกต่อการติดตาม และดูรายละเอียด ให้ทำตามขั้นตอนสั้นๆ ดังนี้

### ขั้นตอน

1. <PERSON>lone [Bug Ticket Template](https://jira.cjexpress.co.th/browse/UXUI-780) เพื่อสร้าง Ticket ใหม่

![CloneBugTicket](./images/clone-bug-ticket.png)

2. ระบุรายละเอียดของ Bug และนำลิงค์ของ Ticket ที่ได้สร้างไว้ DM ส่งให้ UI-Engineer (ชื่อนี้ **<PERSON><PERSON>**)

![BugInformation](./images/update-bug-ticket-information.png)

### การติดตามงาน

หลังจาก UI-Engineer รับทราบแล้วจะมีการ Comment ไว้ใน Ticket ในรายละเอียด Release version และวันที่ปล่อย

![Comment](./images/task-are-planned.png)

### ลำดับความสำคัญของการแก้ไข และระยะเวลาที่ต้องถูกแก้ไข (SLA)

ลำดับของ Bug ที่จะถูกแก้ไขจะขึ้นอยู่กับ Priority ที่กำหนดไว้ ซึ่งส่งผลถึงวันที่ต้องถูกแก้ไขด้วยเช่นกัน
โดยมีเกณฑ์กำหนด Priority ดังนี่้

| Priority    | Description                                                                            | Resolution Time           |
| ----------- | -------------------------------------------------------------------------------------- | ------------------------- |
| 🔥 Critical | ทำให้งานไม่สามารถไปต่อได้                                                              | 1 วัน (Hotfix)            |
| 🚨 High     | งานยังไปต่อได้ แต่เป็นปัญหาที่ทำให้ใช้งานไม่ได้ในบางกรณี                               | ภายในรอบ Release ปัจจุบัน |
| 📝 Medium   | ปัญหาที่ทำให้ใช้งานลำบาก หรือยาก แต่ยังคงใช้งานได้                                     | ภายใน Release ถัดไป       |
| 👀 Low      | ปัญหาเล็กน้อย เช่น UI ไม่ตรง หรือเพี้ยน, ปัญหาที่ทำให้เกิดความลำคาญแต่ยังใช้งานได้ปกติ | ภายใน 2 เดือน             |
