---
title: Working Process
id: workflow
slug: /contribution/workflow
sidebar_position: 1
---

## Overview

เพื่อให้พวกเราสามารถทำงานได้อย่างราบลื่น และง่ายต่อการติดตาม ทางทีมจึงได้มีการทำ Workflow ขึ้นมาเพื่อให้ในทุกๆลูปการทำงานเป็นไปในทิศทางเดียวกัน

### ประโยชน์ของ Workflow นี้คืออะไร

- **👀 ติดตามงานได้ง่ายขึ้น**: เนื่องจากเรามี Workflow ที่ชัดเจนแล้ว จึงสามารถบอกได้ว่าปัจจุบันงานถึง Step ไหนแล้ว และหลังจากนั้นจะเป็นอย่างไร
- **📅 Timeline ชัดเจน**: ในหนึ่ง loop ของ Workflow เราจะมีการกำหนด Release Date ที่ชัดเจน จึงทำให้สามารถรู้วันที่จะมีการปล่อย Update ออกไปทำให้สามารถวางแผนหรือ ทำงานต่อไปได้โดยไม่ต้องรอแบบไม่รู้จุดหมาย
- **📝 มีรายละเอียดการอัพเดทที่ชัดเจน**: ในแต่ละรอบ Release ใหม่ทุกครั้งจะมีการทำ Release Planned ที่มีรายละเอียดของสิ่งต่างๆที่จะถูกหยิบขึ้นมาทำ และทุกครั้งก่อน Publish จริงๆ จะมีการทำ Changelogs มาสรุปอีกทีครับ

## Workflow จะเป็นแบบไหน?

![Workflow](./images/design-system-workflow.drawio.png)

<br />

ในขั้นตอนต่างๆ จะถูกแบ่งเป็นสองกลุ่ม Cycle คือ

### Backlogs Cycle

ในส่วนของ Cycle นี้จะเป็น Loop สำหรับการพูดคุย และเปิด Ticket ขึ้นมาเพื่อแก้ไข/เพิ่ม Component หรือแก้ Bug ก็ตาม ซึ่งเป็นหนึ่งในขั้นตอนที่สำคัญเพราะจะเป็นการพูดคุยถึงสิ่งที่ต้องการ หรือปัญหาที่เกิด และรวมถึงรับทราบ Due Date ของอัพเดทนั้นๆด้วย
ก่อน หรือในระหว่าง Discussion จะมีการเปิด Jira Ticket ขึ้นมาเพื่อระบุรายละเอียด โดยจะมีขั้นตอนจะมีสองแบบ

- [Bug Report](/docs/contribution/bug-report): การเปิด Ticket เพื่อแจ้ง Bug
- [Component Request](/docs/contribution/component-request): การเปิด Ticket เพื่อขอ Component ใหม่ หรือต้องการอัพเดทให้กับ Component เพื่อใช้งานใน Feature ต่างๆ

👨‍💻 ผู้ที่มีส่วนร่วม: UI-Engineer, Developer, Designer, PM/PO หรือใครก็ตามที่ต้องการอัพเดท Apollo

### Release Cycle

ในแต่ละรอบการทำงานเราจะเรียกว่า หนึ่ง Release ซึ่งจะประกอบด้วย Task ที่ถูกหยิบมาจาก Backlogs หนึ่งชุด โดยทุกครั้งที่เริ่ม Release ใหม่จะมีประกาศ Release Planned และมี Changelogs ในตอนสุดท้ายที่ Publish ออกมา

หากสรุปขั้นตอนตามลำดับการทำงานจะเป็นแบบนี้

<br />

```mermaid
graph LR;
RP3["📢 Release Plan "] --> Dev3["⚙️ Development"] --> PB3["Publish 🚀"] --> CL3["📢 Changelogs"]
```

<br />

- ⬇️ **Release Plan**: ทำ Release Planned กำหนดเลข version เช่น v1.0.0 และเลือก Ticket จาก Backlogs โดยเรียงตาม Priority และกำหนด Release Date ที่ชัดเจน
- ⬇️ **Announcement (1st)**: เพื่อให้เกิดความชัดเจนและมั่นใจ ก่อนเริ่ม Develop สิ่งต่างๆ จะมีการ Announce รายละเอียดของ Release นี้ก่อนเริ่มทำ โดยจะให้ทุกคนบนช่องทาง Announce เป็นคนตรวจสอบหากมี Concern หรือ Feedback
- ⬇️ **Development**: หลังจาก Announcement แล้ว 1 วันจึงจะเริ่มทำงานโดย เรียงลำดับตามความสำคัญที่จัดไว้
- ⬇️ **Official Publish**: ขั้นตอนนี้จะเป็นการ upload ตัว Package ขึ้น Gitlab Registry
- ⬇️ **Announcement (2nd)**: ประกาศแนบ Changelogs เพื่อสรุปสิ่งที่จะถูก Publish ออกไปโดยจะประกอบไปด้วยงานที่ทำเสร็จในรอบ Release นี้ กรณีเกิดปัญหาทำให้งานไม่เสร็จในรอบนี้ Ticket นั้นจะถูกเลื่อนออกไปยัง Release ต่อไป

👨‍💻 ผู้ที่มีส่วนร่วม: UI-Engineer, Designer

### สรุป

ในทุกๆ Sprint หรือ รอบ Release เราจะทำตามขั้นตอนด้านบนเสมอเพื่อให้สามารถวัดผล แก้ไขปัญหาทั้งในเชิงการทำงาน และคุณภาพของงาน อีกทั้งเพื่อให้ง่ายและเห็นภาพชัดเจนกับทุกๆคน
