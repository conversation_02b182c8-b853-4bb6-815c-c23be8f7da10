---
title: Component Request
id: component-request
slug: /contribution/component-request
sidebar_position: 2
---

สำหรับการ Develop ฟีเจอร์ใหม่ๆขึ้นมาในระบบ ในบางครั้ง Component ใน Apollo อาจจะยังไม่มี หรือไม่รองรับ Feature นั้นๆ
ซึ่งในกรณีนี้สามารถขอให้มีการเพิ่ม หรืออัพเดท Component ได้โดยการสร้าง Ticket ขึ้นมาใน Backlogs

ให้ทำตามขั้นตอนสั้นๆ ดังนี้

### ขั้นตอน

1. Clone Ticket 👉 [Component Request Template](https://jira.cjexpress.co.th/browse/UXUI-779) เพื่อสร้าง Ticket ใหม่

![CloneBugTicket](./images/clone-component-request-ticket.png)

2. เพิ่มรายละเอียดข้อมูลในการ์ด ตามที่ต้องการ และนำลิงค์ของ Ticket ที่ได้สร้างไว้ DM ส่งให้ UI-Engineer (ชื่อนี้ **<PERSON><PERSON>**)

![UpdateInformation](./images/update-component-request-information.png)

### การติดตามงาน

หลังจาก UI-Engineer รับทราบแล้วจะมีการ Comment ไว้ใน Ticket ในรายละเอียด Release version และวันที่ปล่อย

![Comment](./images/task-are-planned.png)

### ข้อควรทราบ

หากในกรณี Sprint Start Date นั้นกระชั้นชิดเกินไป ไม่สามารถรอรอบ Release ได้ทางทีมจะพิจารณาเป็นเคสๆ ขึ้นอยู่กับความเป็นไปได้ โดยจะออกเวอร์ชั่นทดสอบให้ เพื่อให้สามารถนำไปไปทำงานต่อไปได้

ดังนั้นขอความร่วมมือติดต่อ และสร้าง Ticket ล่วงหน้า เพื่อให้ทางทีมสามารถเตรียมพร้อมและทำออกมาได้ทันเวลาครับ 🙏
