---
title: Installation
id: icon-installation
slug: /icon-installation
sidebar_position: 6
---

import { IconLibrary } from "@site/src/components/IconLibrary"

## Installation

To use icons in your project, you need to install the Apollo Icons package:

:::info
Currently our Apollo Design System still being a private design system using internally at our company.
Please contact the maintainers (@<PERSON><PERSON> Jealwarakun) for the `AUTH_TOKEN`.
:::

### Update .npmrc

```text {2} title=".npmrc" showLineNumbers=3
  @apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
+ @design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
  //gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>
```

```bash
# Using npm
npm install @design-systems/apollo-icons

# Using yarn
yarn add @design-systems/apollo-icons

# Using pnpm
pnpm add @design-systems/apollo-icons
```

## Usage

Import the icons you need from the package:

```tsx
import { CheckCircle, Heart, Search } from "@design-systems/apollo-icons"

function MyComponent() {
  return (
    <div>
      <Heart size={24} />
      <Search size={20} />
      <CheckCircle size={16} color="green" />
    </div>
  )
}
```

### Props

Icons accept the following props:

| Prop        | Type               | Default            | Description                             |
| ----------- | ------------------ | ------------------ | --------------------------------------- |
| `size`      | `number \| string` | `24`               | Size of the icon (width and height)     |
| `color`     | `string`           | Current text color | Color of the icon (any valid CSS color) |
| `className` | `string`           |                    | Additional CSS class names              |
| `style`     | `CSSProperties`    |                    | Additional inline styles                |
