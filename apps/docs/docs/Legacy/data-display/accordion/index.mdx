---
title: Accordion
id: legacy:component:accordion
slug: /legacy/components/accordion
---

import { Accordion } from "@design-systems/apollo-ui"

## Usage

```tsx live
function AccordionDemo() {
  return (
    <div className="w-full flex flex-col justify-start items-start">
      <Accordion header="Accordion header">
        <Typography level="body-1">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget.
        </Typography>
      </Accordion>
    </div>
  )
}
```

## Props

| Name                | Type                          | Required | Default | Description                                                                                                 |
| ------------------- | ----------------------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------- |
| **`header`**        | `ReactNode`                   | ✅       | -       | The header of the accordion. When a component is passed instead of a string, default styles will not apply. |
| **`icon`**          | `ReactNode`                   |          | -       | Custom icon. The expanding state won't be applied. Needs self-control.                                      |
| **`disabled`**      | `bool`                        |          | -       | If `true`, the accordion will be collapsed, hiding its content.                                             |
| **`expanded`**      | `bool`                        |          | `true`  | If `true`, the component is initially expanded. If `onStateChange` is set, this prop will be controlled.    |
| **`onStateChange`** | `(expanded: boolean) => void` |          | -       | Event handler for when the expanding state changes. If set, the component becomes controlled.               |
| **`iconPosition`**  | `"start" \| "end"`            |          | `"end"` | Position of the icon in the accordion header.                                                               |
| **`borderless`**    | `bool`                        |          | -       | If `true`, the accordion will not have a border.                                                            |
| **`hasDivider`**    | `bool`                        |          | -       | If `true`, the accordion will have a divider.                                                               |

## Examples

### Default

By default, the accordion will be an uncontrolled component and will function correctly without any expanded state.

```tsx live
function AccordionDemo() {
  return (
    <div className="w-full flex flex-col justify-start items-start">
      <Accordion header="Accordion header">
        <Typography level="body-1">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget.
        </Typography>
      </Accordion>
    </div>
  )
}
```

### Disabled

When `disabled` is true, the accordion will be collapsed.

```tsx live
function DisabledAccordion() {
  return (
    <Accordion header="Controlled Accordion" disabled>
      <Typography level="body-1">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
      </Typography>
    </Accordion>
  )
}
```

### Controlled

You can control the expanding state by passing the onStateChange prop, making the component becomes controlled.

```tsx live
function ControlledAccordion() {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <Accordion
      header="Controlled Accordion"
      expanded={isExpanded}
      onStateChange={setIsExpanded}
    >
      <Typography level="body-1">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
      </Typography>
    </Accordion>
  )
}
```

### Customization

You can customize the accordion by passing your own header and icon components, controlling the expanded state, and using props like `iconPosition` and `borderless` to adjust its appearance and behavior.

```tsx live
function AccordionDemo() {
  const [isExpanded, setIsExpanded] = useState(true)
  return (
    <div className="flex flex-col justify-start items-center">
      <Accordion
        borderless
        expanded={isExpanded}
        onStateChange={setIsExpanded}
        className="w-[352px]"
        header={
          <div className="flex w-full flex-row justify-between items-center gap-2">
            <Typography level="caption" className="line-clamp-2">
              011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
            </Typography>
            <div className="flex flex-row justify-end items-center gap-2">
              <Typography level="caption" className="whitespace-nowrap">
                เปิดทำการ
              </Typography>
              <Button
                onClick={(e) => {
                  e.stopPropagation()
                  alert("Hi")
                }}
                variant="outline"
              >
                เพิ่ม
              </Button>
            </div>
          </div>
        }
        iconPosition="start"
      >
        <ul>
          <li>
            <Typography level="caption">
              Model: CJ SUPERMARKET + BAO CAFÉ&WASH
            </Typography>
          </li>
          <li>
            <Typography level="caption">Open date: 30/08/2004</Typography>
          </li>
          <li>
            <Typography level="caption">Close date: 30/08/2004</Typography>
          </li>
        </ul>
      </Accordion>
      <Accordion
        borderless
        variant="error"
        className="w-[352px]"
        header={
          <div className="flex w-full flex-row justify-between items-center gap-2">
            <Typography level="caption" className="line-clamp-2">
              011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
            </Typography>
            <div className="flex flex-row justify-end items-center gap-2">
              <Typography level="caption" className="whitespace-nowrap">
                เปิดทำการ
              </Typography>
              <Button
                onClick={(e) => {
                  e.stopPropagation()
                  alert("Hi")
                }}
                variant="outline"
              >
                เพิ่ม
              </Button>
            </div>
          </div>
        }
        iconPosition="start"
      >
        <ul>
          <li>
            <Typography level="caption">
              Model: CJ SUPERMARKET + BAO CAFÉ&WASH
            </Typography>
          </li>
          <li>
            <Typography level="caption">Open date: 30/08/2004</Typography>
          </li>
          <li>
            <Typography level="caption">Close date: 30/08/2004</Typography>
          </li>
        </ul>
      </Accordion>
    </div>
  )
}
```

### Divider

//TODO: Description
To show a divider in the accordion, you can use the `hasDivider` prop. This will add a divider line between the header and body of the accordion.

```tsx live
function AccordionWithDivider() {
  return (
    <Accordion header="Accordion with Divider" hasDivider borderless>
      <Typography level="body-1">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
      </Typography>
    </Accordion>
  )
}
```

## CSS

| Class Name                | Description                          |
| ------------------------- | ------------------------------------ |
| `.ApolloAccordion-root`   | Styles applied to the root container |
| `.ApolloAccordion-header` | Styles applied to the header         |
| `.ApolloAccordion-body`   | Styles applied to the body           |
| `.ApolloAccordion-icon`   | Styles applied to the icon           |
