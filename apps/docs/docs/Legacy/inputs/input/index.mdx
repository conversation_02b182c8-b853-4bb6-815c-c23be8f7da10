---
title: Input
id: legacy:component:input
slug: /legacy/components/input
---

import { Input } from "@design-systems/apollo-ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import { Eye, Info } from "lucide-react"


```tsx
import { Input } from "@design-systems/apollo-ui"
```

Displays an input or a component that looks like an input.

```tsx live
function InputDemo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Input />
      <Input label="test" required />
      <Input size="small" />
      <Input label="Label" placeholder="Placeholder" helperText="lorem ipsum" />
      <Input error label="Label" helperText="error message" />
      <Input
        disabled
        label="Label"
        helperText="helper text"
        value="Disabled Input"
      />
      <Input label="Multiline" multiline helperText="lorem ipsum" />
    </div>
  )
}
```

## Props

> This component extends the type from `InputProps` which is from [@mui/base](https://mui.com/base-ui/react-input).

| Name                 | Type                    | Required | Default   | Description                                                                                    |
| -------------------- | ----------------------- | -------- | --------- | ---------------------------------------------------------------------------------------------- |
| **`variant`**        | `'outline'`             |          | `outline` | Input variant                                                                                  |
| **`color`**          | `'danger' \| 'primary'` |          | `primary` | The color of the component. It supports those theme colors that make sense for this component. |
| **`size`**           | `'medium' \| 'small'`   |          | `medium`  | The size of input                                                                              |
| **`disabled`**       | `bool`                  |          | -         | If `true`, the component is disabled.                                                          |
| **`fullWidth`**      | `bool`                  |          | -         | If `true`, the input will take up the full width of its container.                             |
| **`startDecorator`** | `node`                  |          | -         | Element placed before the children.                                                            |
| **`endDecorator`**   | `node`                  |          | -         | Element placed after the children.                                                             |
| **`error`**          | `bool`                  |          | -         | If `true`, the input will indicate an error.                                                   |
| **`label`**          | `string`                |          | -         | The label of input element.                                                                    |
| **`helperText`**     | `string`                |          | -         | text displayed below the input element.                                                        |

## Examples

### Outline (default)

A simple input with the default outline variant.

```tsx live
function InputDemo() {
  return (
    <Input />
  )
}
```

### Size

There're two size of input which are `medium` and `small`.

```tsx live
function InputDemo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Input size="medium" label="Default Size" />
      <Input size="small" label="Small Size" />
    </div>
  )
}
```

### Decorator

An input with elements placed before and after the input field.

```tsx
import { Input } from "@design-systems/apollo-ui"
import { Info } from "lucide-react"
```

```tsx live
function InputDemo() {
  return (
      <Input endDecorator={<Info size={16} />} />
  )
}
```

### Disabled

An input that is disabled and cannot be interacted with.

```tsx live
function InputDemo() {
  return (
     <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <Input
        disabled
        type="password"
        placeholder="Password"
        endDecorator={<Eye />}
      />
      <Input disabled />
      <Input placeholder="Placeholder" disabled />
      <Input label="Label" disabled />
    </div>
  )
}
```

### Error

An input that indicates an error state.

```tsx
import { Input } from "@design-systems/apollo-ui"
import { Eye } from "lucide-react"
```

```tsx live
function InputDemo() {
  return (
    <Input
      error
      type="password"
      label="Password"
      helperText="error message"
      endDecorator={<Eye />}
    />
  )
}
```

### HelperText

An input with additional descriptive text or label.

```tsx live
function InputDemo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <Input label="Label" helperText="helper text" value="Lorem ipsum" />
      <Input
        label="Label"
        helperText="Error message"
        error
        value="Lorem ipsum"
      />
    </div>
  )
}
```

### Multiple line (Textarea)

> Note: This use case is not official published on Figma yet

An input that is used as a textarea by setting the multiline prop.

```tsx live
function InputDemo() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <Input multiline size="small" />
      <Input multiline size="medium" />
      <Input disabled multiline />
      <Input error multiline />
    </div>
  )
}
```

## CSS

| Class Name                    | Description                                     |
| ----------------------------- | ----------------------------------------------- |
| `.ApolloInput-root`           | Styles applied to the root container            |
| `.ApolloInput-input`          | Styles applied to the input element             |
| `.ApolloInput-formControl`    | Styles applied to the FormControl component     |
| `.ApolloInput-startDecorator` | Styles applied to the start decorator component |
| `.ApolloInput-endDecorator`   | Styles applied to the end decorator component   |
