---
title: How to use
id: mcp:how-to-use
slug: /mcp/how-to-use
sidebar_position: 3
---

# How to use

::::caution[In development]

This MCP is still in development. Some features may not work as expected or may change in the future. Please report any issues you encounter.

Support for `@apollo/ui` is available from version `1.0.0-beta.17` or higher. If you are using an older version, please update to the latest version to use Apollo MCP features.

:::danger[Alert]

For `@design-systems/apollo-ui` users, you must replace the import statement from `@design-systems/apollo-ui` to `@apollo/ui/legacy` in your project files to use Apollo MCP features.

:::

::::

To use Apollo MCP, you can use the VS Code Copilot to generate Apollo UI from Figma link. This is a simple and frequently used feature that allows you to quickly create UI components based on designs in Figma.

![gif-1](./images/example-copilot.gif)

## Quick Steps

:::caution

Please always verify the generated code and result. The generated code may not always be perfect, and you may need to make adjustments to fit your project requirements.

:::

To use the `generateApolloUIFromFigma` tool in VS Code Copilot, follow these steps:

1. Copy your target Figma link.

2. Fill the `#generateApolloUIFromFigma` prompt with the Figma link and your project details.

![copilot-0](./images/copilot-0.png)

3. Wait for Copilot to generate the code.

![gif-1](./images/example-copilot.gif)

4. Verify ✅ the generated code and result. Make sure to check the code quality and fix any issues before using it in your project.

## Prompt

Structure your prompt like this:

```plain title="Prompt"
#generateApolloUIFromFigma

<replace_me_figmaUrl: the Figma link you want to use as a source>

<replace_me_project_description: Tell the AI about your project, such as the framework, libraries, and any specific requirements.>
```

Example, In the case of generating UI from Figma link on the NextJS + Tailwind project. Also want to create a UI for the E-Donation List page.

```plain title="Prompt"
#generateApolloUIFromFigma

https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2076-4180&t=VANOMP6K11rp8VOI-4

สร้าง UI จาก Figma Link ข้างบนนี้ให้หน่อย
ตอนนี้ใช้ nextjs + tailwind
ดูที่หน้า E-Donation_List ใน Figma
```

## ⚠️ Limitations

The accuracy of the generated code depends on the complexity of the Figma design and the current capabilities of the Copilot or LLMs (OpenAI, Gemini, Claude, etc). It may not always produce perfect results, so manual adjustments may be necessary.

Anyway, We're aims to improve the accuracy and reliability of the generated code over time. Your feedback is valuable to us, so please report any issues or suggestions you have.
