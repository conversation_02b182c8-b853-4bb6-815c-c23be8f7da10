---
title: Installation
id: mcp:installation
slug: /mcp/installation
sidebar_position: 2
---

To make Apollo MCP work, you need to set up the Figma access token and configure the MCP server in your VS Code environment. This guide will walk you through the steps to get started.

:::note
Before start, You must install at least version `1.0.0-beta.17` or higher of the `@apollo/ui` package.
::::

## Figma Access Token

First of all, you need to have Figma access token to access Figma resources. You can obtain this token from your Figma account settings.

1. Find the account name at the top left corner of the Figma app and click on it.

![Figma1](./images/figma_1.png)

2. Click on "Settings" in the dropdown menu.

![Figma1](./images/figma_2.png)

3. You will see the modal popup with the "Security" section.

![Figma1](./images/figma_3.png)

4. Scroll down to find the "Personal Access Tokens" section and click on "Generate new token".

![Figma1](./images/figma_4.png)

5. The Generate Token modal will appear. Enter a name for your token and set for scopes with "Read-only" option. Then click on "Generate token

![Figma1](./images/figma_5.png)

6. Copy the generated token and save it in a secure place. You will need this token to set up Apollo MCP.

![Figma1](./images/figma_6.png)

To use Apollo MCP, you need to set up the MCP server and configure it on VS Code.

7. Run this command in your terminal to set up the access token when first time you use Apollo MCP. Replace `<YOUR_FIGMA_TOKEN>` with the token you copied from Figma.

:::note
Do this only first time you use Apollo MCP. or the access token has been changed or expires.
::::

```bash
# npm
npm run cj-apollo setup --figma-token <YOUR_FIGMA_TOKEN>

# yarn
yarn cj-apollo setup --figma-token <YOUR_FIGMA_TOKEN>

# pnpm
pnpm cj-apollo setup --figma-token <YOUR_FIGMA_TOKEN>
```

You must see a success message like this:

![SetupSuccess](./images/success_cli.png)

## Set up VS Code

1. Create a file at the root of your project named `.vscode/mcp.json` if it does not exist.

![mcp-file](./images/mcp-file.png)

2. Copy this code snippet and paste it into the `mcp.json` file you just created.

```json
{
    "servers": {
    "apollo-code-gen": {
      "type": "stdio",
      "command": <YOUR_COPIED_PATH>, // Replace with the copied path of `cj-apollo` at .bin folder
      "args": ["run", "mcp"]
    }
  }
}
```

3. Open `node_modules/.bin` in your project directory and find `cj-apollo` and right-click on it. and select "Copy Path".

![mcp-1](./images/mcp_1.png)

4. Replace `<YOUR_COPIED_PATH>` in the `mcp.json` file with the path you copied in step 3. It should look like this:

```json
{
  "servers": {
    "apollo-code-gen": {
      "type": "stdio",
      "command": "/path/to/your/project/node_modules/.bin/cj-apollo", // Replace with the copied path of `cj-apollo`
      "args": ["run", "mcp"]
    }
  }
}
```

5. Click at "Start" to start the MCP server.

![mcp-2](./images/start-mcp-server.png)

6. Open the Copilot Chat in VS Code. You will see the tools icon.

![mcp-3](./images/mcp-tool-button.png)

7. When to modal appears. Scroll down you must see the server name that you set up. Example: `apollo-code-gen`.

![mcp-4](./images/apollo-tools.png)

Everything is set up now. You can use Apollo MCP in your VS Code Copilot environment.
