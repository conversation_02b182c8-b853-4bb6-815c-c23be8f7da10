---
title: Tools
id: mcp:tools
slug: /mcp/tools
sidebar_position: 3
---

:::caution[In development]

Currently, We're developing the tools for Apollo MCP. So fews tools are available now. We will update this page when new tools are available.

:::

## Available Tools

The following tools are available for Apollo MCP. You can use these tools to make your AI experience more efficient and effective. Some tools are available for all users, while others are in development.

| Tool                      | Input                   | Available | Description                                                                         |
| ------------------------- | ----------------------- | :-------: | ----------------------------------------------------------------------------------- |
| createApolloUIPromptFile  |                         |    ✅     | Generate reusable prompt template (Create a prompt file similar to internal prompt) |
| generateApolloUIFromFigma | `figmaUrl: string`      |    ✅     | Generate Apollo UI from Figma Link (Using internal prompted)                        |
| transformFigmaToDSL       | `figmaUrl: string`      |    ✅     | Convert Figma to DSL format                                                         |
| readDocument              | `componentName: string` |    ⚙️     | Get context of target component. (Props, use cases, Examples)                       |
| verifyDesignToken         |                         |    ⚙️     | Verify the target file about design token usage which valid or not.                 |
