---
title: What is MCP?
id: mcp:what-is-mcp
slug: /mcp/what-is-mcp
sidebar_position: 1
---

# What is MCP?

> "MCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools."

Referrence from the [MCP website](https://modelcontextprotocol.io/introduction)

In summary, MCP (Model Context Protocol) will help us to empower AI such as VSCode Copilot or any MCP compatible AI to access the context of our Apollo Design system to help us could deliver our work faster and more efficiently.

![Structure](./images/structure.png)
