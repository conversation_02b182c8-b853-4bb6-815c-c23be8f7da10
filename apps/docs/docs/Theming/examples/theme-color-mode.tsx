import React from "react";
import { Button, createThemeV2, Theme } from "@apollo/ui"


const App: React.FC = () => {
    const [colorMode, setColorMode] = React.useState<"light" | "dark">('light');
  return (
    <div
      style={{
        display: "flex",
        gap: "16px",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        flexWrap: "wrap",
        width: "100%",
        height: "100%",
      }}
    >
      <Theme mode={colorMode}>
        <Button>{colorMode === "light" ? 'Light Mode' : 'Dark Mode'}</Button>
      </Theme>
      <Theme
        theme={createThemeV2({
          tokens: {
            alias: {
              color: {
                light: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
                dark: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
              },
            },
          },
        })}
        mode={colorMode}
      >
        <Button>Light Mode Always</Button>
      </Theme>
      <Theme mode={'dark'}>
      <Button>Dark Mode Always</Button>
      </Theme>
      <Theme mode={colorMode}>
        <Button onClick={() => setColorMode(colorMode === "light" ? "dark" : "light")}>{colorMode === "light" ? 'Toggle Mode' : 'Toggle Mode'}</Button>
      </Theme>
    </div>
  )
}

export default App
