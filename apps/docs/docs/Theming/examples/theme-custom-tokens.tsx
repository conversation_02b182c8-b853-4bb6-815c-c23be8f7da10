import React from "react";
import { Button, createThemeV2, Theme } from "@apollo/ui"

const App: React.FC = () => (
  <div style={{ display: "flex", gap: "16px", flexDirection: "row", justifyContent: "center", alignItems: "center", flexWrap: "wrap", position: "fixed", width: "100%", height: "100%", }}>
  <Theme><Button>Default</Button></Theme>
  <Theme
    theme={createThemeV2({
      tokens: {
        //Global Token
        color: {
          "green-pine": {
            0: "#000000",
            40: "#7FDA8E",
            50: "#9BF7A7",
            70: "#F6FFF2",
            100: "#FFFFFF",
          },
        },
        //Base Token
        base: {
          color: {
            primary: {
              0: "{color.green-pine.0}",
              40: "{color.green-pine.40}",
              70: "{color.green-pine.70}",
              100: "{color.green-pine.100}",
            },
          },
        },
         //<PERSON><PERSON>
        alias: {
          color: {
            light: {
              primary: {
                primary: "{base.color.primary.40}",
                "on-primary": "{base.color.primary.0}",
              },
            },
            dark: {
              primary: {
                primary: "{base.color.primary.70}",
                "on-primary": "{base.color.primary.100}",
              },
            },
          },
        },
      },
    })}
  >
    <Button>Custom</Button>
  </Theme>
  </div>
)

export default App
