---
title: Overview
id: theming
slug: /theming/Overview
sidebar_position: 6
---

import ColorMode from "!!raw-loader!./examples/theme-color-mode"
import CustomizeDesignToken from "!!raw-loader!./examples/theme-custom-tokens"
import NestedTheme from "!!raw-loader!./examples/theme-nested"
import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

Apollo allows you to customize our design tokens to satisfy UI diversity from business or brand requirements, including color, typography, space, etc.

In version 2.0, we provide a new way to customize themes. With the ability of theming has also been enhanced, including but not limited to:

1. Color mode theme
2. Multiple themes


:::note
We support **two theme versions** in our design system:

- **Theme version 2.0 (Apollo)** – use `Theme` component from `@apollo-ui`
- **Theme (Legacy)** – use `ThemeProvider` component from `@apollo-ui`
  :::

## Basic Usage

In version 2.0 we call the smallest element that affects the theme Design Token. By modifying the Design Token, we can present various themes.
You can pass `theme` to `Theme` to customize theme. After migrate to V2, theme of V2 will be applied by default.

:::caution

We support **two theme versions** in our design system:

- **Theme version 2.0 (Apollo)** – modern default (uses `@layer apollo`)
- **Theme (Legacy)** – previous design (uses `@layer legacy`)

We use the [`@layer`](https://developer.mozilla.org/en-US/docs/Web/CSS/@layer) CSS feature to separate styles for each theme version.  
This allows you to choose which theme(s) are applied in your project simply by defining the `@layer` order in your global CSS file.

If you want to use Theme (Legacy), you can read more detail in Layer section

:::

### Customize Design Token

There are 3 types of Design Token which are `Global`, `Base` and `Alias`.
By modifying token property of theme, we can modify Design Token globally. Some tokens will affect other tokens. We call these tokens Global Token.

- `Base` tokens will be affected by Global Token.
- `Alias` tokens will be affected by Base Token.

<ComponentPreviewUI name="theme-preview" code={CustomizeDesignToken} />

## Advanced

### Color Mode Theme

You can use `mode` property of `Theme` to switch color mode.

:::note
You can change color of `light` and `dark` mode separately.
:::caution
If you only have color of `light mode` in your template, you need to assign color of `dark mode` to be the same as light mode.
:::

<ComponentPreviewUI name="theme-color-mode" code={ColorMode} />

### Nested Theme

By nesting `Theme` you can apply local theme to some parts of your page. Design Tokens that have not been changed in the child theme will inherit the parent theme.

> Good to know: We use native CSS selectors to achieve this.

<ComponentPreviewUI name="theme-nested" code={NestedTheme} />

## Design Token

In Design Token, we provide a three-layer structure that is more suitable for the design, and disassemble the Design Token into three parts: Global Token, Base Token and Alias Token. These three groups of Tokens are not simple groupings, but a three-layer derivation relationship. Base Tokens are derived from Global Tokens, and Alias Tokens are derived from Base Tokens. In most cases, using Seed Tokens is sufficient for custom themes. But if you need a higher degree of theme customization, you need to understand the life cycle of Design Token in Apollo.

![Design Token](./images/design-token.png)

### Global Token

Global Token means the origin of all design intent. For example, we can add the theme color by changing color, and the algorithm inside Apollo will automatically calculate and apply a series of corresponding colors according to the Global Token:

```tsx
const theme = {
  token: {
    color: {
      pink: {
        40: "#F590A5",
      },
    },
  },
}
```

### Base Token

Base Token is a variable derived from Gloabl. It can be overridden by theme.tokens to modify the value of some Base tokens individually.

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "{color.pink.40}", // Reference Global Token
          50: "#F8B3CB", // Modify
        },
      },
    },
  },
}
```

### Alias Token

Alias Token is used to control the style of some common components in batches, which is basically a Base Token alias, or a specially processed Base Token.

```tsx
const theme = {
  token: {
    alias: {
      color: {
        primary: {
          primary: "{base.color.primary.40}", // Reference Global Token
        },
      },
    },
  },
}
```

## API

### Theme

| Name        | Type           | Required | Default | Description             |
| ----------- | -------------- | -------- | ------- | ----------------------- |
| **`theme`** | `ThemeConfig`  | -        | -       | The theme to apply      |
| **`mode`**  | `light`,`dark` | -        | `light` | The color mode to apply |

### ThemeConfig

| Name          | Type                | Required | Default | Description                          |
| ------------- | ------------------- | -------- | ------- | ------------------------------------ |
| **`tokens`**  | `ApolloDefineTokenConfig` | -        | -       | The tokens to apply                  |
| **`inherit`** | `boolean`           | -        | `true`  | Whether to inherit from parent theme |

### ApolloDefineToken

| Name             | Type     | Required | Default | Description             |
| ---------------- | -------- | -------- | ------- | ----------------------- |
| **`color`**      | `object` | -        | -       | Global Color Token      |
| **`typography`** | `object` | -        | -       | Global Typography Token |
| **`spacing`**    | `object` | -        | -       | Global Spacing Token    |
| **`radius`**     | `object` | -        | -       | Global Radius Token     |
| **`elevation`**  | `object` | -        | -       | Global Elevation Token  |
| **`base`**       | `object` | -        | -       | Base Token              |
| **`alias`**      | `object` | -        | -       | Alias Token             |

## CSS Variables

Apollo uses CSS variables to apply tokens. All CSS variables are prefixed with `apl` after transform from design tokens.

### Global CSS Variables

```tsx
const theme = {
  token: {
    color: {
      pink: {
        40: "#F590A5",
      },
    },
  },
}
```

CSS will be generated as following:

```css
:root {
  --apl-color-pink-40: #f590a5;
}
```

### Base CSS Variables

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "{color.pink.40}", // Reference Global Token
          50: "#F8B3CB", // Modify
        },
      },
    },
  },
}
```

CSS will be generated as following:

```css
:root {
  --apl-base-color-primary-40: #f590a5;
  --apl-base-color-primary-50: #f8b3cb;
}
```

### Alias CSS Variables

```tsx
const theme = {
  token: {
    alias: {
      color: {
        primary: {
          primary: "{base.color.primary.40}", // Reference Global Token
        },
      },
    },
  },
}
```

CSS will be generated as following:

```css
:root {
  --apl-alias-color-primary-primary: #f590a5;
}
```

## Layer

We use `@layer` to manage the order of CSS variables. There are 4 layers in Apollo:

### How Theme Layers Work

The theme styles are grouped into **four layers**:

| Layer Name | Purpose                                                          |
| ---------- | ---------------------------------------------------------------- |
| `reset`    | Global reset styles for consistent rendering across browsers     |
| `base`     | Base typography, layout styles                                   |
| `apollo`   | **Theme verion 2.0 (Apollo)** – m3 design tokens, and components |
| `legacy`   | **Theme (Legacy)** – legacy tokens, and components               |

**Layer order matters** – later layers override earlier ones if styles conflict.

### Using Theme version 2.0 (Apollo)

By default, our styles are loaded in the following order for **Theme version 2.0**:

```css
@layer reset, base, legacy, apollo;
```

This means:

1. Browser resets (`reset`)
2. Base styles (`base`)
3. Theme (Legacy) styles (`legacy`)
4. Theme version 2.0 (Apollo) styles (`apollo`) _(final layer, takes precedence)_

### Using Theme (Legacy)

To use **Theme Version 1**, you need to include the `legacy` layer **after** `apollo`.  
This ensures that legacy styles override Theme V2 where applicable.

**Example (Global CSS file):**

```css
@layer reset, base, apollo, legacy;
```

Order explanation:

1. Reset styles
2. Base styles
3. heme version 2.0 (Apollo) styles (for components that are unchanged)
4. **Theme (Legacy) overrides** (final layer, takes precedence)

### Mixing Themes (Not Recommended)

Technically, you can load both `apollo` and `legacy` layers, but this can lead to inconsistent styles.  
If you must mix, **always place `legacy` last** so it overrides version 2.0 (Apollo) styles where needed.


### Quick Reference

| Goal                                | Layers to Include                     |
| ----------------------------------- | ------------------------------------- |
| Use only Theme version 2.0 (Apollo) | `@layer reset, base, apollo;`         |
| Use Theme (Legacy)                  | `@layer reset, base, apollo, legacy;` |
| Mix Legacy overrides on Apollo      | `@layer reset, base, apollo, legacy;` |


### Notes

- **Order matters**: CSS from later layers always overrides earlier layers if selectors match.
- If you omit a layer from your `@layer` declaration, its styles will still load **but may have lower priority**.
- The `apollo` layer contains the most recent tokens and styles – it is the foundation for future updates.
