---
title: Introduction
id: introduction
slug: /intro
sidebar_position: 1
---

:::caution
This introduction is out to date. We're writing the new one.
:::

`apollo/ui`, a `React` ui component library, brings together the best of both worlds: the utility-first approach of [`TailwindCSS`](https://tailwindcss.com/) and the robust component library of [`BaseUI`](https://mui.com/base-ui/getting-started/).

by design, the available props are vary similar to `Material UI`/`JoyUI` on purpose. for user who get used to [`Material UI`](https://mui.com/material-ui/getting-started/) using this library can feel at home. it's lightweight and extendable to fit the needs.

🎉 bug report or contributions are welcome ([read more](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/CONTRIBUTING.md)).
