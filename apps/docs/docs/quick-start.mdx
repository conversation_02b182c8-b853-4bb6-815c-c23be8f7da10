---
title: Quick Start
description: Install the library with ease.
sidebar_position: 2
slug: /quick-start
---

## Installation

Currently our Apollo Design System still being a private design system using internally at our company.

To install the `apollo/ui` package, we need to configure the `.npmrc` file (create this file in the project root) to point to the our custom private registry on Gitlab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

### Install the Package

:::caution
if you're outside CJ network. the VPN are need to install this package.
:::

```bash
# with pnpm
pnpm add @apollo/ui

# with yarn
yarn add @apollo/ui

# with npm
npm install @apollo/ui
```

### Set up the App

To ensure consistent styling, use the `ThemeProvider` and import `style.css` from the `@apollo/ui` package. It injects all design token CSS variables into the DOM.

```jsx title="App.js"
import { createTheme, ThemeProvider } from "@apollo/ui"

import "@apollo/ui/style.css"

const appTheme = createTheme()

function App({ children }) {
  return <ThemeProvider theme={appTheme}>{children}</ThemeProvider>
}
```

## Usage

You can import and use components directly:

```jsx title="Button Example" {1,4}
import { Button } from "@apollo/ui"

export default function Example() {
  return <Button>Hello World</Button>
}
```
