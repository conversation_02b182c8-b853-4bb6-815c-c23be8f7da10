import path from "path"
import type * as Preset from "@docusaurus/preset-classic"
import type { Config } from "@docusaurus/types"
import { themes as prismThemes } from "prism-react-renderer"

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

const config: Config = {
  title: "Apollo Design System",
  tagline: "Apollo Design System",
  favicon: "img/favicon.ico",

  // Set the production url of your site here
  url: "https://cjx-apollo-ui.netlify.app/",
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: "/",

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  organizationName: "CJ", // Usually your GitHub org/user name.
  projectName: "apollo-design-system", // Usually your repo name.

  onBrokenLinks: "throw",
  onBrokenMarkdownLinks: "warn",
  markdown: {
    mermaid: true,
  },
  themes: ["@docusaurus/theme-mermaid", "@docusaurus/theme-live-codeblock"],

  future: {
    v4: true,
    // experimental_faster: true,
  },
  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: "en",
    locales: ["en"],
  },
  plugins: [
    "@orama/plugin-docusaurus-v3",
    [
      "@docusaurus/plugin-google-gtag",
      {
        trackingID: "G-YTDF8RPR28",
        anonymizeIP: true,
      },
    ],

    function myPlugin() {
      return {
        name: "my-alias-plugin",
        configureWebpack() {
          return {
            resolve: {
              alias: {
                "@/utils": path.resolve(
                  process.cwd(),
                  "../../packages/ui/src/utils"
                ),
                "@/components": path.resolve(
                  process.cwd(),
                  "../../packages/ui/src/components"
                ),
                "@/internal": path.resolve(
                  process.cwd(),
                  "../../packages/ui/src/internal"
                ),
                "@/hooks": path.resolve(
                  process.cwd(),
                  "../../packages/ui/src/hooks"
                ),
                // "@/utils": "../../packages/ui/src/utils",
              },
            },
          }
        },
      }
    },
  ],
  presets: [
    [
      "classic",
      {
        docs: {
          sidebarCollapsed: true,
          sidebarPath: "./sidebars.ts",
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            "https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/",
        },
        blog: {
          showReadingTime: true,
          feedOptions: {
            type: ["rss", "atom"],
            xslt: true,
          },
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            "https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/tree/74951cb42da29c25c477a509ac572acbc4721c55/apps/docs/blog/",
          // Useful options to enforce blogging best practices
          onInlineTags: "warn",
          onInlineAuthors: <AUTHORS>
          onUntruncatedBlogPosts: "warn",
        },
        theme: {
          customCss: "./src/css/custom.css",
        },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    image: "img/logo.png",
    navbar: {
      title: "Apollo",
      logo: {
        alt: "Apollo",
        src: "img/logo-flat.svg",
      },
      items: [
        {
          type: "docSidebar",
          sidebarId: "documentSidebar",
          position: "left",
          label: "Documentation",
        },
        { to: "/blog", label: "Blog", position: "left" },
      ],
    },
    footer: {
      style: "dark",
      links: [
        {
          title: "Docs",
          items: [
            {
              label: "Quick Start",
              to: "/docs/quick-start",
            },
          ],
        },
        {
          title: " ",
          items: [
            {
              label: "Blog",
              to: "/blog",
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} Apollo, by CJ MORE. Built with Docusaurus.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
    },
  } satisfies Preset.ThemeConfig,
}

export default config
