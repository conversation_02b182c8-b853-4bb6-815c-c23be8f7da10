import styles from "!!raw-loader!../../../../../packages/apollo-ui/dist/index.css"
import { Sandpack } from "@codesandbox/sandpack-react"
import CodeBlock from "@theme/CodeBlock"
import tokenRaw from "raw-loader!../../../../../packages/apollo-token/dist/index.mjs"
import designSystemRaw from "raw-loader!../../../../../packages/apollo-ui/dist/index.js"
import iconRaw from "raw-loader!../../../../../packages/icons/build-sandpack/icons-react"
import legacyTokenRaw from "raw-loader!../../../../../packages/tokens/build-sandpack/index.js"
// @ts-ignore
import indexFile from "raw-loader!./base/main.tsx"

import packageConfig from "../../../../../packages/apollo-ui/package.json"

const dependencies = {
  ...Object.entries(packageConfig.dependencies)
    .filter(
      ([key, value]) =>
        value !== "workspace:*" && key !== "@design-systems/apollo-ui"
    )
    .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {}),
  "react-hook-form": "^7.44.2",
  "react-datepicker": "7.3.0",
  "react-multi-date-picker": "4.4.1",
  "react-transition-group": "4.4.5",
  notistack: "^3.0.1",
}

export function ComponentPreviewUI({
  code,
  scope,
}: {
  code: string
  scope?: any
}) {
  const queryParams =
    typeof window !== "undefined"
      ? new URLSearchParams(window.location.search)
      : new URLSearchParams()
  const mcpMode = queryParams.get("mcpMode")
  const isMcpMode = mcpMode === "llm"

  if (isMcpMode) {
    return <CodeBlock>{code}</CodeBlock>
  }

  return (
    <div id="component-preview-box">
      <Sandpack
        options={{
          showTabs: false,
          closableTabs: false,
          editorHeight: "500px",
        }}
        customSetup={{
          dependencies,
        }}
        files={{
          "styles.css": styles,
          "index.tsx": indexFile,
          "/App.tsx": code,
          // Config files
          "/node_modules/@apollo/ui/package.json": {
            hidden: true,
            code: JSON.stringify({
              name: "apollo-ui",
              main: "./index.js",
            }),
          },
          "/node_modules/@apollo/ui/index.js": {
            hidden: true,
            code: designSystemRaw,
          },
          "/node_modules/@design-systems/apollo-icons/package.json": {
            hidden: true,
            code: JSON.stringify({
              name: "icons",
              main: "./index.js",
            }),
          },
          "/node_modules/@design-systems/apollo-icons/index.js": {
            hidden: true,
            code: iconRaw,
          },
          "/node_modules/@apollo/token/package.json": {
            hidden: true,
            code: JSON.stringify({
              name: "tokens",
              main: "./index.js",
            }),
          },
          "/node_modules/@apollo/token/index.js": {
            hidden: true,
            code: tokenRaw,
          },
          "/node_modules/@design-systems/tokens/package.json": {
            hidden: true,
            code: JSON.stringify({
              name: "tokens",
              main: "./index.js",
            }),
          },
          "/node_modules/@design-systems/tokens/index.js": {
            hidden: true,
            code: legacyTokenRaw,
          },
        }}
        template="react-ts"
      />
    </div>
  )
}
