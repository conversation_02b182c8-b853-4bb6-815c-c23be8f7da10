import React, {
  PropsWithChildren,
  StrictMode,
  useEffect,
  useLayoutEffect,
  useState,
} from "react"
import { createRoot } from "react-dom/client"

import "./styles.css"

import { ThemeProvider, withApollo } from "@design-systems/apollo-ui"

// @ts-ignore
import App from "./App"

const root = createRoot(document.getElementById("root"))
root.render(
  <StrictMode>
    <TailwindConfigInjector>
      <ThemeProvider>
        <App />
      </ThemeProvider>
    </TailwindConfigInjector>
  </StrictMode>
)

const configs = withApollo({
  content: [],
})

function TailwindConfigInjector({ children }: PropsWithChildren) {
  const [isShowPreview, setIsShowPreview] = useState(false)
  const [isInitTailwind, setIsInitTailwind] = useState(false)

  useLayoutEffect(() => {
    if (!isInitTailwind) {
      const head = document.head

      // Inject Tailwind CDN script
      const baseScript = document.createElement("script")
      baseScript.src = "https://cdn.tailwindcss.com"

      // Create the config script that will be executed after Tailwind loads
      const configScript = document.createElement("script")
      configScript.id = "tailwind-config"
      configScript.innerHTML = `
        tailwind.config = { theme: ${JSON.stringify(configs.theme)}};`

      // Make sure config script runs after Tailwind is loaded
      baseScript.onload = () => {
        head.appendChild(configScript)
        setIsInitTailwind(true)
      }

      // Add the base script to start loading Tailwind
      head.appendChild(baseScript)
    }
  }, [isInitTailwind])

  useEffect(() => {
    if (isInitTailwind) {
      setTimeout(() => {
        setIsShowPreview(true)
      }, 1000)
    }
  }, [isInitTailwind])

  return isShowPreview ? children : null
}
