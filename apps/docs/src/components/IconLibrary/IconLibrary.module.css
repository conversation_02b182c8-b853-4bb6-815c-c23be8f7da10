.container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.searchContainer {
    margin-bottom: 24px;

    & input {
        color: #000 !important;
    }
}

.iconGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

@media (min-width: 640px) {
    .iconGrid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 768px) {
    .iconGrid {
        grid-template-columns: repeat(6, 1fr);
    }
}

.iconCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
}

.iconCard:hover {
    background-color: #333;
}

.iconDisplay {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
}

.iconName {
    margin-top: 8px;
    text-align: center;
}

.copyIndicator {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px;
    color: #10b981;
}

.emptyState {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
}

.totalCount {
    margin-top: 24px;
    text-align: right;
}