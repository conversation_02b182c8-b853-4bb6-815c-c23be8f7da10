"use client"

import React, { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { Input, ThemeProvider, Typography } from "@apollo/ui"
import { CheckCircle, icons, Search } from "@design-systems/apollo-icons"

import styles from "./IconLibrary.module.css"

export type IconLibraryProps = {}

export function IconLibrary() {
  const [searchTerm, setSearchTerm] = useState("")
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null)

  // Filter icons based on search term
  const filteredIcons = useMemo(() => {
    if (!searchTerm) return Object.entries(icons)

    return Object.entries(icons).filter(([name]) =>
      name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm])

  const handleCopy = useCallback((iconName: string) => {
    const importStatement = `<${iconName} />`
    navigator.clipboard.writeText(importStatement)

    setCopiedIcon(iconName)
    setTimeout(() => {
      setCopiedIcon(null)
    }, 2000)
  }, [])

  return (
    <ThemeProvider>
      <div className={styles.container}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search icons..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startDecorator={<Search />}
            fullWidth
          />
        </div>

        <div className={styles.iconGrid}>
          {filteredIcons.map(([iconName, Icon]) => (
            <div
              key={iconName}
              className={styles.iconCard}
              onClick={() => handleCopy(iconName)}
            >
              <div className={styles.iconDisplay}>
                {React.createElement(Icon as any, { size: 24 })}
              </div>
              <Typography level="caption" className={styles.iconName}>
                {iconName}
              </Typography>
              {copiedIcon === iconName && (
                <div className={styles.copyIndicator}>
                  <CheckCircle size={16} /> Copied
                </div>
              )}
            </div>
          ))}

          {filteredIcons.length === 0 && (
            <div className={styles.emptyState}>
              <Typography level="h4">No icons found</Typography>
              <Typography level="body1">
                Try adjusting your search query
              </Typography>
            </div>
          )}
        </div>

        <div className={styles.totalCount}>
          <Typography level="caption">
            Total: {filteredIcons.length} icon
            {filteredIcons.length !== 1 ? "s" : ""}
          </Typography>
        </div>
      </div>
    </ThemeProvider>
  )
}
