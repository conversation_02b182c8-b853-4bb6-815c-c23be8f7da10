.container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.searchContainer {
    margin-bottom: 24px;

    & input {
        color: #000 !important;
    }
}

.illustrationGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    background-color: white;
    border-radius: 16px;
    padding: 8px;
}

@media (min-width: 640px) {
    .illustrationGrid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 768px) {
    .illustrationGrid {
        grid-template-columns: repeat(6, 1fr);
    }
}

.illustrationCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
}

.illustrationCard:hover {
    background-color: #eee;
}

.illustrationDisplay {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 114px;
    height: 114px;
}

.illustrationName {
    margin-top: 8px;
    text-align: center;
    color: black;
    font-weight: bold;
}

.copyIndicator {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px;
    color: #10b981;
}

.emptyState {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
}

.totalCount {
    margin-top: 24px;
    text-align: right;
}