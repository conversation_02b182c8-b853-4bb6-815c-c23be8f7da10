"use client"

import React, { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { Input, ThemeProvider, Typography } from "@apollo/ui"
import { CheckCircle, Search } from "@design-systems/apollo-icons"
import { Illustration } from "@design-systems/apollo-ui"

import styles from "./IllustrationLibrary.module.css"

export type IllustrationLibraryProps = {}

export function IllustrationLibrary() {
  const [searchTerm, setSearchTerm] = useState("")
  const [copiedIllustration, setCopiedIllustration] = useState<string | null>(
    null
  )

  // Filter illustrations based on search term
  const filteredIllustrations = useMemo(() => {
    if (!searchTerm) return Object.entries(Illustration)

    return Object.entries(Illustration).filter(([name]) =>
      name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm])

  const handleCopy = useCallback((illustrationName: string) => {
    const importStatement = `<Illustration.${illustrationName} />`
    navigator.clipboard.writeText(importStatement)

    setCopiedIllustration(illustrationName)
    setTimeout(() => {
      setCopiedIllustration(null)
    }, 2000)
  }, [])

  return (
    <ThemeProvider>
      <div className={styles.container}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search illustrations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startDecorator={<Search />}
            fullWidth
          />
        </div>

        <div className={styles.illustrationGrid}>
          {filteredIllustrations.map(([illustrationName, Illustration]) => (
            <div
              key={illustrationName}
              className={styles.illustrationCard}
              onClick={() => handleCopy(illustrationName)}
            >
              <div className={styles.illustrationDisplay}>
                {React.createElement(Illustration as any, { size: 24 })}
              </div>
              <Typography level="caption" className={styles.illustrationName}>
                {illustrationName}
              </Typography>
              {copiedIllustration === illustrationName && (
                <div className={styles.copyIndicator}>
                  <CheckCircle size={16} /> Copied
                </div>
              )}
            </div>
          ))}

          {filteredIllustrations.length === 0 && (
            <div className={styles.emptyState}>
              <Typography level="h4">No illustrations found</Typography>
              <Typography level="body1">
                Try adjusting your search query
              </Typography>
            </div>
          )}
        </div>

        <div className={styles.totalCount}>
          <Typography level="caption">
            Total: {filteredIllustrations.length} illustration
            {filteredIllustrations.length !== 1 ? "s" : ""}
          </Typography>
        </div>
      </div>
    </ThemeProvider>
  )
}
