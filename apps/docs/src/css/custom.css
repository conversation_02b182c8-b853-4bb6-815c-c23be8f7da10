/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */
/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #7c84d4;
  --ifm-color-primary-dark: #6b73cc;
  --ifm-color-primary-darker: #5f67cb;
  --ifm-color-primary-darkest: #4e56c2;
  --ifm-color-primary-light: #8d94dc;
  --ifm-color-primary-lighter: #9ea3e4;
  --ifm-color-primary-lightest: #bfc2ec;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme="dark"] {
  --ifm-color-primary: #8e5db4;
  --ifm-color-primary-dark: #8050a8;
  --ifm-color-primary-darker: #7b4b9b;
  --ifm-color-primary-darkest: #6E3D8F;
  --ifm-color-primary-light: #9c6ac0;
  --ifm-color-primary-lighter: #aa77cc;
  --ifm-color-primary-lightest: #c699e5;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
  --ifm-navbar-background-color: rgb(27, 27, 29);
}