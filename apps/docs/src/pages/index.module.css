/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 2rem;
  }
}

[data-theme="dark"] {
  --hero-font-color: #fff;
}

.heroText {
  font-size: 5rem;
  font-weight: bold;
  color: var(--hero-font-color);
}
.descriptionText {
  font-size: 2rem;
  font-weight: 400;
  color: var(--hero-font-color);
}

.contentWrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}
