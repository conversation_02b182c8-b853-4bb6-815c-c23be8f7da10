import type { ReactNode } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ider, Typo<PERSON> } from "@apollo/ui"
import useDocusaurusContext from "@docusaurus/useDocusaurusContext"
import Layout from "@theme/Layout"

import styles from "./index.module.css"

export default function Home(): ReactNode {
  const { siteConfig } = useDocusaurusContext()
  return (
    <Layout
      title={`Hello from ${siteConfig.title}`}
      description="Description will go into a meta tag in <head />"
      noFooter
    >
      <main className={styles.contentWrapper}>
        <img
          src="/img/logo.svg"
          alt="Apollo UI Logo"
          className="w-24 h-24 mb-4"
        />
        <div className={styles.heroText}>Elevate Your UI with Ease!</div>
        <div className={styles.descriptionText}>
          Accessible, Comprehensive, Effortless Design ✨
        </div>
        <br />
        <ThemeProvider>
          <Typography level="h3">
            <a href="/docs/intro">Go to Document 👉</a>
          </Typography>
        </ThemeProvider>
      </main>
    </Layout>
  )
}
