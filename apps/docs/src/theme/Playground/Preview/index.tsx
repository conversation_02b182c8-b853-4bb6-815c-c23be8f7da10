import React, { useEffect, useState, type ReactNode } from "react"
import * as LegacyApolloUI from "@design-systems/apollo-ui"
import { cssString } from "@design-systems/apollo-ui/theme"
import BrowserOnly from "@docusaurus/BrowserOnly"
import ErrorBoundary from "@docusaurus/ErrorBoundary"
import { ErrorBoundaryErrorMessageFallback } from "@docusaurus/theme-common"
import Translate from "@docusaurus/Translate"
import PlaygroundHeader from "@theme/Playground/Header"
import { LiveError, LivePreview } from "react-live"

import styles from "./styles.module.css"

function Loader() {
  // Is it worth improving/translating?
  // eslint-disable-next-line @docusaurus/no-untranslated-text
  return <div>Loading...</div>
}

function PlaygroundLivePreview(): ReactNode {
  // No SSR for the live preview
  // See https://github.com/facebook/docusaurus/issues/5747
  return (
    <BrowserOnly fallback={<Loader />}>
      {() => (
        <>
          <ErrorBoundary
            fallback={(params) => (
              <ErrorBoundaryErrorMessageFallback {...params} />
            )}
          >
            <LivePreview />
          </ErrorBoundary>
          <LiveError />
        </>
      )}
    </BrowserOnly>
  )
}

export default function PlaygroundPreview(): ReactNode {
  return (
    <>
      <PlaygroundHeader>
        <Translate
          id="theme.Playground.result"
          description="The result label of the live codeblocks"
        >
          Result
        </Translate>
      </PlaygroundHeader>
      <div className={styles.playgroundPreview}>
        <ShadowDomPreview>
          <PlaygroundLivePreview />
        </ShadowDomPreview>
      </div>
    </>
  )
}

function ShadowDomPreview({ children }: { children: React.ReactNode }) {
  const [ref, setRef] = useState<HTMLElement>()
  const [hostRef, setHostRef] = useState<HTMLElement>()

  useEffect(() => {
    // Check if `nested-identifier` is present in the DOM at the same level under the shadow DOM attached element into the top host element instead
    // of the shadow DOM. FYI: only components that are not using the `ThemeProvider` will be rendered in the shadow DOM.
    const nestedIdentifier = document.getElementById("nested-identifier")
    if (nestedIdentifier) {
      const host = document.getElementById("preview-host")
      if (host) {
        host.appendChild(nestedIdentifier)
      }
    }

    if (ref && hostRef) {
      const host = hostRef
      const shadow = host.attachShadow({ mode: "open" })
      shadow.appendChild(ref)
    }
  }, [ref, hostRef])

  return (
    <LegacyApolloUI.ThemeProvider scope="preview">
      <div
        id="preview-host"
        className={styles.shadowPreviewContainer}
        ref={setHostRef}
      >
        <div ref={setRef} className={styles.shadowPreviewContainer}>
          <style
            dangerouslySetInnerHTML={{
              __html: cssString,
            }}
          />
          {children}
        </div>
      </div>
    </LegacyApolloUI.ThemeProvider>
  )
}
