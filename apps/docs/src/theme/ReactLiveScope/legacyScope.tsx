import React from "react"
import * as LegacyApolloUI from "@design-systems/apollo-ui"
import theme from "@design-systems/apollo-ui/theme.css"

console.log("theme", theme)

// Add react-live imports you need here
const LegacyReactLiveScope: Record<string, unknown> = {
  React,
  ...React,
  ...Object.entries(LegacyApolloUI).reduce(
    (acc, [key, Component]) => ({
      ...acc,
      [key]: (props) => (
        <LegacyApolloUI.ThemeProvider>
          <style
            id="Test"
            dangerouslySetInnerHTML={{
              __html: theme,
            }}
          />
          <Component {...props} />
        </LegacyApolloUI.ThemeProvider>
      ),
    }),
    {}
  ),
}

export default LegacyReactLiveScope
