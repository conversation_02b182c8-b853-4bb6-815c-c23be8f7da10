# Transition Strategy for Apollo Design System Migration

## Overview

This document outlines the strategy for migrating from the legacy design system package (`@design-systems/apollo-ui`) to the new package (`@apollo/ui`).

### **Current State**

1. `@design-systems/apollo-ui` (Legacy Package)

   - Heavily used in the FE codebase.
   - Built with deprecated libraries, making maintenance difficult.
   - Uses a different prop structure compared to the new package.

2. `@apollo/ui` (New Package)

   - Improved version with better architecture and maintainability.
   - Not yet adopted in the FE codebase.
   - Prop structure differs from the legacy package.

## **Migration Approach**

The goal is to transition to `@apollo/ui` with minimal disruption to the existing FE implementation.

### **1. Wrapping the New Components in an Adapter Layer**

- The legacy package will internally use `@apollo/ui`, with an adapter that maps legacy props to the new prop structure.
- FE code continues using `@design-systems/apollo-ui` without modification.

#### **Example: Legacy Component Using New Component**

```tsx
import { NewButton } from "@apollo/ui"

type LegacyButtonProps = {
  type?: "primary" | "secondary"
  isDisabled?: boolean
}

const LegacyButton = ({ type, isDisabled, ...rest }: LegacyButtonProps) => {
  const mappedProps = transformProps({ type, isDisabled })
  return <NewButton {...mappedProps} {...rest} />
}

function transformProps({ type, isDisabled }: LegacyButtonProps) {
  return {
    variant: type, // Mapping legacy "type" to new "variant"
    disabled: isDisabled,
  }
}

export default LegacyButton
```

### **2. Progressive Migration Strategy**

- The FE code will continue using the legacy package while benefiting from the new implementation.
- Developers can gradually migrate to `@apollo/ui` directly without breaking existing functionality.

### **3. Deprecation Warnings**

To encourage developers to transition, we will add console warnings when using legacy components in development mode.

```tsx
if (process.env.NODE_ENV === "development") {
  console.warn(
    "LegacyButton is deprecated. Please use NewButton from @apollo/ui."
  )
}
```

### **4. Performance Considerations**

- The adapter layer may introduce minimal performance overhead.
- If performance issues arise, consider optimizing mappings or rewriting high-usage components first.
- Offer a codemod script to automate FE migration if necessary.

## **Alternative Approach**

If the FE team is open to a more direct migration:

- Provide a **detailed migration guide** with side-by-side component and prop comparisons.
- Implement **codemods** to automate prop conversion for large-scale migrations.

## **Summary**

✅ **Adapter Layer** ensures seamless transition without breaking existing FE code.
✅ **Progressive Migration** allows teams to move at their own pace.
✅ **Deprecation Warnings** guide developers toward direct adoption of `@apollo/ui`.
✅ **Performance Optimizations** keep overhead minimal.
✅ **Codemod Option** available for large-scale migrations.

This strategy balances stability and future maintainability, ensuring a smooth transition to `@apollo/ui`. 🚀

---

# 🚀 Apollo Design System Migration Plan

## **1. Ensure Component Parity**

- Implement all components in `@apollo/ui` that exist in `@design-systems/apollo-ui`.
- Ensure functionality matches before replacing legacy components.

## **2. Build a Code Scanner**

- Scan repositories for:
  - Component usage frequency (identify most-used components).
  - Prop structures and variations (for adapter logic).
  - Custom wrappers around legacy components.

## **3. Create an Adapter Layer (Temporary Compatibility Mode)**

- Wrap `@apollo/ui` components inside `@design-systems/apollo-ui`.
- Transform legacy props to match the new API.

## **4. Automate Migration with Codemods**

- Write codemod scripts to:
  - Convert imports from `@design-systems/apollo-ui` → `@apollo/ui`.
  - Update prop names and structures.

## **5. Provide Developer Support & Documentation**

- Create a **Migration Guide** (step-by-step with examples).
- Provide **Prop Mapping Tables** for easy reference.
- Maintain a **Deprecation Roadmap** with key deadlines.

## **6. Implement Developer Experience Enhancements**

- Add lint rules to warn about outdated components.
- Implement runtime warnings for deprecated props.

## **7. Enforce Maintainability Standards**

- Establish CI checks to prevent legacy imports.
- Set versioning rules to avoid breaking changes.
- Collect feedback from FE teams to ensure smooth adoption.

---

This plan ensures a **structured, low-friction migration** while maintaining FE stability and reducing maintenance overhead. 🚀
