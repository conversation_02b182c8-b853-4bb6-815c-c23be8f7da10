{"name": "labs", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev --turbopack -p 3030", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/token": "workspace:*", "@apollo/ui": "workspace:*", "@design-systems/apollo-icons": "workspace:*", "@design-systems/apollo-ui": "workspace:*", "@hookform/resolvers": "^3.1.0", "@tailwindcss/postcss": "^4.0.0", "next": "15.1.6", "postcss": "^8.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.44.2", "tailwindcss": "^4.0.0", "zod": "^3.21.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "typescript": "^5"}}