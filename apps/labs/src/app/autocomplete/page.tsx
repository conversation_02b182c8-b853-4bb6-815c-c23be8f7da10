"use client"

import { Autocomplete } from "@apollo/ui"

export default function Page() {
  return (
    <div className="p-6 space-y-4 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Registration Form</h1>

      <Autocomplete
        label="Country"
        options={[
          { label: "United States", value: "us" },
          { label: "Canada", value: "ca" },
          { label: "United Kingdom", value: "uk" },
          { label: "Australia", value: "au" },
        ]}
        placeholder="Select your country"
      />

      <Autocomplete
        label="Language"
        multiple
        options={[
          { label: "English", value: "en" },
          { label: "Spanish", value: "es" },
          { label: "French", value: "fr" },
          { label: "German", value: "de" },
          { label: "Mandarin", value: "zh" },
        ]}
        placeholder="Select languages"
      />

      <Autocomplete
        label="Department"
        options={[
          { label: "Engineering", value: "eng" },
          { label: "Marketing", value: "mkt" },
          { label: "Sales", value: "sales" },
          { label: "HR", value: "hr" },
          { label: "Finance", value: "fin" },
        ]}
        placeholder="Select department"
      />

      <Autocomplete
        label="Skills"
        multiple
        options={[
          { label: "JavaScript", value: "js" },
          { label: "Python", value: "py" },
          { label: "React", value: "react" },
          { label: "Node.js", value: "node" },
          { label: "TypeScript", value: "ts" },
        ]}
        placeholder="Select skills"
      />

      <Autocomplete
        label="Office Location"
        options={[
          { label: "New York", value: "ny" },
          { label: "San Francisco", value: "sf" },
          { label: "London", value: "ldn" },
          { label: "Tokyo", value: "tokyo" },
          { label: "Singapore", value: "sg" },
        ]}
        placeholder="Select office"
      />

      <Autocomplete
        label="Project"
        multiple
        options={[
          { label: "Apollo", value: "apollo" },
          { label: "Artemis", value: "artemis" },
          { label: "Phoenix", value: "phoenix" },
          { label: "Titan", value: "titan" },
          { label: "Atlas", value: "atlas" },
        ]}
        placeholder="Select projects"
      />

      <Autocomplete
        label="Role"
        options={[
          { label: "Developer", value: "dev" },
          { label: "Designer", value: "des" },
          { label: "Manager", value: "mgr" },
          { label: "Analyst", value: "analyst" },
          { label: "Architect", value: "arch" },
        ]}
        placeholder="Select role"
      />

      <Autocomplete
        label="Time Zone"
        options={[
          { label: "UTC-8", value: "utc-8" },
          { label: "UTC-5", value: "utc-5" },
          { label: "UTC+0", value: "utc+0" },
          { label: "UTC+8", value: "utc+8" },
          { label: "UTC+9", value: "utc+9" },
        ]}
        placeholder="Select time zone"
      />

      <Autocomplete
        label="Team"
        multiple
        options={[
          { label: "Frontend", value: "frontend" },
          { label: "Backend", value: "backend" },
          { label: "DevOps", value: "devops" },
          { label: "QA", value: "qa" },
          { label: "UI/UX", value: "uiux" },
        ]}
        placeholder="Select teams"
      />

      <Autocomplete
        label="Software"
        multiple
        options={[
          { label: "VS Code", value: "vscode" },
          { label: "Figma", value: "figma" },
          { label: "Jira", value: "jira" },
          { label: "Git", value: "git" },
          { label: "Docker", value: "docker" },
        ]}
        placeholder="Select software"
      />

      <Autocomplete
        label="Certification"
        multiple
        options={[
          { label: "AWS", value: "aws" },
          { label: "Azure", value: "azure" },
          { label: "GCP", value: "gcp" },
          { label: "CISSP", value: "cissp" },
          { label: "PMP", value: "pmp" },
        ]}
        placeholder="Select certifications"
      />

      <Autocomplete
        label="Industry"
        options={[
          { label: "Technology", value: "tech" },
          { label: "Healthcare", value: "health" },
          { label: "Finance", value: "finance" },
          { label: "Education", value: "edu" },
          { label: "Retail", value: "retail" },
        ]}
        placeholder="Select industry"
      />

      <Autocomplete
        label="Experience Level"
        options={[
          { label: "Entry", value: "entry" },
          { label: "Junior", value: "junior" },
          { label: "Mid", value: "mid" },
          { label: "Senior", value: "senior" },
          { label: "Lead", value: "lead" },
        ]}
        placeholder="Select level"
      />

      <Autocomplete
        label="Education"
        options={[
          { label: "High School", value: "hs" },
          { label: "Bachelor's", value: "bachelor" },
          { label: "Master's", value: "master" },
          { label: "PhD", value: "phd" },
          { label: "Other", value: "other" },
        ]}
        placeholder="Select education"
      />

      <Autocomplete
        label="Work Model"
        options={[
          { label: "Remote", value: "remote" },
          { label: "Hybrid", value: "hybrid" },
          { label: "On-site", value: "onsite" },
        ]}
        placeholder="Select work model"
      />

      <Autocomplete
        label="Benefits"
        multiple
        options={[
          { label: "Health Insurance", value: "health" },
          { label: "401k", value: "401k" },
          { label: "Stock Options", value: "stock" },
          { label: "Gym", value: "gym" },
          { label: "Food", value: "food" },
        ]}
        placeholder="Select benefits"
      />

      <Autocomplete
        label="Contract Type"
        options={[
          { label: "Full-time", value: "fulltime" },
          { label: "Part-time", value: "parttime" },
          { label: "Contract", value: "contract" },
          { label: "Internship", value: "internship" },
        ]}
        placeholder="Select contract type"
      />

      <Autocomplete
        label="Training"
        multiple
        options={[
          { label: "Leadership", value: "leadership" },
          { label: "Technical", value: "technical" },
          { label: "Soft Skills", value: "softskills" },
          { label: "Safety", value: "safety" },
          { label: "Compliance", value: "compliance" },
        ]}
        placeholder="Select training"
      />

      <Autocomplete
        label="Equipment"
        multiple
        options={[
          { label: "Laptop", value: "laptop" },
          { label: "Monitor", value: "monitor" },
          { label: "Keyboard", value: "keyboard" },
          { label: "Mouse", value: "mouse" },
          { label: "Headset", value: "headset" },
        ]}
        placeholder="Select equipment"
      />

      <Autocomplete
        label="Reporting To"
        options={[
          { label: "CTO", value: "cto" },
          { label: "Engineering Manager", value: "engmgr" },
          { label: "Product Manager", value: "pm" },
          { label: "Team Lead", value: "tl" },
        ]}
        placeholder="Select reporting manager"
      />
    </div>
  )
}
