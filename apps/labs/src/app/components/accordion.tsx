import { useState } from "react"
import { Accordion, Button, Typography } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const AccordionDemo = () => {
  const [controlledOpen, setControlledOpen] = useState(false)

  return (
    <>
      <ComponentGroup>
        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Default Accordion
          </Typography>
          <Accordion label="Accordion Title">Accordion content</Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Open by Default
          </Typography>
          <Accordion defaultOpen label="Accordion Title">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Controlled Accordion
          </Typography>
          <div className="mb-2">
            <Button onClick={() => setControlledOpen(!controlledOpen)}>
              {controlledOpen ? "Close Accordion" : "Open Accordion"}
            </Button>
          </div>
          <Accordion
            open={controlledOpen}
            onOpenChange={setControlledOpen}
            label="Controlled Accordion"
          >
            This accordion is controlled externally
          </Accordion>

          <Typography level="h3" className="mb-4">
            Disabled Accordion
          </Typography>
          <Accordion label="Accordion Title" open disabled>Accordion content</Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Default Accordion with Divider
          </Typography>
          <Accordion label="Accordion Title" hasDivider>Accordion content</Accordion>

          <Typography level="h3" className="mb-4">
            Error Accordion with Divider
          </Typography>
          <Accordion label="Accordion Title" variant="danger" hasDivider>Accordion content</Accordion>

          <Typography level="h3" className="mb-4">
            Disabled Accordion with Divider
          </Typography>
          <Accordion label="Accordion Title" open disabled hasDivider>Accordion content</Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Icon at End (Default)
          </Typography>
          <Accordion iconPosition="end" label="Icon at End">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Icon at Start
          </Typography>
          <Accordion iconPosition="start" label="Icon at Start">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Icons at Both Ends
          </Typography>
          <Accordion iconPosition="both" label="Icons at Both Ends">
            Accordion content
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Default Variant
          </Typography>
          <Accordion variant="default" label="Default Variant">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Error Variant
          </Typography>
          <Accordion variant="danger" label="Error Variant">
            Accordion content
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Default Icon
          </Typography>
          <Accordion iconVariant="default" label="Default Icon">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Primary Icon
          </Typography>
          <Accordion iconVariant="primary" label="Primary Icon">
            Accordion content
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            With Border (Default)
          </Typography>
          <Accordion borderless={false} label="With Border">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Borderless
          </Typography>
          <Accordion borderless label="Borderless">
            Accordion content
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            Default Width
          </Typography>
          <Accordion fullWidth={false} label="Default Width">
            Accordion content
          </Accordion>

          <Typography level="h3" className="mt-6 mb-4">
            Full Width
          </Typography>
          <Accordion fullWidth label="Full Width">
            Accordion content fits the full width
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          <Typography level="h3" className="mb-4">
            With keepMounted (Content stays in DOM)
          </Typography>
          <Accordion keepMounted label="Keep Mounted Example">
            This content remains in the DOM even when collapsed
          </Accordion>
        </ComponentBox>

        <ComponentBox>
          {/* Keeping the existing complex examples */}
          <Accordion fullWidth open label="Accordion Title">
            Accordion header
          </Accordion>
          <Accordion
            defaultOpen
            borderless
            variant="danger"
            className="w-[200px]"
            fullWidth
            iconPosition="start"
            iconVariant="primary"
            label={
              <div className="flex flex-row items-center justify-between">
                <Typography level="caption" className="max-w-[100px]">
                  011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
                </Typography>
                <div className="flex flex-row items-center justify-end gap-2">
                  <Typography level="caption">เปิดทำการ</Typography>
                  <Button
                    onClick={(e) => e.stopPropagation()}
                    variant="outline"
                    size="small"
                  >
                    เพิ่ม
                  </Button>
                </div>
              </div>
            }
          >
            <div className="flex flex-col">
              <span>Model: CJ SUPERMARKET + BAO CAFÉ&WASH </span>
              <span>Open date: 30/08/2004 Close</span>
              <span>date: 30/08/2004</span>
            </div>
          </Accordion>
          <Accordion
            defaultOpen
            borderless
            className="w-[200px]"
            fullWidth
            iconPosition="start"
            iconVariant="primary"
            label={
              <div className="flex flex-row items-center justify-between">
                <Typography level="caption" className="max-w-[100px]">
                  011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
                </Typography>
                <div className="flex flex-row items-center justify-end gap-2">
                  <Typography level="caption">เปิดทำการ</Typography>
                  <Button
                    onClick={(e) => e.stopPropagation()}
                    variant="outline"
                    size="small"
                  >
                    เพิ่ม
                  </Button>
                </div>
              </div>
            }
          >
            <div className="flex flex-col">
              <span>Model: CJ SUPERMARKET + BAO CAFÉ&WASH </span>
              <span>Open date: 30/08/2004 Close</span>
              <span>date: 30/08/2004</span>
            </div>
          </Accordion>
          <Accordion keepMounted variant="danger" label="Accordion header">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Accordion>
          <Accordion defaultOpen variant="danger" label="Accordion header">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
            malesuada lacus ex, sit amet blandit leo lobortis eget.
          </Accordion>
        </ComponentBox>
      </ComponentGroup>
    </>
  )
}
