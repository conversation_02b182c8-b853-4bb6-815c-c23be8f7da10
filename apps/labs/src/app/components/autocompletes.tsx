import { useCallback, useState } from "react"
import { Autocomplete } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

// Example data
const countries = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK" },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE", disabled: true },
  { label: "Italy", value: "IT" },
  { label: "Spain", value: "ES" },
  { label: "Canada", value: "CA" },
  { label: "Australia", value: "AU" },
  { label: "Japan", value: "JP" },
  { label: "China", value: "CN" },
]

const users = [
  {
    label: "<PERSON>",
    value: { id: 1, name: "<PERSON>", email: "<EMAIL>" },
  },
  {
    label: "<PERSON>",
    value: { id: 2, name: "<PERSON>", email: "<EMAIL>" },
  },
]

const longCountryList = [
  { label: "United States of America", value: "USA" },
  { label: "United Kingdom of Great Britain", value: "GBR" },
  { label: "French Republic", value: "FRA" },
  { label: "Federal Republic of Germany", value: "DEU" },
  { label: "Kingdom of Spain", value: "ESP" },
  // Add more longer country names
]

const countriesWithDisabled = [
  { label: "United States", value: "US" },
  { label: "United Kingdom", value: "UK", disabled: true },
  { label: "France", value: "FR" },
  { label: "Germany", value: "DE", disabled: true },
  { label: "Italy", value: "IT" },
  // ...existing countries...
]

// Delay utility
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export function AutocompletesExample() {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <SelectAllExample />
        <DisabledOptionsExample />
        <CustomNoOptionsExample />
        <BasicSmallSingleSelect />
        <BasicSingleSelect />
        <StyledSingleSelect />
        <AccessibleAutocomplete />
        <DisabledSearchSelect />
        <AsyncSearchSelect />
        <DisabledSearchSelect />
        <InfiniteScrollSingleSelect />
        <CustomLoadingExample />
        <BasicMultipleSelect />
        <BasicSmallMultipleSelect />
        <CustomFilterExample />
        <InfiniteScrollMultipleSelect />
        <PersistedSelectionExample />
        <ComplexDataExample />
      </ComponentBox>
    </ComponentGroup>
  )
}

// Example Components
function BasicSmallSingleSelect() {
  const [value, setValue] = useState<string>()
  return (
    <ComponentBox>
      <h3> Basic Single Select</h3>
      <Autocomplete
        size="small"
        label="Test"
        placeholder="Search"
        options={countries}
        value={value}
        onFocus={() => console.log("Focused")}
        onBlur={() => console.log("Blur")}
        helperText="lorem ipsum"
        onChange={(value) => setValue(value)}
      />
    </ComponentBox>
  )
}

function BasicSingleSelect() {
  const [value, setValue] = useState<string>()
  return (
    <ComponentBox>
      <h3> Basic Single Select</h3>
      <Autocomplete
        options={countries}
        value={value}
        fullWidth
        onChange={(value) => setValue(value)}
        menuLabelText="Country"
      />
    </ComponentBox>
  )
}

function StyledSingleSelect() {
  const [value, setValue] = useState<string>()
  return (
    <ComponentBox>
      <h3> Single Select with Label & Helper Text</h3>
      <Autocomplete
        options={countries}
        value={value}
        onChange={(value) => setValue(value)}
        label="Select Country"
        helperText="Please select your country"
        fullWidth
      />
    </ComponentBox>
  )
}

function DisabledSearchSelect() {
  const [value, setValue] = useState<string>()
  return (
    <ComponentBox>
      <h3> Single Select with Disabled Search</h3>
      <Autocomplete
        options={countries}
        value={value}
        onChange={setValue}
        disableSearch
      />
    </ComponentBox>
  )
}

function AsyncSearchSelect() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(countries)
  const [isLoading, setIsLoading] = useState(false)

  const handleSearch = useCallback(async (search: string) => {
    setIsLoading(true)
    try {
      await delay(3000)
      setOptions(
        countries.filter((country) =>
          country.label.toLowerCase().includes(search.toLowerCase())
        )
      )
    } finally {
      setIsLoading(false)
    }
  }, [])

  return (
    <ComponentBox>
      <h3> Single Select with Async Search</h3>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        loading={isLoading}
        debounceMs={300}
      />
    </ComponentBox>
  )
}

function BasicSmallMultipleSelect() {
  const [value, setValue] = useState<string[]>()
  return (
    <ComponentBox>
      <h3> Basic Small Multiple Select</h3>
      <Autocomplete
        multiple
        size="small"
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
      />
      <Autocomplete
        multiple
        size="small"
        disabled
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
      />
      <Autocomplete
        multiple
        size="small"
        required
        error
        helperText="error message"
        helperTextDecorator={<InfoCircle size={12} />}
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
      />
    </ComponentBox>
  )
}
function BasicMultipleSelect() {
  const [value, setValue] = useState<string[]>(["US", "UK"])
  return (
    <ComponentBox>
      <h3> Basic Multiple Select</h3>
      <Autocomplete
        multiple
        onFocus={() => console.log("Focused")}
        onBlur={() => console.log("Blur")}
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
      />
      <Autocomplete
        multiple
        disabled
        label="Select Country"
        helperText="Select multiple countries"
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
        menuLabelText="Country"
      />
      <Autocomplete
        multiple
        error
        helperText="error message"
        label="Select Country"
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
      />
    </ComponentBox>
  )
}

function SelectAllExample() {
  const [value, setValue] = useState<string[]>()
  return (
    <ComponentBox>
      <h3> Multiple Select with Select All</h3>
      <Autocomplete
        multiple
        options={countries}
        value={value}
        fullWidth
        onChange={(values) => setValue(values)}
        showSelectAll
        selectAllText="Select All Countries"
        // hideCheckbox
      />
    </ComponentBox>
  )
}

function CustomFilterExample() {
  const [value, setValue] = useState<string[]>()
  return (
    <ComponentBox>
      <h3> Multiple Select with Custom Filter</h3>
      <Autocomplete
        multiple
        options={countries}
        value={value}
        onChange={(values) => setValue(values)}
        filterLogic={(options, search) =>
          options.filter((option) =>
            option.label.toLowerCase().startsWith(search.toLowerCase())
          )
        }
      />
    </ComponentBox>
  )
}

function ComplexDataExample() {
  const [value, setValue] = useState<{
    id: number
    name: string
    email: string
  }>()

  return (
    <ComponentBox>
      <h3> Complex Data Structure Example</h3>
      <Autocomplete
        options={users}
        value={value}
        onChange={(value) => setValue(value)}
        label="Select User"
        labelDecorator={<InfoCircle size={12} />}
        helperText="Select a user from the list"
        helperTextDecorator={<InfoCircle size={12} />}
      />
    </ComponentBox>
  )
}

function PersistedSelectionExample() {
  const [value, setValue] = useState<string[]>()
  const [options, setOptions] = useState(longCountryList)
  const [isLoading, setIsLoading] = useState(false)

  const handleSearch = useCallback(async (search: string) => {
    setIsLoading(true)
    try {
      await delay(1000)
      const filtered = search
        ? longCountryList.filter((country) =>
            country.label.toLowerCase().includes(search.toLowerCase())
          )
        : longCountryList
      setOptions(filtered.slice(0, 2))
    } finally {
      setIsLoading(false)
    }
  }, [])

  return (
    <ComponentBox>
      <h3>Persisted Selection Example</h3>
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <p>Selected values persist even when filtered out of search results</p>
        <Autocomplete
          multiple
          options={options}
          value={value}
          onChange={(values) => setValue(values)}
          onSearch={handleSearch}
          loading={isLoading}
          debounceMs={300}
          label="Search Countries"
          helperText="Try searching after selecting some options"
        />
        <p>Selected values: {value?.join(", ")}</p>
      </div>
    </ComponentBox>
  )
}

function InfiniteScrollSingleSelect() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(countries.slice(0, 3))
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const handleLoadMore = useCallback(async () => {
    setIsLoading(true)
    try {
      await delay(1000)
      const currentLength = options.length
      const nextBatch = countries.slice(currentLength, currentLength + 3)
      setOptions([...options, ...nextBatch])
      setHasMore(currentLength + 3 < countries.length)
    } finally {
      setIsLoading(false)
    }
  }, [options])

  return (
    <ComponentBox>
      <h3>Single Select with Infinite Scroll</h3>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onLoadMore={handleLoadMore}
        loadingMore={isLoading}
        hasMore={hasMore}
        loadMoreLabel={
          <div className="flex items-center gap-2">
            <span>Loading more countries</span>
            <span>🌍</span>
          </div>
        }
        menuLabelText="Select Country"
      />
    </ComponentBox>
  )
}

function InfiniteScrollMultipleSelect() {
  const [value, setValue] = useState<string[]>()
  const [options, setOptions] = useState(countries.slice(0, 3))
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const handleLoadMore = useCallback(async () => {
    setIsLoading(true)
    try {
      await delay(1000)
      const currentLength = options.length
      const nextBatch = countries.slice(currentLength, currentLength + 3)
      setOptions([...options, ...nextBatch])
      setHasMore(currentLength + 3 < countries.length)
    } finally {
      setIsLoading(false)
    }
  }, [options])

  return (
    <ComponentBox>
      <h3>Multiple Select with Infinite Scroll</h3>
      <Autocomplete
        multiple
        options={options}
        value={value}
        onChange={(value) => setValue(value)}
        onLoadMore={handleLoadMore}
        loadingMore={isLoading}
        hasMore={hasMore}
        loadMoreLabel="Loading more options..."
        showSelectAll
      />
    </ComponentBox>
  )
}

function CustomLoadingExample() {
  const [value, setValue] = useState<string>()
  const [isLoading, setIsLoading] = useState(false)

  const handleSearch = useCallback(async () => {
    setIsLoading(true)
    try {
      await delay(2000)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return (
    <ComponentBox>
      <h3>Custom Loading Component</h3>
      <Autocomplete
        options={countries}
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        hasMore
        loadingMore
        loading={isLoading}
        loadingComponent={
          <div className="flex flex-col items-center gap-2 p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p>Searching countries...</p>
          </div>
        }
      />
    </ComponentBox>
  )
}

function CustomNoOptionsExample() {
  const [value, setValue] = useState<string>()
  const [options, setOptions] = useState(countries)

  const handleSearch = useCallback(async () => {
    setOptions([]) // Force no options to show custom component
  }, [])

  return (
    <ComponentBox>
      <h3>Custom No Options Component</h3>
      <Autocomplete
        options={options}
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        helperText="Search for a country"
        noOptionsComponent={
          <div className="flex flex-col items-center gap-2 p-4">
            <p className="text-gray-500">No matching countries found</p>
            <button className="text-primary">Create new?</button>
          </div>
        }
      />
    </ComponentBox>
  )
}

function DisabledOptionsExample() {
  const [value, setValue] = useState<string>()

  return (
    <ComponentBox>
      <h3>Autocomplete with Disabled Options</h3>
      <Autocomplete
        options={countriesWithDisabled}
        value={value}
        onChange={setValue}
      />
    </ComponentBox>
  )
}

function AccessibleAutocomplete() {
  const [value, setValue] = useState<string>()

  return (
    <ComponentBox>
      <h3>Accessible Autocomplete</h3>
      <Autocomplete
        id="country-select"
        name="country"
        label="Country"
        aria-label="Select a country"
        aria-describedby="country-select-description"
        className="my-autocomplete"
        style={{ maxWidth: "300px" }}
        placeholder="Select a country"
        options={countries}
        value={value}
        onChange={(value) => setValue(value)}
        inputProps={{
          maxLength: 100,
        }}
        menuProps={{
          "aria-label": "Country options",
          className: "custom-menu-class",
        }}
      />
      <small id="country-select-description">
        Select the country you currently reside in
      </small>
    </ComponentBox>
  )
}
