import React from "react"
import { Badge } from "@apollo/ui"
import { ClockCircle } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

const Badges = () => {
  return (
    <ComponentGroup>
      <ComponentBox>
        <Badge label="Default" />
        <Badge label="Process" color="process" />
        <Badge label="Active" color="process" icon={<ClockCircle />} />
        <Badge label="Success" color="success" />
        <Badge label="Warning" color="warning" />
        <Badge label="Error" color="error" />
        <div className="!mt-4">
        <Badge label="25" color="error">
          <div className="w-[40px] h-[40px] bg-gray-500 rounded-[4px]"/>
        </Badge>
        </div>
      </ComponentBox>
    </ComponentGroup>
  )
}

export default Badges
