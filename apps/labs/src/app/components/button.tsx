import { <PERSON><PERSON> } from "@apollo/ui"
import { DeleteOutlined, <PERSON> } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

export const Buttons = () => (
  <>
    <ComponentGroup>
      <ComponentBox>
        <Button>Primary</Button>
        <Button startDecorator={<Smile size={16} />}>Primary</Button>
        <Button endDecorator={<Smile size={16} />}>Primary</Button>
        <Button endDecorator={<Smile size={16} />} disabled>
          Primary
        </Button>
        <Button disabled>Primary Disabled</Button>
      </ComponentBox>
      <ComponentBox>
        <Button variant="outline">Outline</Button>
        <Button startDecorator={<Smile size={16} />} variant="outline">
          Outline
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="outline">
          Outline
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="outline" disabled>
          Outline
        </Button>
        <Button variant="outline" disabled>
          Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button variant="text">Text</Button>
        <Button startDecorator={<Smile size={16} />} variant="text">
          Text
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="text">
          Text
        </Button>
        <Button variant="text" disabled>
          Text Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small">Small</Button>
        <Button startDecorator={<Smile size={14} />} size="small">
          Small
        </Button>
        <Button endDecorator={<Smile size={14} />} size="small">
          Small
        </Button>
        <Button size="small" disabled>
          Small Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small" variant="outline">
          Small Outline
        </Button>
        <Button size="small" variant="outline" disabled>
          Small Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small" variant="text">
          Small Text
        </Button>
        <Button size="small" variant="text" disabled>
          Small Text Disabled
        </Button>
      </ComponentBox>
      <Button fullWidth>Full Width</Button>
      <Button fullWidth size="small">
        Full Width Small
      </Button>
    </ComponentGroup>
    <ComponentGroup>
      <ComponentBox>
        <Button color="negative">Negative</Button>
        <Button startDecorator={<DeleteOutlined size={16} />} color="negative">
          Negative
        </Button>
        <Button endDecorator={<DeleteOutlined size={16} />} color="negative">
          Negative
        </Button>
        <Button color="negative" disabled>
          Negative Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" variant="outline">
          Negative Outline
        </Button>
        <Button
          startDecorator={<DeleteOutlined size={16} />}
          color="negative"
          variant="outline"
        >
          Negative Outline
        </Button>
        <Button
          endDecorator={<DeleteOutlined size={16} />}
          color="negative"
          variant="outline"
        >
          Negative Outline
        </Button>
        <Button color="negative" variant="outline" disabled>
          Negative Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" variant="text">
          Negative Text
        </Button>
        <Button color="negative" variant="text" disabled>
          Negative Text Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" size="small">
          Negative Small
        </Button>
        <Button color="negative" size="small" disabled>
          Negative Small Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" size="small" variant="outline">
          Negative Small Outline
        </Button>
        <Button color="negative" size="small" variant="outline" disabled>
          Negative Small Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" size="small" variant="text">
          Negative Small Text
        </Button>
        <Button color="negative" size="small" variant="text" disabled>
          Negative Small Text Disabled
        </Button>
      </ComponentBox>
      <Button color="negative" fullWidth>
        Negative Full Width
      </Button>
      <Button color="negative" fullWidth size="small">
        Negative Full Width Small
      </Button>
    </ComponentGroup>
    <ComponentGroup>
      <ComponentBox>
        <Button startDecorator={<Smile size={16} />} href="https://google.com">
          Link Primary
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" variant="outline">
          Link Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" variant="text">
          Link Text
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small">
          Link Small
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small" variant="outline">
          Link Small Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small" variant="text">
          Link Small Text
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" href="https://google.com">
          Link Negative
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" href="https://google.com" variant="outline">
          Link Negative Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" href="https://google.com" variant="text">
          Link Negative Text
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="negative" href="https://google.com" size="small">
          Link Negative Small
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button
          color="negative"
          href="https://google.com"
          size="small"
          variant="outline"
        >
          Link Negative Small Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button
          color="negative"
          href="https://google.com"
          size="small"
          variant="text"
        >
          Link Negative Small Text
        </Button>
      </ComponentBox>
      <Button color="negative" fullWidth href="https://google.com">
        Link Negative Full Width
      </Button>
      <Button color="negative" fullWidth href="https://google.com" size="small">
        Link Negative Full Width Small
      </Button>
    </ComponentGroup>
  </>
)
