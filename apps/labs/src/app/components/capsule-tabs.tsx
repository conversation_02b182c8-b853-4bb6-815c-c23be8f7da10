import { useState } from "react"
import { CapsuleTab } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const CapsuleTabs = () => {
  const [selectedTab, setSelectedTab] = useState<number>(0)

  return (
    <ComponentGroup>
      <ComponentBox>
        <h3>Basic Capsule Tabs</h3>
        <CapsuleTab
          tabs={[
            { label: "Tab 1", id: "tab1" },
            { label: "Tab 2", id: "tab2" },
            { label: "Tab 3", id: "tab3" },
          ]}
          selectedIndex={selectedTab}
          onSelect={(index) => setSelectedTab(index)}
        />
      </ComponentBox>
    </ComponentGroup>
  )
}
