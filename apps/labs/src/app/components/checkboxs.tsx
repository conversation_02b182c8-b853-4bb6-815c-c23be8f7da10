import { But<PERSON>, Checkbox } from "@apollo/ui"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { ComponentBox, ComponentGroup } from "./common"

export function Checkboxs() {
  const { register, handleSubmit, watch } = useForm({
    defaultValues: {
      checked: false,
    },
    resolver: zodResolver(
      z
        .object({
          name: z
            .string({
              required_error: "name is required na",
            })
            .min(1, "name is required na"),
        })
        .required({
          name: true,
        })
    ),
  })
  const result = watch()
  console.log("result", result)

  const onSubmit = ({ name }: any) => {
    console.log("[DEBUG Form] value", name)
  }
  return (
    <ComponentGroup>
      <ComponentBox>
        <h2 className="text-xl font-semibold">Checkboxs</h2>
        <Checkbox id="normal" />
        <Checkbox id="normal" indeterminate />
        <Checkbox id="normal" disabled />
        <Checkbox id="normal" label="Normal checkbox" />
        <Checkbox id="checked" label="Checked checkbox" checked />
        <Checkbox
          id="indeterminate"
          label="Indeterminate checkbox"
          indeterminate
        />
        <Checkbox id="disabled" label="Disabled checkbox" disabled />
        <Checkbox
          id="disabled-checked"
          label="Disabled checked checkbox"
          disabled
          checked
        />
        <Checkbox
          id="disabled-indeterminate"
          label="Disabled indeterminate checkbox"
          disabled
          indeterminate
        />
      </ComponentBox>
      <ComponentBox>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Checkbox
            {...register("checked")}
            id="disabled-indeterminate"
            label="Disabled indeterminate checkbox"
          />
          <Button type="submit">Submit</Button>
        </form>
      </ComponentBox>
    </ComponentGroup>
  )
}
