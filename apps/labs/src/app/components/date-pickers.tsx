import { useState } from "react"
import { DatePicker } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const DatePickers = () => {
  const [singleDate, setSingleDate] = useState<Date | null>(null)
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ])
  const [dateMonthView, setDateMonthView] = useState<Date | null>(null)
  const [dateYearView, setDateYearView] = useState<Date | null>(null)
  const [dateBuddhistEra, setDateBuddhistEra] = useState<Date | null>(null)
  const [dateGregorianEra, setDateGregorianEra] = useState<Date | null>(null)

  return (
    <>
      <ComponentGroup>
        <ComponentBox title="Basic DatePicker">
          <DatePicker
            label="Date"
            placeholder="Select date"
            value={singleDate}
            onChange={setSingleDate}
          />
          <DatePicker
            label="Date"
            showTimeSelect
            placeholder="Select date"
            format="dd/MM/yyyy HH:mm:ss"
            value={singleDate}
            onChange={setSingleDate}
          />
        </ComponentBox>

        <ComponentBox title="DatePicker with error state">
          <DatePicker
            label="Date with error"
            placeholder="Select date"
            value={singleDate}
            onChange={setSingleDate}
            error
            helperText="This is an error message"
          />
        </ComponentBox>

        <ComponentBox title="DatePicker disabled">
          <DatePicker
            label="Disabled date picker"
            placeholder="Select date"
            value={singleDate}
            onChange={setSingleDate}
            disabled
          />
        </ComponentBox>

        <ComponentBox title="DatePicker with date range">
          <DatePicker
            isRange
            label="Date range"
            placeholder="Select date range"
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={setDateRange}
          />
        </ComponentBox>

        <ComponentBox title="DatePicker view modes">
          <div className="flex flex-row gap-4">
            <DatePicker
              label="Month picker"
              placeholder="Select month"
              showMonthYearPicker
              value={dateMonthView}
              onChange={setDateMonthView}
            />

            <DatePicker
              label="Year picker"
              placeholder="Select year"
              showYearPicker
              value={dateYearView}
              onChange={setDateYearView}
            />
          </div>
        </ComponentBox>

        <ComponentBox title="DatePicker eras">
          <div className="flex flex-row gap-4">
            <DatePicker
              label="Buddhist Era (default)"
              placeholder="Select date"
              era="bd"
              value={dateBuddhistEra}
              onChange={setDateBuddhistEra}
            />

            <DatePicker
              label="Gregorian Era (AD)"
              placeholder="Select date"
              era="ad"
              value={dateGregorianEra}
              onChange={setDateGregorianEra}
            />
          </div>
        </ComponentBox>

        <ComponentBox title="DatePicker with format">
          <DatePicker
            label="Custom format"
            placeholder="Select date"
            format="dd/MM/bbbb"
            value={singleDate}
            onChange={setSingleDate}
          />
        </ComponentBox>

        <ComponentBox title="DatePicker with min/max dates">
          <DatePicker
            label="Limited date range"
            placeholder="Select date"
            value={singleDate}
            onChange={setSingleDate}
            minDate={new Date(new Date().setDate(new Date().getDate() - 5))}
            maxDate={new Date(new Date().setDate(new Date().getDate() + 5))}
            helperText="Can only select dates ±5 days from today"
          />
        </ComponentBox>
      </ComponentGroup>
    </>
  )
}
