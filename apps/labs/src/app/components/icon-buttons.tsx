import React from "react"
import { I<PERSON><PERSON><PERSON><PERSON> } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

const IconButtons = () => {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <IconButton>
          <Smile />
        </IconButton>
        <IconButton size="small">
          <Heart />
        </IconButton>
        <IconButton size="large">
          <Heart />
        </IconButton>
        <IconButton
          size="large"
          onClick={() => {
            console.log("Click!")
          }}
        >
          <Heart />
        </IconButton>
        <IconButton disabled size="large">
          <Heart />
        </IconButton>
        <IconButton
          href="https://cjx-apollo-ui.netlify.app/docs/components/button"
          target="_blank"
          size="large"
        >
          <Heart />
        </IconButton>
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <IconButton color="negative">
          <Smile />
        </IconButton>
        <IconButton color="negative" size="small">
          <Heart />
        </IconButton>
        <IconButton color="negative" size="large">
          <Heart />
        </IconButton>
        <IconButton
          color="negative"
          size="large"
          onClick={() => {
            console.log("Click!")
          }}
          variant="outline"
        >
          <Heart />
        </IconButton>
        <IconButton color="negative" disabled size="large">
          <Heart />
        </IconButton>
        <IconButton
          color="negative"
          href="https://cjx-apollo-ui.netlify.app/docs/components/button"
          target="_blank"
          size="large"
          variant="filled"
        >
          <Heart />
        </IconButton>
      </ComponentBox>
    </ComponentGroup>
  )
}

export default IconButtons
