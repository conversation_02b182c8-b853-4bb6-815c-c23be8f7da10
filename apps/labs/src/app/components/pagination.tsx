import { Pagination, PaginationItem } from "@apollo/ui"
import { Left, Right } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"
import { useState } from "react"

export const Paginations = () => {
  const [pageSize, setPageSize] = useState(10)
  return (
  <>
    <ComponentGroup>
      <ComponentBox title="Basic Pagination">
        <Pagination count={10} defaultPage={1} />
      </ComponentBox>

      <ComponentBox title="Current Page">
        <Pagination count={10} page={5} />
      </ComponentBox>

      <ComponentBox title="Disabled Pagination">
        <Pagination count={10} defaultPage={3} disabled />
      </ComponentBox>
    </ComponentGroup>

    <ComponentGroup>
      <ComponentBox title="Boundary Count">
        <Pagination count={20} defaultPage={8} boundaryCount={2} />
      </ComponentBox>

      <ComponentBox title="Sibling Count">
        <Pagination count={20} defaultPage={8} siblingCount={2} />
      </ComponentBox>

      <ComponentBox title="Hide Navigation Buttons">
        <Pagination
          count={10}
          defaultPage={5}
          showPrevPageButton={false}
          showNextPageButton={false}
        />
      </ComponentBox>
    </ComponentGroup>

    <ComponentGroup>
      <ComponentBox title="With onChange Handler">
        <Pagination
          count={10}
          defaultPage={1}
          onChange={(event, page) => {
            console.log(`Page changed to: ${page}`)
          }}
        />
      </ComponentBox>

      <ComponentBox title="Minimum Visible Count">
        <Pagination count={30} defaultPage={15} minimumVisibleCount={15} />
      </ComponentBox>
      <ComponentBox title="Page Size Pagination">
        <Pagination count={100} defaultPage={1} pageSize={pageSize} onPageSizeChange={(value) => {
            console.log(`Page size changed to: ${value}`)
            setPageSize(value)
          }}
        />
        <Pagination count={100} defaultPage={1} displayType="full" pageSize={pageSize} onPageSizeChange={(value) => {
            setPageSize(value)
          }}
        />
        <Pagination count={100} defaultPage={1} displayType="compact" pageSize={pageSize} onPageSizeChange={(value) => {
            setPageSize(value)
          }}
        />
      </ComponentBox>
    </ComponentGroup>
    <ComponentGroup>
      <ComponentBox title="Individual PaginationItem Examples">
        <div className="flex flex-row gap-2">
          <PaginationItem>1</PaginationItem>
          <PaginationItem selected>2</PaginationItem>
          <PaginationItem disabled>3</PaginationItem>
          <PaginationItem borderless>...</PaginationItem>
        </div>
      </ComponentBox>

      <ComponentBox title="Custom Navigation PaginationItem">
        <div className="flex flex-row gap-2">
          <PaginationItem>
            <Left style={{ width: "16px", height: "16px" }} />
          </PaginationItem>
          <PaginationItem>1</PaginationItem>
          <PaginationItem selected>2</PaginationItem>
          <PaginationItem>3</PaginationItem>
          <PaginationItem>
            <Right style={{ width: "16px", height: "16px" }} />
          </PaginationItem>
        </div>
      </ComponentBox>
    </ComponentGroup>
  </>
)
}