import { createTheme, CreateThemeOptions } from "@apollo/ui"

const posAdminTokens = {
  "apl-global-colors-brand-10": "#EDFAF5",
  "apl-global-colors-brand-20": "#C8F0E1",
  "apl-global-colors-brand-30": "#A4E5CD",
  "apl-global-colors-brand-40": "#7FDBB8",
  "apl-global-colors-brand-50": "#5BDB0A4",
  "apl-global-colors-brand-60": "#36C690",
  "apl-global-colors-brand-70": "#227C5A",
  "apl-global-colors-brand-80": "#185840",
  "apl-global-colors-brand-90": "#0E3325",
  "apl-global-colors-brand-95": "#092017",
} satisfies CreateThemeOptions["colors"]

export const posAdminTheme = createTheme({
  darkColors: posAdminTokens,
  lightColors: posAdminTokens,
  tokens: {
    "apl-global-font-families-content": "Kanit, sans-serif",
    "apl-global-font-families-display": "Kanit, sans-serif",
  },
})
