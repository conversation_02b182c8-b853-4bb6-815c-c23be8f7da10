// filepath: /Users/<USER>/Documents/workspace/apollo-design-system/apps/labs/src/app/components/product-card.tsx
import { Button, ProductCard, Typography } from "@apollo/ui"
import { Heart, ShoppingCart } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

export const ProductCards = () => (
  <>
    <ComponentGroup>
      <ComponentBox title="Basic Product Cards">
        {/* Basic product card with title only */}
        <ProductCard
          title="Basic Product Card"
          imageSrc="https://picsum.photos/200"
        />

        {/* Product card with body content */}
        <ProductCard
          title="Product with Body"
          imageSrc="https://picsum.photos/200"
          body={
            <Typography level="body2">
              This is a product description that explains the features and
              benefits.
            </Typography>
          }
        />

        {/* Product card with footer */}
        <ProductCard
          title="Product with Footer"
          imageSrc="https://picsum.photos/200"
          body={
            <Typography level="body2">
              Product description text here.
            </Typography>
          }
          footer={
            <div className="flex flex-col w-full gap-2">
              <Typography level="body1" className="font-bold">
                $49.99
              </Typography>
              <Button size="small" fullWidth>
                Add to Cart
              </Button>
            </div>
          }
        />
      </ComponentBox>

      <ComponentBox title="Size Variations">
        <div className="flex gap-4 flex-wrap">
          <ProductCard
            title="Default Size"
            imageSrc="https://picsum.photos/200"
          />
          <ProductCard
            title="Custom Size"
            size="200px"
            imageSrc="https://picsum.photos/200"
          />
          <div className="w-[300px] h-[300px]">
            <ProductCard
              title="Fill Size"
              size="fill"
              imageSrc="https://picsum.photos/200"
              body={
                <Typography level="body2">
                  This card fills its container.
                </Typography>
              }
            />
          </div>
        </div>
      </ComponentBox>

      <ComponentBox title="No Image Variations">
        <div className="flex gap-4 flex-wrap">
          {/* Card with default no image placeholder */}
          <ProductCard
            title="Default No Image"
            body={
              <Typography level="body2">
                This card shows the default no image placeholder.
              </Typography>
            }
          />

          {/* Card with custom no image content */}
          <ProductCard
            title="Custom No Image"
            noImage={
              <div className="flex justify-center items-center h-full">
                <Typography level="h4">No Image Available</Typography>
              </div>
            }
            body={
              <Typography level="body2">
                This card has custom no-image content.
              </Typography>
            }
          />
        </div>
      </ComponentBox>

      <ComponentBox title="With Extra and Image Overlay">
        <div className="flex gap-4 flex-wrap">
          {/* Card with extra content */}
          <ProductCard
            title="Product with Extra Content"
            imageSrc="https://picsum.photos/200"
            extra={
              <div className="flex justify-end w-full">
                <Heart size={24} color="red" />
              </div>
            }
            body={
              <Typography level="body2">
                This product has extra content above the title.
              </Typography>
            }
          />

          {/* Card with image overlay */}
          <ProductCard
            title="Product with Image Overlay"
            imageSrc="https://picsum.photos/200"
            imageOverlay="SALE"
            body={
              <Typography level="body2">
                This product shows an overlay on the image.
              </Typography>
            }
          />

          {/* Card with custom image overlay */}
          <ProductCard
            title="Custom Image Overlay"
            imageSrc="https://picsum.photos/200"
            imageOverlay={
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <Typography level="h4" className="text-white">
                  SOLD OUT
                </Typography>
              </div>
            }
            body={
              <Typography level="body2">
                This product has a custom image overlay.
              </Typography>
            }
          />
        </div>
      </ComponentBox>

      <ComponentBox title="Complete Product Card Examples">
        <div className="flex gap-4 flex-wrap">
          {/* Shopping product card example */}
          <ProductCard
            title="Premium Headphones"
            imageSrc="https://picsum.photos/200/300"
            extra={
              <div className="flex justify-between w-full">
                <Typography
                  level="body2"
                  className="bg-blue-500 text-white px-2 py-1 rounded"
                >
                  New
                </Typography>
                <Heart size={20} />
              </div>
            }
            body={
              <div className="space-y-2">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((n) => (
                    <span key={n}>⭐</span>
                  ))}
                  <Typography level="body2" className="ml-1">
                    (120)
                  </Typography>
                </div>
                <Typography level="body2">
                  High-quality wireless headphones with noise cancellation and
                  long battery life.
                </Typography>
              </div>
            }
            footer={
              <div className="flex flex-col w-full gap-2">
                <Typography level="h5">$199.99</Typography>
                <Button
                  startDecorator={<ShoppingCart size={16} />}
                  size="small"
                  fullWidth
                >
                  Add to Cart
                </Button>
              </div>
            }
          />

          {/* Event card example */}
          <ProductCard
            title="Music Festival 2025"
            imageSrc="https://picsum.photos/200/300?random=2"
            imageOverlay={
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 p-2">
                <Typography level="body2" className="text-white">
                  Apr 25-27
                </Typography>
              </div>
            }
            body={
              <Typography level="body2">
                Join us for three days of amazing music performances featuring
                top artists from around the world.
              </Typography>
            }
            footer={<Button fullWidth>Get Tickets</Button>}
          />
        </div>
      </ComponentBox>
    </ComponentGroup>
  </>
)
