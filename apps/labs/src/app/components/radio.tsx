import React, { useState } from "react"
import { Radio, RadioGroup, Typography } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const RadioDemo = () => {
  const [selected, setSelected] = useState<string>("apple")
  return (
    <ComponentGroup>
      <ComponentBox>
        <Typography level="h4">Default</Typography>
        <RadioGroup name="fruits" defaultValue="apple">
          <Radio value="apple">Apple</Radio>
          <Radio value="banana">Banana</Radio>
          <Radio value="cherry">Cherry</Radio>
        </RadioGroup>
      </ComponentBox>
      <ComponentBox>
        <Typography level="h4">Without label</Typography>
        <RadioGroup direction="horizontal" name="fruits" defaultValue="apple">
          <Radio value="apple" />
          <Radio value="banana" />
          <Radio value="cherry" />
        </RadioGroup>
      </ComponentBox>
      <ComponentBox>
        <Typography level="h4">Controlled Radio</Typography>
        <RadioGroup
          name="fruits"
          value={selected}
          onValueChange={(value) => setSelected(value as string)}
        >
          <Radio value="apple">Apple</Radio>
          <Radio value="banana">Banana</Radio>
          <Radio value="cherry">Cherry</Radio>
        </RadioGroup>
        <Typography>Selected: {selected}</Typography>
      </ComponentBox>
      <ComponentBox>
        <Typography level="h4">Disabled</Typography>
        <RadioGroup disabled name="fruits" defaultValue="apple">
          <Radio value="apple">Apple</Radio>
          <Radio value="banana">Banana</Radio>
          <Radio value="cherry">Cherry</Radio>
        </RadioGroup>
      </ComponentBox>
      <ComponentBox>
        <Typography level="h4">Horizontal</Typography>
        <RadioGroup direction="horizontal" name="fruits" defaultValue="apple">
          <Radio value="apple">Apple</Radio>
          <Radio value="banana">Banana</Radio>
          <Radio value="cherry">Cherry</Radio>
        </RadioGroup>
      </ComponentBox>
      <ComponentBox>
        <Typography level="h4">Custom Label</Typography>
        <RadioGroup name="fruits" defaultValue="apple">
          <Radio value="apple">
            <Typography level="h2">Huge Word</Typography>
          </Radio>

          <Radio value="banana">
            <Typography level="h3">Big Word</Typography>
          </Radio>
          <Radio value="cherry">Normal Word</Radio>
        </RadioGroup>
      </ComponentBox>
    </ComponentGroup>
  )
}
