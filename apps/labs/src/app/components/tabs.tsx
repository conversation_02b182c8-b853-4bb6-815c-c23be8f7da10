import { useState } from "react"
import { <PERSON><PERSON>, Ta<PERSON> } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export function TabsDemo() {
  const [activeTab, setActiveTab] = useState("controlled1")

  return (
    <ComponentGroup>
      <ComponentBox>
        <Tabs.Root defaultValue="tab1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
          <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
          <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>
      <ComponentBox>
        <Tabs.Root fullWidth defaultValue="tab1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
          <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
          <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>
      <ComponentBox>
        <Tabs.Root fullWidth defaultValue="tab1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab fitContent value="tab1">
              Tab 1
            </Tabs.Tab>
            <Tabs.Tab fitContent value="tab2">
              Tab 2
            </Tabs.Tab>
            <Tabs.Tab fitContent value="tab3">
              Tab 3
            </Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
          <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
          <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>
      <ComponentBox>
        <Tabs.Root defaultValue="tab1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab value="tab1">
              <p
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  lineClamp: 2,
                  WebkitBoxOrient: "vertical",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                Lorem ipsum dolor sit amet consectetur adipiscing elit Lorem
                ipsum dolor sit amet consectetur adipiscing elitsed do
              </p>
            </Tabs.Tab>
            <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            <Tabs.Tab value="tab3">Tab 3</Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
          <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
          <Tabs.Panel value="tab3">Content 3</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>

      {/* Tabs with disabled state */}
      <ComponentBox title="Tabs with Disabled State">
        <Tabs.Root defaultValue="disabled1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab value="disabled1">Enabled Tab</Tabs.Tab>
            <Tabs.Tab value="disabled2" disabled>
              Disabled Tab
            </Tabs.Tab>
            <Tabs.Tab value="disabled3">Enabled Tab</Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="disabled1">Content for enabled tab 1</Tabs.Panel>
          <Tabs.Panel value="disabled2">
            This content won't be easily accessible
          </Tabs.Panel>
          <Tabs.Panel value="disabled3">Content for enabled tab 3</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>

      {/* Vertical tabs */}
      {/* <ComponentBox title="Vertical Tabs">
        <div style={{ display: "flex", height: "200px" }}>
          <Tabs.Root defaultValue="vertical1" orientation="vertical">
            <Tabs.List
              style={{
                flexDirection: "column",
                height: "100%",
                borderRight:
                  "1px solid var(--apl-colors-border-primary-subdued, #409261)",
                borderBottom: "none",
              }}
            >
              <Tabs.Tab value="vertical1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="vertical2">Tab 2</Tabs.Tab>
              <Tabs.Tab value="vertical3">Tab 3</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <div style={{ flex: 1, padding: "16px" }}>
              <Tabs.Panel value="vertical1">
                Content for vertical tab 1
              </Tabs.Panel>
              <Tabs.Panel value="vertical2">
                Content for vertical tab 2
              </Tabs.Panel>
              <Tabs.Panel value="vertical3">
                Content for vertical tab 3
              </Tabs.Panel>
            </div>
          </Tabs.Root>
        </div>
      </ComponentBox> */}

      {/* Tabs with icons */}
      <ComponentBox title="Tabs with Icons">
        <Tabs.Root defaultValue="icon1" orientation="horizontal">
          <Tabs.List>
            <Tabs.Tab value="icon1">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <span role="img" aria-label="home">
                  🏠
                </span>
                <span>Home</span>
              </div>
            </Tabs.Tab>
            <Tabs.Tab value="icon2">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <span role="img" aria-label="settings">
                  ⚙️
                </span>
                <span>Settings</span>
              </div>
            </Tabs.Tab>
            <Tabs.Tab value="icon3">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <span role="img" aria-label="profile">
                  👤
                </span>
                <span>Profile</span>
              </div>
            </Tabs.Tab>
            <Tabs.Indicator />
          </Tabs.List>
          <Tabs.Panel value="icon1">Home content</Tabs.Panel>
          <Tabs.Panel value="icon2">Settings content</Tabs.Panel>
          <Tabs.Panel value="icon3">Profile content</Tabs.Panel>
        </Tabs.Root>
      </ComponentBox>

      {/* Controlled tabs */}
      <ComponentBox title="Controlled Tabs">
        <div>
          <Tabs.Root
            value={activeTab}
            onValueChange={setActiveTab}
            orientation="horizontal"
          >
            <Tabs.List>
              <Tabs.Tab value="controlled1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="controlled2">Tab 2</Tabs.Tab>
              <Tabs.Tab value="controlled3">Tab 3</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="controlled1">
              Content for controlled tab 1
            </Tabs.Panel>
            <Tabs.Panel value="controlled2">
              Content for controlled tab 2
            </Tabs.Panel>
            <Tabs.Panel value="controlled3">
              Content for controlled tab 3
            </Tabs.Panel>
          </Tabs.Root>
          <div style={{ marginTop: "16px" }}>
            <p>External tab control:</p>
            <div style={{ display: "flex", gap: "8px", marginTop: "8px" }}>
              <Button onClick={() => setActiveTab("controlled1")}>
                Select Tab 1
              </Button>
              <Button onClick={() => setActiveTab("controlled2")}>
                Select Tab 2
              </Button>
              <Button onClick={() => setActiveTab("controlled3")}>
                Select Tab 3
              </Button>
            </div>
            <p style={{ marginTop: "8px" }}>Active tab value: {activeTab}</p>
          </div>
        </div>
      </ComponentBox>
    </ComponentGroup>
  )
}
