import React from "react"
import { Typography } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const Typographys = () => (
  <>
    <ComponentGroup>
      <ComponentBox direction="horizontal" className="text-left gap-4">
        <Typography level="displayLarge">Display Large</Typography>
        <Typography level="displayMedium">Display Medium</Typography>
        <Typography level="displaySmall">Display Small</Typography>
        <Typography level="headlineLarge">Headline Large</Typography>
        <Typography level="headlineMedium">Headline Medium</Typography>
        <Typography level="headlineSmall">Headline Small</Typography>
        <Typography level="titleLarge">Title Large</Typography>
        <Typography level="titleMedium">Title Medium</Typography>
        <Typography level="titleSmall">Title Small</Typography>
        <Typography level="bodyLarge">Body Large</Typography>
        <Typography level="bodyLargeEmphasized">Body Large Emphasized</Typography>
        <Typography level="bodyMedium">Body Medium</Typography>
        <Typography level="bodyMediumEmphasized">Body Medium Emphasized</Typography>
        <Typography level="bodySmall">Body Small</Typography>
        <Typography level="bodySmallEmphasized">Body Small Emphasized</Typography>
        <Typography level="labelLarge">Label Large</Typography>
        <Typography level="labelLargeEmphasized">Label Large Emphasized</Typography>
        <Typography level="labelMedium">Label Medium</Typography>
        <Typography level="labelMediumEmphasized">Label Medium Emphasized</Typography>
        <Typography level="labelSmall">Label Small</Typography>
        <Typography level="labelSmallEmphasized">Label Small Emphasized</Typography>
      </ComponentBox>
      <ComponentBox direction="horizontal" className="text-left gap-4">
        <Typography level="display1">Display 1</Typography>
        <Typography level="display2">Display 2</Typography>
        <Typography level="h1">Heading 1</Typography>
        <Typography level="h2">Heading 2</Typography>
        <Typography level="h3">Heading 3</Typography>
        <Typography level="h4">Heading 4</Typography>
        <Typography level="h5">Heading 5</Typography>
        <Typography level="body1">Body 1</Typography>
        <Typography level="body2">Body 2</Typography>
        <Typography level="caption">Caption</Typography>
        <Typography level="textlink" href="#">
          Text Link
        </Typography>
      </ComponentBox>
      <ComponentBox className="items-start w-[100px]">
        <Typography className="w-full" align="left">
          Left
        </Typography>
        <Typography className="w-full" align="center">
          Center
        </Typography>
        <Typography className="w-full" align="right">
          Right
        </Typography>
      </ComponentBox>
      <ComponentBox>
        <Typography color="primary">Primary Colored Text</Typography>
        <Typography color="negative">Danger Colored Text</Typography>
        <Typography gutterBottom>Text with Gutter Bottom</Typography>
        <Typography noWrap style={{ width: "100px" }}>
          No Wrap Text with a very long content that should not wrap
        </Typography>
        <Typography className="custom-class">Text with Custom Class</Typography>
      </ComponentBox>
    </ComponentGroup>
  </>
)
