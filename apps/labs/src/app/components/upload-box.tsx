"use client"

import { useState } from "react"
import {
  UploadBox,
  useUploadMultipleFile,
  useUploadSingleFile,
} from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export const UploadBoxDemo = () => {
  const [disabledValue, setDisabledValue] = useState<File[] | null>(null)
  const [errorValue, setErrorValue] = useState<File[] | null>(null)
  const [errorState, setErrorState] = useState<({ key: string; errorMessage: string; }[]) | null>(null)
  const [errorMessage, setErrorMessage] = useState<string | undefined>("There was an error uploading your file. Please try again.")
  // Single file upload example
  const singleFileUpload = useUploadSingleFile({
    uploadFileFn: async (file) => {
      // Simulate an upload process
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log("File uploaded:", file.name)
          resolve(file)
        }, 2000)
      })
    },
    onDelete: () => {
      console.log("File deleted")
    },
    onCancelUpload: (file) => {
      console.log("Upload cancelled for file:", file.name)
    },
  })

  // Multiple file upload example
  const multipleFileUpload = useUploadMultipleFile({
    uploadFileFn: async (file) => {
      // Simulate an upload process
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log("File uploaded:", file.name)
          setDisabledValue((prev) => [...(prev ?? []), file])
          resolve(file)
        }, 4000)
      })
    },
    onDelete: (fileIndex) => {
      console.log("File deleted at index:", fileIndex)
    },
    onCancelUpload: (file, fileIndex) => {
      console.log(
        "Upload cancelled for file:",
        file.name,
        "at index:",
        fileIndex
      )
    },
  })

  return (
    <>
      <ComponentGroup>
        <ComponentBox title="Basic UploadBox">
          <UploadBox label="Upload File" helperText="Upload your files here" />
        </ComponentBox>
        <ComponentBox title="With Custom Allowed Extensions">
          <UploadBox
            label="Upload Document"
            helperText="Only PDF and DOC files allowed"
            allowedFilesExtension={["pdf", "doc", "docx"]}
          />
        </ComponentBox>
      </ComponentGroup>

      <ComponentGroup>
        <ComponentBox title="Single File Upload With State Management">
          <UploadBox
            label="Profile Picture"
            helperText="Upload your profile picture"
            allowedFilesExtension={["jpg", "png", "jpeg"]}
            maxFileSizeInBytes={2 * 1024 * 1024} // 2MB limit
            {...singleFileUpload}
          />
        </ComponentBox>
        <ComponentBox title="Multiple File Upload">
          <UploadBox
            label="Multiple Documents"
            helperText="Upload multiple files"
            multiple
            fullWidth
            fileLimit={3}
            allowedFilesExtension={["pdf", "jpg", "png", "doc"]}
            {...multipleFileUpload}
          />
        </ComponentBox>
      </ComponentGroup>

      <ComponentGroup>
        <ComponentBox title="Disabled UploadBox">
          <UploadBox
            label="Disabled Upload"
            helperText="You cannot upload files here"
            disabled
            multiple
            value={disabledValue}
          />
        </ComponentBox>
        <ComponentBox title="Error UploadBox">
          <UploadBox
            label="Error Upload"
            helperText="There was an error uploading your file. Please try again."
            multiple
            value={errorValue}
            fileState={errorState}
            errorMessage="There was an error uploading your file. Please try again."
            onUpload={(uploadedFile: File[]) => {
              setErrorValue(uploadedFile)
              setErrorState(uploadedFile.map((file) => ({ key: file.name, errorMessage: "Cloud Error" })))
            }}
          />
        </ComponentBox>
        <ComponentBox title="With Custom Upload Button Text">
          <UploadBox
            label="Custom Button Text"
            helperText="Customized button text"
            uploadButtonText="Choose Files"
            errorMessage={errorMessage}
            errorOnClose={() => {
              setErrorMessage("")
            }}
          />
        </ComponentBox>
      </ComponentGroup>
    </>
  )
}

export default UploadBoxDemo
