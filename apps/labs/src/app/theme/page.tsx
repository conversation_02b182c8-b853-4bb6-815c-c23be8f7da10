"use client"

import React from "react"
import {
  createThemeV2,
  Theme,
} from "@apollo/ui"

import Badges from "../components/badges"
import { SelectDemo } from "../components/select"

// Demo components that use the theme system
function ThemeCard({
  title,
  children,
}: Readonly<{ title: string; children: React.ReactNode }>) {
  return (
    <div
      style={{
        border: "1px solid var(--apl-color-gray-smoke-80, #ccc)",
        borderRadius: "var(--apl-radius-8, 8px)",
        padding: "var(--apl-spacing-12, 16px)",
        margin: "var(--apl-spacing-12, 8px)",
        backgroundColor: "var(--apl-alias-color-background-and-surface-surface)",
        boxShadow: "0 2px 4px var(--apl-color-gray-smoke-20, #00000020)",
      }}
    >
      <h3
        style={{
          margin: "0 0 12px 0",
          color:"var(--apl-alias-color-background-and-surface-on-surface)",
          fontSize: "var(--apl-base-typography-font-size-xs, 18px)",
        }}
      >
        {title}
      </h3>
      {children}
    </div>
  )
}

function ThemedButton({
  children,
  variant = "primary",
}: Readonly<{
  children: React.ReactNode
  variant?: "primary" | "secondary"
}>) {
  const isPrimary = variant === "primary"

  return (
    <button
      style={{
        backgroundColor: isPrimary
          ? "var(--apl-alias-color-primary-primary)"
          : "var(--apl-alias-color-secondary-secondary)",
        color: isPrimary
          ? "var(--apl-color-gray-smoke-100, #ffffff)"
          : "var(--apl-color-gray-smoke-10, #1C1B1C)",
        padding: "var(--apl-spacing-3, 8px) var(--apl-global-spacing-5, 16px)",
        borderRadius: "var(--apl-border-radius-sm, 4px)",
        border: "none",
        cursor: "pointer",
        fontSize: "var(--apl-typography-font-size-sm, 14px)",
        fontWeight: "normal",
        transition: "all 0.2s ease",
        margin: "var(--apl-spacing-2, 4px)",
      }}
      onMouseOver={(e) => {
        e.currentTarget.style.opacity = "0.8"
      }}
      onMouseOut={(e) => {
        e.currentTarget.style.opacity = "1"
      }}
      onFocus={(e) => {
        e.currentTarget.style.opacity = "0.8"
      }}
      onBlur={(e) => {
        e.currentTarget.style.opacity = "1"
      }}
    >
      {children}
    </button>
  )
}

// Example 2: Custom Theme
function CustomThemeExample() {
  const customTheme = createThemeV2({
    tokens: {
      color: {
        "green-pine": {
          "40": "#ff6b35", //change green to orange
        },
        "gray-smoke": {
          "40": "#004e89",
        },
      },
    },
  })

  return (
    <Theme theme={customTheme}>
      <ThemeCard title="Custom Theme Override">
        <p>This theme overrides specific Apollo tokens with custom values.</p>
        <div>
          <ThemedButton>Custom Primary</ThemedButton>
          <ThemedButton variant="secondary">Custom Secondary</ThemedButton>
        </div>
      </ThemeCard>
    </Theme>
  )
}

// Example 3: Nested Themes
function NestedThemeExample() {

  const childTheme = createThemeV2({
    tokens: {
      base: {
        color: {
          primary: {
            0: "{color.gray-bluish.0}",
            10: "{color.gray-bluish.10}",
            20: "{color.gray-bluish.20}",
            30: "{color.gray-bluish.30}",
            40: "#565F71",
            50: "{color.gray-bluish.50}",
            60: "{color.gray-bluish.60}",
            70: "{color.gray-bluish.70}",
            80: "{color.gray-bluish.80}",
            90: "{color.gray-bluish.90}",
            95: "#ECF0FF",
            99: "#FDFBFF",
            100: "{color.gray-bluish.100}",
          },
          secondary: {
            0: "{color.green-pine.0}",
            10: "{color.green-pine.10}",
            20: "{color.green-pine.20}",
            30: "{color.green-pine.30}",
            40: "{color.green-pine.40}",
            50: "{color.green-pine.50}",
            60: "{color.green-pine.60}",
            70: "{color.green-pine.70}",
            80: "{color.green-pine.80}",
            90: "{color.green-pine.90}",
            99: "{color.green-pine.99}",
            95: "{color.green-pine.95}",
            100: "{color.green-pine.100}",
          },
        },
      },
    },
  })

  return (
    <ThemeCard title="Parent Theme">
      <p>This is the parent theme with green primary color.</p>
      <ThemedButton>Parent Button</ThemedButton>
      <Badges />
      <SelectDemo />
      <Theme theme={childTheme} mode="dark">
        <ThemeCard title="Child Theme (Inherits + Overrides)">
          <p>
            This child theme inherits from parent but overrides the primary
            color to red.
          </p>
          <ThemedButton variant="primary">Child Button</ThemedButton>
          <Badges />
          <SelectDemo />
          <Theme theme={createThemeV2()} >
            <ThemeCard title="Child Theme (Inherits)">
              <ThemedButton>Child 2 Button</ThemedButton>
            </ThemeCard>
          </Theme>
        </ThemeCard>
      </Theme>
    </ThemeCard>
  )
}

export default function ThemePage() {
  return (
    <Theme>
      <div
        style={{
          padding: "24px",
          fontFamily: "var(--apl-alias-typography-font-family)",
          backgroundColor: "#f8f9fa",
          minHeight: "100vh",
        }}
      >
        <div style={{ maxWidth: "1200px", margin: "0 auto" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "32px",
            }}
          >
            <h1
              style={{
                margin: 0,
                color: "#1a1a1a",
                fontSize: "32px",
              }}
            >
              Apollo Theme Examples
            </h1>

            <a
              href="/theme/advanced"
              style={{
                padding: "12px 24px",
                backgroundColor: "#2C8745",
                color: "white",
                textDecoration: "none",
                borderRadius: "6px",
                fontSize: "14px",
                fontWeight: "500",
              }}
            >
              View Advanced Examples →
            </a>
          </div>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(500px, 1fr))",
              gap: "24px",
            }}
          >
            <CustomThemeExample />
            <NestedThemeExample />
          </div>
        </div>
      </div>
    </Theme>
  )
}
