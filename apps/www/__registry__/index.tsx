// @ts-nocheck
// This file is autogenerated by scripts/build-registry.ts
// Do not edit this file directly.
import * as React from "react"

export const Index: Record<string, any> = {
  "default": {
    "button-demo": {
      name: "button-demo",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-demo")),
      files: ["registry/default/example/button-demo.tsx"],
    },
    "button-secondary": {
      name: "button-secondary",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-secondary")),
      files: ["registry/default/example/button-secondary.tsx"],
    },
    "button-disabled": {
      name: "button-disabled",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-disabled")),
      files: ["registry/default/example/button-disabled.tsx"],
    },
    "button-outline": {
      name: "button-outline",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-outline")),
      files: ["registry/default/example/button-outline.tsx"],
    },
    "button-preview": {
      name: "button-preview",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-preview")),
      files: ["registry/default/example/button-preview.tsx"],
    },
    "button-loading": {
      name: "button-loading",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-loading")),
      files: ["registry/default/example/button-loading.tsx"],
    },
    "button-icon": {
      name: "button-icon",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-icon")),
      files: ["registry/default/example/button-icon.tsx"],
    },
    "button-as": {
      name: "button-as",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-as")),
      files: ["registry/default/example/button-as.tsx"],
    },
    "button-plain": {
      name: "button-plain",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-plain")),
      files: ["registry/default/example/button-plain.tsx"],
    },
    "button-override": {
      name: "button-override",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-override")),
      files: ["registry/default/example/button-override.tsx"],
    },
    "typography-preview": {
      name: "typography-preview",
      type: "components:example",
      registryDependencies: ["typography"],
      component: React.lazy(() => import("@/registry/default/example/typography-preview")),
      files: ["registry/default/example/typography-preview.tsx"],
    },
    "typography-demo": {
      name: "typography-demo",
      type: "components:example",
      registryDependencies: ["typography"],
      component: React.lazy(() => import("@/registry/default/example/typography-demo")),
      files: ["registry/default/example/typography-demo.tsx"],
    },
    "typography-align": {
      name: "typography-align",
      type: "components:example",
      registryDependencies: ["typography"],
      component: React.lazy(() => import("@/registry/default/example/typography-align")),
      files: ["registry/default/example/typography-align.tsx"],
    },
    "typography-override": {
      name: "typography-override",
      type: "components:example",
      registryDependencies: ["typography"],
      component: React.lazy(() => import("@/registry/default/example/typography-override")),
      files: ["registry/default/example/typography-override.tsx"],
    },
    "input-preview": {
      name: "input-preview",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-preview")),
      files: ["registry/default/example/input-preview.tsx"],
    },
    "input-size": {
      name: "input-size",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-size")),
      files: ["registry/default/example/input-size.tsx"],
    },
    "input-outline": {
      name: "input-outline",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-outline")),
      files: ["registry/default/example/input-outline.tsx"],
    },
    "input-icon": {
      name: "input-icon",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-icon")),
      files: ["registry/default/example/input-icon.tsx"],
    },
    "input-disabled": {
      name: "input-disabled",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-disabled")),
      files: ["registry/default/example/input-disabled.tsx"],
    },
    "input-error": {
      name: "input-error",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-error")),
      files: ["registry/default/example/input-error.tsx"],
    },
    "input-multiline": {
      name: "input-multiline",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-multiline")),
      files: ["registry/default/example/input-multiline.tsx"],
    },
    "input-with-label": {
      name: "input-with-label",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-with-label")),
      files: ["registry/default/example/input-with-label.tsx"],
    },
    "icon-preview": {
      name: "icon-preview",
      type: "components:example",
      registryDependencies: ["icon"],
      component: React.lazy(() => import("@/registry/default/example/icon-preview")),
      files: ["registry/default/example/icon-preview.tsx"],
    },
    "datepicker-preview": {
      name: "datepicker-preview",
      type: "components:example",
      registryDependencies: ["datepicker"],
      component: React.lazy(() => import("@/registry/default/example/datepicker-preview")),
      files: ["registry/default/example/datepicker-preview.tsx"],
    },
    "datepicker-disabled": {
      name: "datepicker-disabled",
      type: "components:example",
      registryDependencies: ["datepicker"],
      component: React.lazy(() => import("@/registry/default/example/datepicker-disabled")),
      files: ["registry/default/example/datepicker-disabled.tsx"],
    },
    "datepicker-error": {
      name: "datepicker-error",
      type: "components:example",
      registryDependencies: ["datepicker"],
      component: React.lazy(() => import("@/registry/default/example/datepicker-error")),
      files: ["registry/default/example/datepicker-error.tsx"],
    },
    "datepicker-time": {
      name: "datepicker-time",
      type: "components:example",
      registryDependencies: ["datepicker"],
      component: React.lazy(() => import("@/registry/default/example/datepicker-time")),
      files: ["registry/default/example/datepicker-time.tsx"],
    },
    "select-preview": {
      name: "select-preview",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-preview")),
      files: ["registry/default/example/select-preview.tsx"],
    },
    "select-label-helper-text": {
      name: "select-label-helper-text",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-label-helper-text")),
      files: ["registry/default/example/select-label-helper-text.tsx"],
    },
    "select-disabled": {
      name: "select-disabled",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-disabled")),
      files: ["registry/default/example/select-disabled.tsx"],
    },
    "select-error": {
      name: "select-error",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-error")),
      files: ["registry/default/example/select-error.tsx"],
    },
    "select-fullwidth": {
      name: "select-fullwidth",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-fullwidth")),
      files: ["registry/default/example/select-fullwidth.tsx"],
    },
    "select-controlled": {
      name: "select-controlled",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-controlled")),
      files: ["registry/default/example/select-controlled.tsx"],
    },
    "select-multiple": {
      name: "select-multiple",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-multiple")),
      files: ["registry/default/example/select-multiple.tsx"],
    },
    "select-value-appearance": {
      name: "select-value-appearance",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-value-appearance")),
      files: ["registry/default/example/select-value-appearance.tsx"],
    },
    "breadcrumbs-preview": {
      name: "breadcrumbs-preview",
      type: "components:example",
      registryDependencies: ["breadcrumbs"],
      component: React.lazy(() => import("@/registry/default/example/breadcrumbs-preview")),
      files: ["registry/default/example/breadcrumbs-preview.tsx"],
    },
    "breadcrumbs-ellipsis": {
      name: "breadcrumbs-ellipsis",
      type: "components:example",
      registryDependencies: ["breadcrumbs"],
      component: React.lazy(() => import("@/registry/default/example/breadcrumbs-ellipsis")),
      files: ["registry/default/example/breadcrumbs-ellipsis.tsx"],
    },
    "breadcrumbs-mutiple-active": {
      name: "breadcrumbs-mutiple-active",
      type: "components:example",
      registryDependencies: ["breadcrumbs"],
      component: React.lazy(() => import("@/registry/default/example/breadcrumbs-mutiple-active")),
      files: ["registry/default/example/breadcrumbs-mutiple-active.tsx"],
    },
    "chip-demo": {
      name: "chip-demo",
      type: "components:example",
      registryDependencies: ["chip"],
      component: React.lazy(() => import("@/registry/default/example/chip-demo")),
      files: ["registry/default/example/chip-demo.tsx"],
    },
    "alert-demo": {
      name: "alert-demo",
      type: "components:example",
      registryDependencies: ["alert"],
      component: React.lazy(() => import("@/registry/default/example/alert-demo")),
      files: ["registry/default/example/alert-demo.tsx"],
    },
    "switch-demo": {
      name: "switch-demo",
      type: "components:example",
      registryDependencies: ["switch"],
      component: React.lazy(() => import("@/registry/default/example/switch-demo")),
      files: ["registry/default/example/switch-demo.tsx"],
    },
    "toast-demo": {
      name: "toast-demo",
      type: "components:example",
      registryDependencies: ["toast"],
      component: React.lazy(() => import("@/registry/default/example/toast-demo")),
      files: ["registry/default/example/toast-demo.tsx"],
    },
    "toast-with-hook": {
      name: "toast-with-hook",
      type: "components:example",
      registryDependencies: ["toast"],
      component: React.lazy(() => import("@/registry/default/example/toast-with-hook")),
      files: ["registry/default/example/toast-with-hook.tsx"],
    },
    "tabs-demo": {
      name: "tabs-demo",
      type: "components:example",
      registryDependencies: ["tabs"],
      component: React.lazy(() => import("@/registry/default/example/tabs-demo")),
      files: ["registry/default/example/tabs-demo.tsx"],
    },
    "tabs-variants": {
      name: "tabs-variants",
      type: "components:example",
      registryDependencies: ["tabs"],
      component: React.lazy(() => import("@/registry/default/example/tabs-variants")),
      files: ["registry/default/example/tabs-variants.tsx"],
    },
    "checkbox-demo": {
      name: "checkbox-demo",
      type: "components:example",
      registryDependencies: ["checkbox"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-demo")),
      files: ["registry/default/example/checkbox-demo.tsx"],
    },
    "radio-demo": {
      name: "radio-demo",
      type: "components:example",
      registryDependencies: ["radio"],
      component: React.lazy(() => import("@/registry/default/example/radio-demo")),
      files: ["registry/default/example/radio-demo.tsx"],
    },
    "radio-group": {
      name: "radio-group",
      type: "components:example",
      registryDependencies: ["radio"],
      component: React.lazy(() => import("@/registry/default/example/radio-group")),
      files: ["registry/default/example/radio-group.tsx"],
    },
    "radio-standalone": {
      name: "radio-standalone",
      type: "components:example",
      registryDependencies: ["radio"],
      component: React.lazy(() => import("@/registry/default/example/radio-standalone")),
      files: ["registry/default/example/radio-standalone.tsx"],
    },
    "accordion-demo": {
      name: "accordion-demo",
      type: "components:example",
      registryDependencies: ["accordion"],
      component: React.lazy(() => import("@/registry/default/example/accordion-demo")),
      files: ["registry/default/example/accordion-demo.tsx"],
    },
    "accordion-custom": {
      name: "accordion-custom",
      type: "components:example",
      registryDependencies: ["accordion"],
      component: React.lazy(() => import("@/registry/default/example/accordion-custom")),
      files: ["registry/default/example/accordion-custom.tsx"],
    },
    "accordion-disabled": {
      name: "accordion-disabled",
      type: "components:example",
      registryDependencies: ["accordion"],
      component: React.lazy(() => import("@/registry/default/example/accordion-disabled")),
      files: ["registry/default/example/accordion-disabled.tsx"],
    },
    "accordion-controlled": {
      name: "accordion-controlled",
      type: "components:example",
      registryDependencies: ["accordion"],
      component: React.lazy(() => import("@/registry/default/example/accordion-controlled")),
      files: ["registry/default/example/accordion-controlled.tsx"],
    },
    "modal-preview": {
      name: "modal-preview",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-preview")),
      files: ["registry/default/example/modal-preview.tsx"],
    },
    "modal-full-size": {
      name: "modal-full-size",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-full-size")),
      files: ["registry/default/example/modal-full-size.tsx"],
    },
    "modal-with-close-on-esc": {
      name: "modal-with-close-on-esc",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-close-on-esc")),
      files: ["registry/default/example/modal-with-close-on-esc.tsx"],
    },
    "modal-with-close-on-backdrop-click": {
      name: "modal-with-close-on-backdrop-click",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-close-on-backdrop-click")),
      files: ["registry/default/example/modal-with-close-on-backdrop-click.tsx"],
    },
    "modal-with-custom-header-and-footer": {
      name: "modal-with-custom-header-and-footer",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-custom-header-and-footer")),
      files: ["registry/default/example/modal-with-custom-header-and-footer.tsx"],
    },
    "modal-with-icon": {
      name: "modal-with-icon",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-icon")),
      files: ["registry/default/example/modal-with-icon.tsx"],
    },
    "modal-with-ok-button": {
      name: "modal-with-ok-button",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-ok-button")),
      files: ["registry/default/example/modal-with-ok-button.tsx"],
    },
    "modal-with-cancel-button": {
      name: "modal-with-cancel-button",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-cancel-button")),
      files: ["registry/default/example/modal-with-cancel-button.tsx"],
    },
    "modal-with-delete-button": {
      name: "modal-with-delete-button",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-delete-button")),
      files: ["registry/default/example/modal-with-delete-button.tsx"],
    },
    "modal-negative-variant": {
      name: "modal-negative-variant",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-negative-variant")),
      files: ["registry/default/example/modal-negative-variant.tsx"],
    },
    "modal-with-disabled-buttons": {
      name: "modal-with-disabled-buttons",
      type: "components:example",
      registryDependencies: ["modal"],
      component: React.lazy(() => import("@/registry/default/example/modal-with-disabled-buttons")),
      files: ["registry/default/example/modal-with-disabled-buttons.tsx"],
    },
    "product-card-demo": {
      name: "product-card-demo",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-demo")),
      files: ["registry/default/example/product-card-demo.tsx"],
    },
    "product-card-size": {
      name: "product-card-size",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-size")),
      files: ["registry/default/example/product-card-size.tsx"],
    },
    "product-card-extra": {
      name: "product-card-extra",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-extra")),
      files: ["registry/default/example/product-card-extra.tsx"],
    },
    "product-card-default": {
      name: "product-card-default",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-default")),
      files: ["registry/default/example/product-card-default.tsx"],
    },
    "product-card-with-body": {
      name: "product-card-with-body",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-with-body")),
      files: ["registry/default/example/product-card-with-body.tsx"],
    },
    "product-card-with-footer": {
      name: "product-card-with-footer",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-with-footer")),
      files: ["registry/default/example/product-card-with-footer.tsx"],
    },
    "product-card-with-image": {
      name: "product-card-with-image",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-with-image")),
      files: ["registry/default/example/product-card-with-image.tsx"],
    },
    "product-card-with-image-overlay": {
      name: "product-card-with-image-overlay",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-with-image-overlay")),
      files: ["registry/default/example/product-card-with-image-overlay.tsx"],
    },
    "product-card-with-custom-image-component": {
      name: "product-card-with-custom-image-component",
      type: "components:example",
      registryDependencies: ["product-card"],
      component: React.lazy(() => import("@/registry/default/example/product-card-with-custom-image-component")),
      files: ["registry/default/example/product-card-with-custom-image-component.tsx"],
    },
    "navbar-demo": {
      name: "navbar-demo",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-demo")),
      files: ["registry/default/example/navbar-demo.tsx"],
    },
    "navbar-no-shadow": {
      name: "navbar-no-shadow",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-no-shadow")),
      files: ["registry/default/example/navbar-no-shadow.tsx"],
    },
    "navbar-with-active-icons": {
      name: "navbar-with-active-icons",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-with-active-icons")),
      files: ["registry/default/example/navbar-with-active-icons.tsx"],
    },
    "navbar-with-additional-property": {
      name: "navbar-with-additional-property",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-with-additional-property")),
      files: ["registry/default/example/navbar-with-additional-property.tsx"],
    },
    "navbar-with-hidden-item": {
      name: "navbar-with-hidden-item",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-with-hidden-item")),
      files: ["registry/default/example/navbar-with-hidden-item.tsx"],
    },
    "navbar-with-badge": {
      name: "navbar-with-badge",
      type: "components:example",
      registryDependencies: ["navbar"],
      component: React.lazy(() => import("@/registry/default/example/navbar-with-badge")),
      files: ["registry/default/example/navbar-with-badge.tsx"],
    },
    "capsule-tab-demo": {
      name: "capsule-tab-demo",
      type: "components:example",
      registryDependencies: ["capsule-tab"],
      component: React.lazy(() => import("@/registry/default/example/capsule-tab-demo")),
      files: ["registry/default/example/capsule-tab-demo.tsx"],
    },
    "float-button-preview": {
      name: "float-button-preview",
      type: "components:example",
      registryDependencies: ["float-button"],
      component: React.lazy(() => import("@/registry/default/example/float-button-preview")),
      files: ["registry/default/example/float-button-preview.tsx"],
    },
    "autocomplete-demo": {
      name: "autocomplete-demo",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-demo")),
      files: ["registry/default/example/autocomplete-demo.tsx"],
    },
    "autocomplete-not-searchable": {
      name: "autocomplete-not-searchable",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-not-searchable")),
      files: ["registry/default/example/autocomplete-not-searchable.tsx"],
    },
    "autocomplete-form-props": {
      name: "autocomplete-form-props",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-form-props")),
      files: ["registry/default/example/autocomplete-form-props.tsx"],
    },
    "autocomplete-fit-and-full-width": {
      name: "autocomplete-fit-and-full-width",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-fit-and-full-width")),
      files: ["registry/default/example/autocomplete-fit-and-full-width.tsx"],
    },
    "autocomplete-infinite-load": {
      name: "autocomplete-infinite-load",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-infinite-load")),
      files: ["registry/default/example/autocomplete-infinite-load.tsx"],
    },
    "autocomplete-custom-filter-options": {
      name: "autocomplete-custom-filter-options",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-custom-filter-options")),
      files: ["registry/default/example/autocomplete-custom-filter-options.tsx"],
    },
    "autocomplete-multiple": {
      name: "autocomplete-multiple",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-multiple")),
      files: ["registry/default/example/autocomplete-multiple.tsx"],
    },
    "autocomplete-limit-visible-tags": {
      name: "autocomplete-limit-visible-tags",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-limit-visible-tags")),
      files: ["registry/default/example/autocomplete-limit-visible-tags.tsx"],
    },
    "autocomplete-with-select-all-option": {
      name: "autocomplete-with-select-all-option",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-with-select-all-option")),
      files: ["registry/default/example/autocomplete-with-select-all-option.tsx"],
    },
    "autocomplete-custom-select-all-option": {
      name: "autocomplete-custom-select-all-option",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-custom-select-all-option")),
      files: ["registry/default/example/autocomplete-custom-select-all-option.tsx"],
    },
    "autocomplete-server-side": {
      name: "autocomplete-server-side",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-server-side")),
      files: ["registry/default/example/autocomplete-server-side.tsx"],
    },
    "autocomplete-controlled-open-state": {
      name: "autocomplete-controlled-open-state",
      type: "components:example",
      registryDependencies: ["autocomplete"],
      component: React.lazy(() => import("@/registry/default/example/autocomplete-controlled-open-state")),
      files: ["registry/default/example/autocomplete-controlled-open-state.tsx"],
    },
    "dateinput-demo": {
      name: "dateinput-demo",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-demo")),
      files: ["registry/default/example/dateinput-demo.tsx"],
    },
    "dateinput-era": {
      name: "dateinput-era",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-era")),
      files: ["registry/default/example/dateinput-era.tsx"],
    },
    "dateinput-locale": {
      name: "dateinput-locale",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-locale")),
      files: ["registry/default/example/dateinput-locale.tsx"],
    },
    "dateinput-format": {
      name: "dateinput-format",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-format")),
      files: ["registry/default/example/dateinput-format.tsx"],
    },
    "dateinput-excludedate": {
      name: "dateinput-excludedate",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-excludedate")),
      files: ["registry/default/example/dateinput-excludedate.tsx"],
    },
    "dateinput-month-year-picker": {
      name: "dateinput-month-year-picker",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-month-year-picker")),
      files: ["registry/default/example/dateinput-month-year-picker.tsx"],
    },
    "dateinput-daterange": {
      name: "dateinput-daterange",
      type: "components:example",
      registryDependencies: ["dateinput"],
      component: React.lazy(() => import("@/registry/default/example/dateinput-daterange")),
      files: ["registry/default/example/dateinput-daterange.tsx"],
    },
    "icon-button-demo": {
      name: "icon-button-demo",
      type: "components:example",
      registryDependencies: ["icon-button"],
      component: React.lazy(() => import("@/registry/default/example/icon-button-demo")),
      files: ["registry/default/example/icon-button-demo.tsx"],
    },
    "upload-box-demo": {
      name: "upload-box-demo",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-demo")),
      files: ["registry/default/example/upload-box-demo.tsx"],
    },
    "upload-box-multiple": {
      name: "upload-box-multiple",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-multiple")),
      files: ["registry/default/example/upload-box-multiple.tsx"],
    },
    "upload-box-allowed-file-type-and-limit-size": {
      name: "upload-box-allowed-file-type-and-limit-size",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-allowed-file-type-and-limit-size")),
      files: ["registry/default/example/upload-box-allowed-file-type-and-limit-size.tsx"],
    },
    "upload-box-error-state": {
      name: "upload-box-error-state",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-error-state")),
      files: ["registry/default/example/upload-box-error-state.tsx"],
    },
    "upload-box-file-limit": {
      name: "upload-box-file-limit",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-file-limit")),
      files: ["registry/default/example/upload-box-file-limit.tsx"],
    },
    "upload-box-custom-messsage-ui": {
      name: "upload-box-custom-messsage-ui",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-custom-messsage-ui")),
      files: ["registry/default/example/upload-box-custom-messsage-ui.tsx"],
    },
    "upload-box-loading-state": {
      name: "upload-box-loading-state",
      type: "components:example",
      registryDependencies: ["upload-box"],
      component: React.lazy(() => import("@/registry/default/example/upload-box-loading-state")),
      files: ["registry/default/example/upload-box-loading-state.tsx"],
    },
    "sidebar-demo": {
      name: "sidebar-demo",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-demo")),
      files: ["registry/default/example/sidebar-demo.tsx"],
    },
    "sidebar-configure-menus": {
      name: "sidebar-configure-menus",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-configure-menus")),
      files: ["registry/default/example/sidebar-configure-menus.tsx"],
    },
    "sidebar-anatomy-element": {
      name: "sidebar-anatomy-element",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-anatomy-element")),
      files: ["registry/default/example/sidebar-anatomy-element.tsx"],
    },
    "sidebar-default-expanded": {
      name: "sidebar-default-expanded",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-default-expanded")),
      files: ["registry/default/example/sidebar-default-expanded.tsx"],
    },
    "sidebar-controlled-expanded": {
      name: "sidebar-controlled-expanded",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-controlled-expanded")),
      files: ["registry/default/example/sidebar-controlled-expanded.tsx"],
    },
    "sidebar-custom-trigger": {
      name: "sidebar-custom-trigger",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-custom-trigger")),
      files: ["registry/default/example/sidebar-custom-trigger.tsx"],
    },
    "sidebar-selected-item": {
      name: "sidebar-selected-item",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-selected-item")),
      files: ["registry/default/example/sidebar-selected-item.tsx"],
    },
    "sidebar-footer-with-config": {
      name: "sidebar-footer-with-config",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-footer-with-config")),
      files: ["registry/default/example/sidebar-footer-with-config.tsx"],
    },
    "sidebar-collapsible": {
      name: "sidebar-collapsible",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-collapsible")),
      files: ["registry/default/example/sidebar-collapsible.tsx"],
    },
    "sidebar-layout-demo": {
      name: "sidebar-layout-demo",
      type: "components:example",
      registryDependencies: ["sidebar"],
      component: React.lazy(() => import("@/registry/default/example/sidebar-layout-demo")),
      files: ["registry/default/example/sidebar-layout-demo.tsx"],
    },
    "pagination-demo": {
      name: "pagination-demo",
      type: "components:example",
      registryDependencies: ["pagination"],
      component: React.lazy(() => import("@/registry/default/example/pagination-demo")),
      files: ["registry/default/example/pagination-demo.tsx"],
    },
    "pagination-disabled": {
      name: "pagination-disabled",
      type: "components:example",
      registryDependencies: ["pagination"],
      component: React.lazy(() => import("@/registry/default/example/pagination-disabled")),
      files: ["registry/default/example/pagination-disabled.tsx"],
    },
    "pagination-controlled": {
      name: "pagination-controlled",
      type: "components:example",
      registryDependencies: ["pagination"],
      component: React.lazy(() => import("@/registry/default/example/pagination-controlled")),
      files: ["registry/default/example/pagination-controlled.tsx"],
    },
    "pagination-custom-ranges": {
      name: "pagination-custom-ranges",
      type: "components:example",
      registryDependencies: ["pagination"],
      component: React.lazy(() => import("@/registry/default/example/pagination-custom-ranges")),
      files: ["registry/default/example/pagination-custom-ranges.tsx"],
    },
    "menu-option-demo": {
      name: "menu-option-demo",
      type: "components:example",
      registryDependencies: ["menu-option"],
      component: React.lazy(() => import("@/registry/default/example/menu-option-demo")),
      files: ["registry/default/example/menu-option-demo.tsx"],
    },
    "menu-option-custom-decorator": {
      name: "menu-option-custom-decorator",
      type: "components:example",
      registryDependencies: ["menu-option"],
      component: React.lazy(() => import("@/registry/default/example/menu-option-custom-decorator")),
      files: ["registry/default/example/menu-option-custom-decorator.tsx"],
    },
    "sorting-icon-demo": {
      name: "sorting-icon-demo",
      type: "components:example",
      registryDependencies: ["sorting-icon"],
      component: React.lazy(() => import("@/registry/default/example/sorting-icon-demo")),
      files: ["registry/default/example/sorting-icon-demo.tsx"],
    },
  },
}
