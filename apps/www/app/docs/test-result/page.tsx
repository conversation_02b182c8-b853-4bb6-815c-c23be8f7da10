"use client"

import { useEffect, useMemo, useState } from "react"
import { Accordion, Chip, Typography } from "@/components"
import { apolloTheme } from "@design-systems/tokens"
import { Chart, ChartConfiguration } from "chart.js/auto"
import { capitalize } from "lodash"
import componentUsage from "public/component-usage.json"
import testResult from "public/jest-results.json"

import componentReport from "../../../__registry__/component-report.json"

export default function TestResultPage() {
  const groupedTestResult = simplifyJestResults(testResult)

  const [tested, untest] = useMemo(() => {
    return Object.entries(componentReport)?.reduce(
      ([prevTested, prevUntest], [componentName]) => {
        const name = capitalize(componentName.replace("-", ""))
        const componentTestResult = groupedTestResult?.[name]
        const results = componentTestResult
        return results
          ? [prevTested + 1, prevUntest]
          : [prevTested, prevUntest + 1]
      },
      [0, 0]
    )
  }, [])

  return (
    <div className="w-full flex flex-col gap-3 justify-start items-start p-4">
      <div className="flex flex-row justify-around items-center gap-2 self-stretch">
        <div className="py-5 flex flex-row justify-center items-center">
          <UnitTestSummaryChart
            hasUnitTestCount={tested}
            noUnitTestCount={untest}
          />
        </div>
        <div className="py-5 flex flex-col justify-center items-start">
          <Typography level="h3">
            <b>Total Component:</b> {Object.entries(componentReport).length}
          </Typography>
          <Typography level="h3">
            <b>Unit-test Provided:</b> {tested}/
            {Object.entries(componentReport).length}
          </Typography>
        </div>
      </div>
      {Object.entries(componentReport)?.map(([componentName]) => {
        const name = capitalize(componentName.replace("-", ""))

        const results = groupedTestResult?.[name]
        const componentStat = componentUsage.summary["UI component"]?.find(
          (component) =>
            name === capitalize(component.component.replace("-", ""))
        )

        return (
          <div className="self-stretch flex flex-col gap-2 justify-start items-start">
            <Accordion
              expanded={false}
              header={
                <div className="flex flex-row gap-2 justify-start items-center">
                  <Typography level="h5">{name}</Typography>
                  {results && (
                    <Chip
                      label={
                        <Typography level="caption">✅ unit-test</Typography>
                      }
                      color="primary"
                    />
                  )}
                  <Chip label={`Usage: ${componentStat?.usage_count ?? 0}`} />
                </div>
              }
            >
              <div
                className="flex flex-col gap-2 justify-start items-start self-stretch"
                key={componentName}
              >
                <Typography level="body-1" className="font-bold underline">
                  Unit Test Results
                </Typography>
                <table className="w-full border-border-default border">
                  <thead>
                    <tr className="border border-border-default">
                      <th className="py-1 px-2 text-center border border-border-default"></th>
                      <th className="py-1 px-2 text-left border border-border-default">
                        Test Case
                      </th>
                      <th className="w-[100px] py-1 px-2 text-center border border-border-default">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {results ? (
                      results.map((result, index) => (
                        <tr key={result.testName}>
                          <td className="py-1 px-2 text-center border border-border-default">
                            {index + 1}
                          </td>
                          <td className="py-1 px-2 text-left border border-border-default">
                            {result.testName}
                          </td>
                          <td className="py-1 px-2 text-center border border-border-default">
                            {result.status === "passed" ? "✅" : "🚨"}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td className="text-center p-4" colSpan={3}>
                          No Unit-test Provided
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </Accordion>
          </div>
        )
      })}
    </div>
  )
}
type TestCase = {
  testName: string
  status: string
}

type SimplifiedResults = Record<string, TestCase[]>

// Function to read and simplify Jest results grouped by `ancestorTitles`
function simplifyJestResults(data: any): SimplifiedResults {
  const report: SimplifiedResults = {}

  data.testResults?.forEach((suite: any) => {
    suite.assertionResults.forEach((testCase: any) => {
      const ancestorTitles = capitalize(
        testCase.ancestorTitles.join(" > ").replace(/[\/><]|\s|Component/g, "")
      ) // Combine ancestor titles
      const testName = testCase.title
      const status = testCase.status // Passed, Failed, or Skipped

      // Initialize the group by ancestor title if not already present
      if (!report[ancestorTitles]) {
        report[ancestorTitles] = []
      }

      // Add the test case data
      report[ancestorTitles].push({
        testName,
        status,
      })
    })
  })

  return report
}

function UnitTestSummaryChart({
  hasUnitTestCount,
  noUnitTestCount,
}: {
  hasUnitTestCount: number
  noUnitTestCount: number
}) {
  return (
    <BaseChart
      options={{
        type: "doughnut",
        data: {
          labels: ["Unit-test", "No Unit-test"],
          datasets: [
            {
              data: [hasUnitTestCount, noUnitTestCount],
              backgroundColor: [
                apolloTheme.colors["cjx-colors-content-primary-default"],
                apolloTheme.colors["cjx-colors-content-warning-default"],
              ],
              hoverOffset: 4,
            },
          ],
        },
      }}
    />
  )
}

function BaseChart({ options }: { options: ChartConfiguration }) {
  const [chartRef, setChartRef] = useState<HTMLCanvasElement | null>(null)

  useEffect(() => {
    if (chartRef) {
      new Chart(chartRef, options)
    }
  }, [chartRef])

  return <canvas ref={setChartRef} />
}
