import "@apollo/ui/style.css"
import "@/styles/globals.css"

import { Metadata, Viewport } from "next"
import {
  <PERSON><PERSON>rovider as ApolloThemeProvider,
  createTheme,
} from "@design-systems/apollo-ui"
import NextTopLoader from "nextjs-toploader"

import { siteConfig } from "@/config/site"
import { fontThai } from "@/lib/fonts"
import { cn } from "@/lib/utils"
import { ThemeProvider } from "@/components/providers"
import { TailwindIndicator } from "@/components/tailwind-indicator"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { Toaster as SonnerToaster } from "@/registry/default/ui/sonner"
import { Toaster as DefaultToaster } from "@/registry/default/ui/toaster"

import ReactQueryProvider from "../providers/ReactQueryProvider"

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  metadataBase: new URL(siteConfig.url),
  description: siteConfig.description,
  keywords: ["Next.js", "React", "Tailwind CSS"],
  authors: [
    {
      name: "",
      url: "https://example.com",
    },
  ],
  creator: "",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: "@",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: `${siteConfig.url}/site.webmanifest`,
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
}

const defaultTheme = createTheme()

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <>
      <html lang="en" suppressHydrationWarning>
        <head />
        <body
          className={cn(
            `min-h-screen bg-background font-sans antialiased`,
            fontThai.className
          )}
        >
          <ReactQueryProvider>
            <NextTopLoader
              color="#000"
              showAtBottom={false}
              showSpinner={false}
            />
            <ApolloThemeProvider theme={defaultTheme}>
              <ThemeProvider
                attribute="class"
                defaultTheme="light"
                enableSystem
                disableTransitionOnChange
              >
                {children}
                <TailwindIndicator />
                <ThemeSwitcher />
                <SonnerToaster />
                <DefaultToaster />
              </ThemeProvider>
            </ApolloThemeProvider>
          </ReactQueryProvider>
        </body>
      </html>
    </>
  )
}
