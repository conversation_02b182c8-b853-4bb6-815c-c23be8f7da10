"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@design-systems/apollo-ui"

import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from "@/components/page-header"
import Playground from "@/components/playground"
import { SiteHeader } from "@/components/site-header"

export default function IndexPage() {
  return (
    <>
      <SiteHeader />
      <div className="container relative h-[calc(100vh_-_3.5rem)] [&>.ApolloDoc-header]:h-full [&>.ApolloDoc-header]:pt-8 pb-8">
        <PageHeader className="justify-center">
          {process.env.NODE_ENV === "production" ? (
            <>
              <PageHeaderHeading>Elevate Your UI with Ease!</PageHeaderHeading>
              <PageHeaderDescription>
                Accessible, Comprehensive, Effortless Design ✨🚀✨🚀
              </PageHeaderDescription>
              <Button className="bg-black text-white hover:bg-gray-60 mt-8">
                <Link href={"/docs"}>Get Started</Link>
              </Button>
            </>
          ) : (
            <>
              <div className="inline-flex items-center rounded-lg bg-muted px-3 py-1 text-sm font-medium">
                <span className="sm:hidden">dev playground</span>
                <span className="hidden sm:inline">
                  playground for developing component
                </span>
              </div>
              <span className="text-xs flex flex-col justify-center items-center">
                <span className="flex space-x-2">
                  <p>put component you're developing here </p>
                  <b>
                    (do not forget to export component from '/ui/src/index.ts')
                  </b>
                </span>
                <p>any changes will magically get updated.</p>
              </span>
              <Playground />
            </>
          )}
        </PageHeader>
      </div>
    </>
  )
}
