"use client"

import { NavBar } from "@/components"
import { Shop, Shopping, Solution } from "@design-systems/apollo-icons"
import { Home, User } from "lucide-react"

import { PageHeader } from "@/components/page-header"
import Playground from "@/components/playground"

export default function PlaygroundPage() {
  return (
    <div className="container relative h-[calc(100vh_-_3.5rem)] [&>.ApolloDoc-header]:h-full [&>.ApolloDoc-header]:pt-8 pb-8">
      <PageHeader className="justify-center">
        <>
          <div className="inline-flex items-center rounded-lg bg-muted px-3 py-1 text-sm font-medium">
            <span className="hidden sm:inline">
              playground for trying the components
            </span>
          </div>
          <span className="text-xs flex flex-col justify-center items-center">
            <p>try changing props to see how it works</p>
          </span>
          <Playground />
        </>
      </PageHeader>
    </div>
  )
}
