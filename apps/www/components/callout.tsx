import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/registry/default/ui/alert"

interface CalloutProps {
  icon?: string
  title?: string
  slotClass?: string
  children?: React.ReactNode
}

export function Callout({ title, slotClass, children, icon, ...props }: CalloutProps) {
  return (
    <Alert {...props}>
      {icon && <span className="mr-4 text-2xl">{icon}</span>}
      {title && <AlertTitle>{title}</AlertTitle>}
      <AlertDescription className={slotClass}>{children}</AlertDescription>
    </Alert>
  )
}
