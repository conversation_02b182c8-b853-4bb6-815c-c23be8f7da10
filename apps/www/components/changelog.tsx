"use client"

import React from "react"
import { Typography } from "@design-systems/apollo-ui"
import { format } from "date-fns"
import Markdown from "react-markdown"

import { useApolloRelease } from "@/hooks/api/useApolloRelease"

import { markdownComponents } from "./mdx-components"

export function Changelog() {
  const { releases, isLoading, isError } = useApolloRelease()

  return (
    <>
      {isLoading ? (
        <div className="p-6 flex flex-row justify-center items-center">
          <Typography level="body-2">Loading...</Typography>
        </div>
      ) : isError ? (
        <div className="p-6 flex flex-row justify-center items-center">
          <Typography level="body-2">
            Something went wrong. Please retry again.
          </Typography>
        </div>
      ) : (
        releases?.map((release) => (
          <div
            key={release.tag_name}
            className="flex flex-col justify-start items-start border-b [&_ul]:my-0 py-4"
          >
            <Typography level="h3">{release.name}</Typography>
            <Typography level="caption">
              <a href={release.commit.web_url}>
                Commit: {release.commit.short_id}
              </a>
            </Typography>
            <Typography level="caption">
              Released at:{" "}
              {format(new Date(release.released_at), "dd/MM/yyyy HH:mm:ss")}
            </Typography>
            <Markdown components={markdownComponents}>
              {release.description}
            </Markdown>
          </div>
        ))
      )}
    </>
  )
}
