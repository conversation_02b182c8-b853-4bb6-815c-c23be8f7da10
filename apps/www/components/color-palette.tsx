import React from "react"
import { apolloTailwindConfig, apolloTheme } from "@design-systems/tokens"
import clsx from "clsx"
import { flatten } from "flat"

const { colors: tokenColors } = apolloTailwindConfig
const colors = JSON.parse(JSON.stringify(tokenColors))
const hexCodeColors = apolloTheme.colors

const coreColors = ["brand", "gray", "danger", "warning", "success", "process"]
const aliasColors = Object.keys(colors).filter(
  (color) => coreColors.indexOf(color) == -1
)

const cores: { [key: string]: { [key: number]: string } } = coreColors.reduce(
  (o, key) => Object.assign(o, { [key]: colors[key] }),
  {}
)
const alias = aliasColors.reduce(
  (o, key) => Object.assign(o, { [key]: colors[key] }),
  {}
)

type TokenType = "core" | "alias"

type PaletteRowType = {
  tokens: { [key: string]: { [_key: number]: string } }
  tokenKey: string
  type?: string
  forceRow?: number
}

type PaletteCardType = PaletteRowType & {
  k?: number
}

const PaletteRow = ({
  tokens,
  tokenKey,
  type = "core",
  forceRow,
}: PaletteRowType) => {
  if (typeof tokens[tokenKey] === "string") {
    return (
      <PaletteCard
        tokens={tokens}
        tokenKey={tokenKey}
        type={type}
        forceRow={forceRow}
      />
    )
  }

  if (
    typeof tokens[tokenKey] === "object" &&
    typeof tokens[tokenKey] !== null
  ) {
    return (
      <div className="2xl:contents">
        <div className="text-sm font-semibold text-slate-900 dark:text-slate-200 2xl:col-end-1 2xl:pt-2.5">
          {tokenKey}
        </div>
        <div className="2xl:contents">
          <div
            className={clsx(
              "grid mt-3 grid-cols-1  gap-y-3 gap-x-2 sm:mt-2 2xl:mt-0",
              {
                "sm:grid-cols-12": forceRow === undefined,
                "sm:grid-cols-2": forceRow,
              }
            )}
          >
            {Object.keys(tokens[tokenKey]).map((k: any, i) => {
              return (
                <PaletteRow
                  tokens={tokens[tokenKey]}
                  tokenKey={k}
                  key={i}
                  type={type}
                  forceRow={forceRow}
                />
              )
            })}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {Object.keys(tokens[tokenKey]).map((k: any, i) => (
        <PaletteCard
          tokens={tokens}
          tokenKey={tokenKey}
          k={k}
          key={i}
          type={type}
          forceRow={forceRow}
        />
      ))}
    </>
  )
}

const PaletteCard = ({
  tokens,
  tokenKey,
  k,
  type,
  forceRow,
}: PaletteCardType) => {
  const token = k ? tokens[tokenKey][k] : tokens[tokenKey]
  return (
    <div className="relative flex">
      <div className="flex items-center gap-x-3 w-full sm:block sm:space-y-1.5">
        <div
          className={clsx(
            "rounded dark:ring-1 dark:ring-inset dark:ring-white/10 ",
            {
              "rounded-full h-8 w-8": type === "alias",
              "rounded h-10 w-10 sm:w-full": type === "core",
              "border border-gray-30 border-dashed": `${token}` === "#FFFFFF",
            }
          )}
          style={{
            backgroundColor: `${token}`,
          }}
        ></div>
        <div className="px-0.5">
          <div className="w-6 font-medium text-xs text-slate-900 2xl:w-full dark:text-white">
            {k ? k : tokenKey}
          </div>
          <div className="text-slate-500 text-xs font-mono lowercase dark:text-slate-400 sm:text-[0.625rem] md:text-xs lg:text-[0.625rem] 2xl:text-xs">
            {`${token}`}
          </div>
          <div className="text-slate-500 text-xs font-mono lowercase dark:text-slate-400 sm:text-[0.625rem] md:text-xs lg:text-[0.625rem] 2xl:text-xs">
            {hexCodeColors?.[
              (token as string).replace(
                /^var\(--(.*)\)/g,
                "$1"
              ) as keyof typeof hexCodeColors
            ]?.toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  )
}

export function ColorPalette({ type }: { type: TokenType }) {
  return (
    <div className="overflow-y-scroll">
      <div className="grid gap-x-2 gap-y-8 sm:grid-cols-1">
        {Object.keys(cores).map((tokenKey, index) => (
          <PaletteRow tokens={cores} tokenKey={tokenKey} key={index} />
        ))}
      </div>
    </div>
  )
}

// export function ColorPaletteAlias() {
//   return (
//     <div className="overflow-y-scroll">
//       <div className="grid gap-x-2 gap-y-8 grid-cols-1">
//         {Object.keys(alias).map((tokenKey, index) => (
//           <PaletteRow
//             tokens={alias}
//             tokenKey={tokenKey}
//             key={index}
//             type="alias"
//             forceRow={1}
//           />
//         ))}
//       </div>
//     </div>
//   )
// }

export function ColorPaletteAlias(): ReturnType<React.FC> {
  const aliasTokens: any = flatten(alias, { delimiter: "-" })
  return (
    <>
      {Object.keys(aliasTokens).map((tokenKey: string) => (
        <div className="relative flex">
          <div className="flex items-center gap-x-3 w-full sm:block sm:space-y-1.5">
            <div
              className={clsx(
                "dark:ring-1 rounded-full h-8 w-8 dark:ring-inset dark:ring-white/10",
                {
                  "border border-gray-40 border-dashed":
                    `${aliasTokens[tokenKey]}` === "#FFFFFF",
                }
              )}
              style={{ backgroundColor: `${aliasTokens[tokenKey]}` }}
            ></div>
            <div className="px-0.5">
              <div className="font-medium text-xs text-slate-900 2xl:w-full dark:text-white">
                {tokenKey}
              </div>
              <div className="text-slate-500 text-xs font-mono lowercase dark:text-slate-400 sm:text-[0.625rem] md:text-xs lg:text-[0.625rem] 2xl:text-xs">
                {`${aliasTokens[tokenKey]}`}{" "}
              </div>
              <div className="text-slate-500 text-xs font-mono dark:text-slate-400 sm:text-[0.625rem] md:text-xs lg:text-[0.625rem] 2xl:text-xs">
                {hexCodeColors?.[
                  aliasTokens[tokenKey].replace(
                    /^var\(--(.*)\)/g,
                    "$1"
                  ) as keyof typeof hexCodeColors
                ]?.toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
