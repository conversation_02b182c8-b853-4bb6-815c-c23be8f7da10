import { icons } from "@design-systems/apollo-icons"
import { Button, Typography } from "@design-systems/apollo-ui"

import { useToast } from "@/registry/default/ui/use-toast"

export function IconCollectionsComponent() {
  const { toast, dismiss } = useToast()
  return (
    <div className="grid sm:grid-cols-6 gap-4 sm:gap-6 my-6">
      {Object.entries(icons).map(([iconName, Icon], i) => {
        return (
          <div
            key={iconName}
            className="relative cursor-pointer group flex w-full flex-col items-center rounded-xl border bg-card p-6 text-card-foreground  transition-colors hover:bg-muted/50 sm:p-10"
            onClick={() => {
              toast({
                title: `icon: <${iconName} />`,
                description: "copied to clipboard",
                action: (
                  <Button size="md" onClick={() => dismiss()}>
                    close
                  </Button>
                ),
              })
              navigator.clipboard.writeText(
                `import { ${iconName} } from "@design-systems/apollo-icons"`
              )
            }}
          >
            <Typography
              level="caption"
              className="absolute bottom-0 mb-4 text-content-placeholder group-hover:text-black"
            >
              {iconName}
            </Typography>
            <Icon />
          </div>
        )
      })}
    </div>
  )
}
