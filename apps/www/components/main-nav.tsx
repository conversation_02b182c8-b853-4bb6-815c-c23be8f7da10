"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Option, Select } from "@/components"
import pkg from "@design-systems/apollo-ui/package.json"

import { siteConfig } from "@/config/site"
import { cn } from "@/lib/utils"
import { Icons } from "@/components/icons"

export function MainNav() {
  const pathname = usePathname()

  return (
    <div className="mr-4 hidden md:flex gap">
      <nav className="ml-4 flex items-center gap-4 text-sm">
        <Link
          href="/docs"
          className={cn(
            "transition-colors hover:text-foreground/80",
            pathname === "/docs" ? "text-foreground" : "text-foreground/60"
          )}
        >
          Docs
        </Link>
        <Link
          href="/playground"
          className={cn(
            "transition-colors hover:text-foreground/80",
            pathname === "/playground"
              ? "text-foreground"
              : "text-foreground/60"
          )}
        >
          Playground
        </Link>
      </nav>
    </div>
  )
}
