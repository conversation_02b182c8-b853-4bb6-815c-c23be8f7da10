import React, { useEffect, useRef, useState } from "react"
import * as UI from "@design-systems/apollo-ui"
import {
  LiveEditor,
  LiveError,
  LivePreview,
  LiveProvider,
  withLive,
} from "react-live"

const MIN_WIDTH = 100

const Live = ({ onEdit }: { onEdit: (_: string) => void }) => {
  const [isDragging, setIsDragging] = useState(false)
  const [xPosition, setXPosition] = useState<number>()
  const [leftWidth, setLeftWidth] = useState<number>()
  const splitPaneRef = useRef<any>()

  useEffect(() => {
    if (splitPaneRef.current) {
      if (!leftWidth) {
        setLeftWidth(splitPaneRef.current.firstChild.clientWidth)
      }
    }
  }, [splitPaneRef])

  useEffect(() => {
    document.addEventListener("mousemove", onMouseMove)
    document.addEventListener("mouseup", onMouseUp)
    return () => {
      document.removeEventListener("mousemove", onMouseMove)
      document.removeEventListener("mouseup", onMouseUp)
    }
  })

  const onMouseUp = () => {
    setIsDragging(false)
  }
  const onMouseDown = (e: any) => {
    setXPosition(e.clientX)
    setIsDragging(true)
  }

  const onMouseMove = (e: any) => {
    if (isDragging && leftWidth && xPosition) {
      const newLeftWidth = leftWidth + e.clientX - xPosition
      setXPosition(e.clientX)
      if (newLeftWidth < MIN_WIDTH) {
        setLeftWidth(MIN_WIDTH)
        return
      }
      if (splitPaneRef.current) {
        const splitPaneWidth = splitPaneRef.current.clientWidth
        if (newLeftWidth > splitPaneWidth - MIN_WIDTH) {
          setLeftWidth(splitPaneWidth - MIN_WIDTH)
          return
        }
      }
      setLeftWidth(newLeftWidth)
    }
  }

  return (
    <>
      <div
        ref={splitPaneRef}
        className="hidden w-full md:flex relative grow overflow-hidden rounded-lg"
      >
        <div className="flex flex-col">
          <LiveEditor
            onChange={onEdit}
            style={{
              width: leftWidth + "px",
              fontFamily: "monospace",
            }}
            className={`${
              isDragging ? "pointer-events-none select-none" : ""
            } overflow-auto [&>.prism-code]:h-auto [&>.prism-code]:min-h-full [&>.prism-code]:rounded-none flex-1`}
          />
          <LiveError className="text-error bg-gray-30 p-4" />
        </div>
        <div
          onMouseDown={onMouseDown}
          className={`${
            isDragging ? "cursor-col-resize" : "cursor-ew-resize"
          } w-1 bg-secondary`}
        ></div>
        <LivePreview
          className={`${
            isDragging ? "pointer-events-none select-none" : ""
          }flex flex-1 overflow-auto border border-outline p-2`}
        />
      </div>
      <div className="grid md:hidden">
        <LiveEditor
          onChange={onEdit}
          className="overflow-auto [&>.prism-code]:h-full [&>.prism-code]:rounded-none"
        />
        <LiveError className="text-error" />
        <LivePreview className="flex-1 overflow-auto border border-outline p-2" />
      </div>
    </>
  )
}

const LiveComponent = withLive(Live)

const Playground = () => {
  const [code, setCode] = useState(`// example, no need to import component
  <div className="w-full space-y-2">
    <Typography level="h3">please, refer to the documents
    for more informations </Typography>

    <Button fullWidth>Button</Button>
    <Button fullWidth color="danger">Button</Button>
    <Button disabled fullWidth>Button</Button>

    <DatePicker />
    <DatePicker disableDayPicker/>

    <div className="flex justify-between">
      <Button variant="outline">Button</Button>
      <Button variant="plain">Button</Button>
      <Button color="danger">Button</Button>
      <Button variant="outline" color="danger">Button</Button>
      <Button variant="outline" color="danger">Button</Button>
    </div>

    <Breadcrumbs aria-label="breadcrumb">
      <a href="/">Apollo</a>
      <a href="/material-ui/getting-started/installation/">
          Core
      </a>
      <Typography>
        Breadcrumbs
      </Typography>
     </Breadcrumbs>

    <Input variant="outline" />
    <Input variant="outline" color="danger" />
    <Input disabled />

    <Switch defaultChecked label="Label" />
    <Toast
      open={true}
      title="Bottom-Right"
      description="This is info toast"
      position="bottom-right"
      severity="info"
    />
    <Toast
      open={true}
      title="Bottom-Center"
      description="This is error toast"
      position="bottom-center"
      severity="error"
    />
    <Toast
      open={true}
      title="Bottom-Left"
      description="This is warning toast"
      position="bottom-left"
      severity="warning"
    />
    <div className="flex justify-between">
      <Chip truncatedTextWidth={30} label="Default"
       onDelete={()=>{}} />
      <Chip startDecorator={2} label="Primary"
       rounded="full" color="primary" />
      <Chip label="Success" color="success" />
      <Chip label="Warning" color="warning" />
      <Chip label="Danger" color="danger" />
    </div>
    <Alert color="success" title="This master component"
    description="Type your description."/>

    <Tabs defaultValue={0}>
      <TabsList variant="scrollable" scrollButtons>
        <Tab value={0}>Tab 1</Tab>
        <Tab value={1}>Tab 2</Tab>
        <Tab value={2}>Tab 3</Tab>
        <Tab value={3}>Tab 4</Tab>
        <Tab value={4} disabled>Tab 5</Tab>
      </TabsList>

      <TabPanel value={0}>Page for Tab 1</TabPanel>
      <TabPanel value={1}>Page for Tab 2</TabPanel>
      <TabPanel value={2}>Page for Tab 3</TabPanel>
      <TabPanel value={3}>Page for Tab 4</TabPanel>
      <TabPanel value={4}>Page for Tab 5</TabPanel>

    </Tabs>

    <Checkbox label="Top" labelPlacement="top"/>
    <Checkbox label="Left" labelPlacement="left"/>
    <Checkbox label="Right" labelPlacement="right"/>
    <Checkbox label="Bottom" labelPlacement="bottom"/>
    <Checkbox label="Label" checked />
    <Checkbox label="Label" defaultChecked />
    <Checkbox label="Label indeterminate without checked" indeterminate />
    <Checkbox label="Label indeterminate with checked" indeterminate checked />
    
    <Radio value="Foo" defaultChecked />
    <Radio value="Foo" checked />
    <Radio value="Foo" />
    <Radio label="Checked" value="Foo" checked />
    <Radio label="Uncheck" value="Foo"/>
    
    <Accordion header="Long titleLong titleLong titleLong titleLong title">Content</Accordion>
  </div>
`)

  useEffect(() => {
    document.title = "Playground" + " | @design-systems/apollo-ui"
    const hash = location.hash.slice(1)
    const decoded = atob(hash)
    if (decoded) {
      setCode(decoded)
    }
  }, [])

  return (
    <LiveProvider code={code} scope={UI}>
      <UI.Alert
        color="error"
        title="Announcement"
        description="This playground is not up to date. New version coming soon."
      />
      <div className="rounded-t flex items-center justify-between">
        <span className="flex-1"></span>
        <span className="flex-1"></span>
      </div>
      <LiveComponent onEdit={setCode} />
    </LiveProvider>
  )
}

export default Playground
