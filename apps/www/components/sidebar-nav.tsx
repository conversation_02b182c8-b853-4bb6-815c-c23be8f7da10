"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Typography } from "@/components"
import { SidebarNavItem } from "types/nav"

import { cn } from "@/lib/utils"

export interface DocsSidebarNavProps {
  items: SidebarNavItem[]
}

export function DocsSidebarNav({ items }: DocsSidebarNavProps) {
  const pathname = usePathname()

  return items.length ? (
    <div className="w-full">
      {items.map((item, index) => (
        <div key={index} className={cn("pb-4")}>
          <div className="flex flex-row justify-between w-full items-center">
            <Typography level="h4" className="mb-1 rounded-md px-2 py-1">
              {item.title}
            </Typography>
            {item.labelVersion && (
              <span className="bg-surface-static-success-default text-surface-action-primary-default text-xs font-medium ms-2 px-2 rounded dark:bg-blue-900 dark:text-blue-300">
                {item.labelVersion}
              </span>
            )}
          </div>

          {item?.items?.length && (
            <DocsSidebarNavItems items={item.items} pathname={pathname} />
          )}
        </div>
      ))}
    </div>
  ) : null
}

interface DocsSidebarNavItemsProps {
  items: SidebarNavItem[]
  pathname: string | null
}

export function DocsSidebarNavItems({
  items,
  pathname,
}: DocsSidebarNavItemsProps) {
  return items?.length ? (
    <div className="grid grid-flow-row auto-rows-max text-sm">
      {items.map((item, index) =>
        item.items?.length > 0 ? (
          <div className="px-2 flex flex-col justify-start items-start">
            <Typography
              level="body-2"
              className="text-content-description font-bold my-1 mx-2"
            >
              {item.title}
            </Typography>
            <DocsSidebarNavItems items={item.items} pathname={null} />
          </div>
        ) : item.href && !item.disabled ? (
          <Link
            key={index}
            href={item.href}
            className={cn(
              "group flex w-full items-center rounded-md border border-transparent mx-4 py-1 hover:underline",
              item.disabled && "cursor-not-allowed opacity-60",
              pathname === item.href
                ? "font-medium text-foreground"
                : "text-muted-foreground"
            )}
            target={item.external ? "_blank" : ""}
            rel={item.external ? "noreferrer" : ""}
          >
            {item.title}
            {item.label && (
              <span className="ml-2 rounded-md bg-black text-white whitespace-nowrap px-1.5 py-0.5 text-xs leading-none no-underline group-hover:no-underline">
                {item.label}
              </span>
            )}
            {item.labelVersion && (
              <span className="bg-surface-static-success-default text-surface-action-primary-default text-xs font-medium ms-2 px-2.5 rounded dark:bg-blue-900 dark:text-blue-300">
                {item.labelVersion}
              </span>
            )}
          </Link>
        ) : (
          <span
            key={index}
            className={cn(
              "flex w-full cursor-not-allowed items-center rounded-md p-2 text-muted-foreground hover:underline",
              item.disabled && "cursor-not-allowed opacity-60"
            )}
          >
            {item.title}
            {item.label && (
              <span className="ml-2 rounded-md bg-muted px-1.5 py-0.5 text-xs leading-none text-muted-foreground no-underline group-hover:no-underline">
                {item.label}
              </span>
            )}
          </span>
        )
      )}
    </div>
  ) : null
}
