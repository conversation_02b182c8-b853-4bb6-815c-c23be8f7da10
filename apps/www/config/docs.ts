import pkg from "@design-systems/apollo-icons/package.json"
import uiPkg from "@design-systems/apollo-ui/package.json"
import tokenPkg from "@design-systems/tokens/package.json"
import type { MainNavItem, SidebarNavItem } from "types/nav"

interface DocsConfig {
  mainNav: MainNavItem[]
  sidebarNav: SidebarNavItem[]
}

export const docsConfig: DocsConfig = {
  mainNav: [
    {
      title: "Documentation",
      href: "/docs",
    },
  ],
  sidebarNav: [
    {
      title: "Getting Started",
      items: [
        {
          title: "Introduction",
          href: "/docs",
          items: [],
        },
        {
          title: "Quick Start",
          href: "/docs/quick-start",
          items: [],
        },

        {
          title: "Changelog",
          href: "/docs/changelog",
          items: [],
        },
        {
          title: "Contribution",
          href: "/docs/development",
          items: [],
        },
        {
          title: "Theming",
          href: "/docs/#",
          labelVersion: "coming soon",
          items: [],
        },
      ],
    },
    {
      title: "Components",
      labelVersion: `v${uiPkg.version}`,
      items: [
        {
          title: "Inputs",
          items: [
            {
              title: "Autocomplete",
              href: "/docs/components/autocomplete",
              items: [],
              label: "",
            },
            {
              title: "Button",
              href: "/docs/components/button",
              items: [],
              label: "",
            },
            {
              title: "Icon Button",
              href: "/docs/components/icon-button",
              items: [],
              label: "",
            },
            {
              title: "Float Button",
              href: "/docs/components/float-button",
              items: [],
              label: "",
            },
            {
              title: "Input",
              href: "/docs/components/input",
              items: [],
              label: "",
            },
            {
              title: "Select",
              href: "/docs/components/select",
              items: [],
              label: "",
            },
            {
              title: "DatePicker",
              href: "/docs/components/datepicker",
              items: [],
              labelVersion: "Deprecated",
              label: "",
            },
            {
              title: "DateInput",
              href: "/docs/components/dateinput",
              items: [],
              label: "",
            },
            {
              title: "Checkbox",
              href: "/docs/components/checkbox",
              items: [],
              label: "",
            },
            {
              title: "Radio",
              href: "/docs/components/radio",
              items: [],
              label: "",
            },
            {
              title: "Switch",
              href: "/docs/components/switch",
              items: [],
              label: "",
            },
            {
              title: "Upload Box",
              href: "/docs/components/upload-box",
              items: [],
              label: "",
            },
            // {
            //   title: "Menu Option",
            //   href: "/docs/components/menu-option",
            //   items: [],
            //   label: "",
            // },
          ],
        },
        {
          title: "Data Display",
          items: [
            {
              title: "Typography",
              href: "/docs/components/typography",
              items: [],
              label: "",
            },
            {
              title: "Icon",
              href: "/docs/components/icon",
              items: [],
              label: "",
            },
            {
              title: "Product Card",
              href: "/docs/components/product-card",
              items: [],
              label: "",
            },
            {
              title: "Chip",
              href: "/docs/components/chip",
              items: [],
              label: "",
            },
            {
              title: "Sorting Icon",
              href: "/docs/components/sorting-icon",
              items: [],
              label: "",
            },
          ],
        },
        {
          title: "Feedback",
          items: [
            {
              title: "Toast",
              href: "/docs/components/toast",
              items: [],
              label: "",
            },
            {
              title: "Alert",
              href: "/docs/components/alert",
              items: [],
              label: "",
            },
            {
              title: "Modal",
              href: "/docs/components/modal",
              items: [],
              label: "",
            },
          ],
        },
        {
          title: "Navigation",
          items: [
            {
              title: "Breadcrumbs",
              href: "/docs/components/breadcrumbs",
              items: [],
              label: "",
            },
            {
              title: "NavBar",
              href: "/docs/components/navbar",
              items: [],
              label: "",
            },
            {
              title: "Sidebar",
              href: "/docs/components/sidebar",
              items: [],
              label: "",
            },
            {
              title: "Pagination",
              href: "/docs/components/pagination",
              items: [],
              label: "",
            },
          ],
        },
        {
          title: "Layout",
          items: [
            {
              title: "Accordion",
              href: "/docs/components/accordion",
              items: [],
              label: "",
            },
            {
              title: "Tabs",
              href: "/docs/components/tabs",
              items: [],
              label: "",
            },
            {
              title: "Capsule Tab",
              href: "/docs/components/capsule-tab",
              items: [],
              label: "",
            },
          ],
        },
      ],
    },
    {
      title: "Icons",
      labelVersion: `v${pkg.version}`,
      items: [
        {
          title: "Introduction & Library",
          href: "/docs/icons/introduction",
          items: [],
        },
        {
          title: "Contribution",
          href: "/docs/icons/development",
          items: [],
        },
      ],
    },
    {
      title: "Design Tokens",
      labelVersion: `v${tokenPkg.version}`,
      items: [
        {
          title: "Introduction",
          href: "/docs/tokens/introduction",
          items: [],
        },
        {
          title: "Colors",
          href: "/docs/tokens/colors",
          items: [],
        },
        {
          title: "Contribution",
          href: "/docs/tokens/development",
          items: [],
        },
      ],
    },
  ],
}
