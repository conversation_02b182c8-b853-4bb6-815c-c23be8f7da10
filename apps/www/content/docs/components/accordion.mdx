---
title: Accordion
description: The Accordion component lets users show and hide sections of related content on a page.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Accordion/AccordionProps.ts
---

<ComponentPreviewUI name="accordion-demo" />

## Usage

```tsx
import { Accordion } from "@design-systems/apollo-ui"

function MyFeature() {
  return <Accordion header="Title">Content</Accordion>
}
```

## Props

| Name                | Type                          | Required | Default | Description                                                                                                 |
| ------------------- | ----------------------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------- |
| **`header`**        | `ReactNode`                   | ✅       | -       | The header of the accordion. When a component is passed instead of a string, default styles will not apply. |
| **`icon`**          | `ReactNode`                   |          | -       | Custom icon. The expanding state won't be applied. Needs self-control.                                      |
| **`disabled`**      | `bool`                        |          | -       | If `true`, the accordion will be collapsed, hiding its content.                                             |
| **`expanded`**      | `bool`                        |          | `true`  | If `true`, the component is initially expanded. If `onStateChange` is set, this prop will be controlled.    |
| **`onStateChange`** | `(expanded: boolean) => void` |          | -       | Event handler for when the expanding state changes. If set, the component becomes controlled.               |
| **`iconPosition`**  | `"start" \| "end"`            |          | `"end"` | Position of the icon in the accordion header.                                                               |
| **`borderless`**    | `bool`                        |          | -       | If `true`, the accordion will not have a border.                                                            |

## Examples

### Default

By default, the accordion will be an uncontrolled component and will function correctly without any expanded state.

<ComponentPreviewUI name="accordion-demo" />

### Disabled

When `disabled` is true, the accordion will be collapsed.

<ComponentPreviewUI name="accordion-disabled" />

### Controlled

You can control the expanding state by passing the onStateChange prop, making the component becomes controlled.

<ComponentPreviewUI name="accordion-controlled" />

### Customization

You can customize the accordion by passing your own header and icon components, controlling the expanded state, and using props like `iconPosition` and `borderless` to adjust its appearance and behavior.

<ComponentPreviewUI name="accordion-custom" />

## CSS

| Class Name                | Description                          |
| ------------------------- | ------------------------------------ |
| `.ApolloAccordion-root`   | Styles applied to the root container |
| `.ApolloAccordion-header` | Styles applied to the header         |
| `.ApolloAccordion-body`   | Styles applied to the body           |
| `.ApolloAccordion-icon`   | Styles applied to the icon           |
