---
title: Alert
description: Displays a alert component.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Alert/AlertProps.ts
---

<ComponentPreviewUI name="alert-demo" />

## Usage

```tsx
import { Alert } from "@design-systems/apollo-ui"
```

## Props

| Name                 | Type                                        | Default | Description                       |
| -------------------- | ------------------------------------------- | ------- | --------------------------------- |
| **`color`**          | `'success'`,`'info'`,`'warning'`,`'danger'` | `info`  | The color of the component.       |
| **`fullwidth`**      | `bool`                                      | `-`     | is prop change width.             |
| **`title`**          | `ReactNode`                                 | `-`     | is prop title.                    |
| **`description`**    | `ReactNode`                                 | `-`     | is prop description.              |
| **`startDecorator`** | `ReactNode`                                 | `-`     | is prop startIcon can change.     |
| **`endDecorator`**   | `ReactNode`                                 | `-`     | is prop endIcon can add more.     |
| **`onClose`**        | `ReactEventHandler`                         | `-`     | is delete icon can add function.  |
| **`...other`**       | `Div`                                       | `-`     | The div property of the component |

## CSS classes

| Class Name                | Rule name | Description                      |
| ------------------------- | --------- | -------------------------------- |
| `.ApolloAlert-title`      | Root      | Styles applied to the title      |
| `.ApolloAlert-desription` | Label     | Styles applied to the desciption |
