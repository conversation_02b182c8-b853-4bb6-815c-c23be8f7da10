---
title: Autocomplete
description: The Autocomplete component lets users choose from a list of options as they type. It supports multiple selections, custom filters, and dynamic loading, making it great for forms with large datasets.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Autocomplete/AutocompleteProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8945-1291&node-type=CANVAS&m=dev
---

> Note: This component is currently in beta. While we have tested it thoroughly, users might still encounter some issues due to its complexity. If we're facing any issue please feel free to contact the team

<ComponentPreviewUI name="autocomplete-demo" />

## Usage

```tsx
import { useState } from "react"
import { Autocomplete, AutocompleteOption } from "@design-systems/apollo-ui"

export default function AutocompleteDemo() {
  const [singleValue, setSingleValue] = useState<AutocompleteOption | null>()
  const [value, setValue] = useState<AutocompleteOption[]>([])

  return (
    <div className="w-full flex flex-col gap-2 justify-start items-center p-4">
      <Autocomplete
        placeholder="Search"
        label="Single"
        helperText="lorem ipsum"
        options={mockOptions}
        value={singleValue}
        onChange={(_, newValue) => {
          setSingleValue(newValue)
        }}
      />
      <Autocomplete
        placeholder="Search"
        label="Multiple"
        helperText="lorem ipsum"
        options={mockOptions}
        value={value}
        multiple
        onChange={(_, newValue) => {
          setValue(newValue)
        }}
        limitTags={2}
      />
    </div>
  )
}
```

## Props

> This component extends the types from the `useAutocomplete` hook, which is from [@mui/base](https://mui.com/base-ui/react-autocomplete/components-api/). Many of the props and behaviors are derived from it.

### Generic Types:

- **`ValueType`**: Represents the type of the value associated with each option. This allows the options to be strongly typed according to your specific use case.
- **`Multiple`**: A boolean that determines whether the autocomplete component allows multiple selections. If `true`, `value` will be an array of `ValueType`; otherwise, it will be a single `ValueType`.
- **`DisableClearable`**: A boolean that indicates whether the clear button should be disabled. When `true`, the user cannot clear the selection.

| Name                                  | Type                                    | Required | Default | Description                                                                                                                               |
| ------------------------------------- | --------------------------------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| **`value`**                           | `ValueType[] \| ValueType`              |          | -       | The current selected value(s). If `multiple` is `true`, this will be an array of `ValueType`; otherwise, it will be a single `ValueType`. |
| **`options`**                         | `AutocompleteOption<ValueType>[]`       |          | -       | The options available for selection.                                                                                                      |
| **`multiple`**                        | `boolean`                               |          | -       | If `true`, multiple items can be selected.                                                                                                |
| **`inputValue`**                      | `string`                                |          | -       | The current input value.                                                                                                                  |
| **`disabled`**                        | `boolean`                               |          | -       | If `true`, the autocomplete is disabled.                                                                                                  |
| **`loading`**                         | `boolean`                               |          | -       | If `true`, the autocomplete will show a loading indicator.                                                                                |
| **`required`**                        | `boolean`                               |          | -       | If `true`, the autocomplete will be marked as required.                                                                                   |
| **`noItemLabel`**                     | `string`                                |          | -       | Text displayed when no items are available.                                                                                               |
| **`filterOptions`**                   | `UseAutocompleteProps['filterOptions']` |          | -       | Custom filter function for options.                                                                                                       |
| **`inputProps`**                      | `Omit<InputProps, "onChange">`          |          | -       | Props passed to the input element.                                                                                                        |
| **`limitTags`**                       | `number`                                |          | -       | The maximum number of tags to display.                                                                                                    |
| **`disableClearable`**                | `boolean`                               |          | -       | If `true`, the clear button is hidden.                                                                                                    |
| **`hasSelectAll`**                    | `boolean`                               |          | -       | If `true`, a "Select All" option is available.                                                                                            |
| **`allOption`**                       | `AutocompleteAllOption`                 |          | -       | Option to select all items.                                                                                                               |
| **`isClearSearchKeywordAfterSelect`** | `boolean`                               |          | -       | If `true`, the search keyword is cleared after selecting an item.                                                                         |
| **`loadMoreLabel`**                   | `string`                                |          | -       | Text for the "Load more" option.                                                                                                          |
| **`hasLoadMore`**                     | `boolean`                               |          | -       | If `true`, a "Load more" option is available.                                                                                             |
| **`searchable`**                      | `boolean`                               |          | 'true'  | If `true`, The autocomplete's input would be able to type the keyword to search options.                                                  |
| **`hideOverflowTag`**                 | `boolean`                               |          | 'true'  | If `true`, The autocomplete's tags would be hide if it is overflow its container.                                                         |

## Event Callbacks

The event callback types can also be customized using the same generic type parameters as `AutocompleteProps`.

| Name                   | Type                                                                                                                                                                                    | Required | Default | Description                                                          |
| ---------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | ------- | -------------------------------------------------------------------- |
| **`onChange`**         | `UseAutocompleteProps['onChange']`                                                                                                                                                      |          | -       | Callback function triggered when the selected value changes.         |
| **`onInputChange`**    | `(event: React.SyntheticEvent, value: string, reason: AutocompleteInputChangeReason, focused?: boolean) => void`                                                                        |          | -       | Callback function triggered when the input value changes.            |
| **`onClickAllOption`** | `(event: ReactMouseEvent<HTMLLIElement, MouseEvent>, currentValue: AutocompleteValue<ValueType, Multiple, DisableClearable, false>, currentState?: AutocompleteAllOptionState) => void` |          | -       | Callback function triggered when the "Select All" option is clicked. |
| **`onLoadMore`**       | `() => void`                                                                                                                                                                            |          | -       | Callback function triggered when loading more items.                 |

## AutocompleteOption

The `AutocompleteOption` type defines the properties for each option in the autocomplete component.\_

| Name            | Type                                                                           | Required | Default | Description                                                                 |
| --------------- | ------------------------------------------------------------------------------ | -------- | ------- | --------------------------------------------------------------------------- |
| **`label`**     | `string`                                                                       | ✅       | -       | The display label for the option.                                           |
| **`value`**     | `ValueType`                                                                    | ✅       | -       | The value associated with the option.                                       |
| **`disabled`**  | `boolean`                                                                      |          | `false` | If `true`, the option will be disabled and unselectable.                    |
| **`className`** | `string`                                                                       |          | -       | Additional class name(s) to apply to the option element for custom styling. |
| **`onClick`**   | `(event: ReactMouseEvent<HTMLLIElement, MouseEvent>, item: ValueType) => void` |          | -       | Callback function triggered when the option is clicked.                     |

## AutocompleteAllOption

The `AutocompleteAllOption` type defines the properties for the "Select All" option in the autocomplete component.

| Name        | Type                                         | Required | Default     | Description                                                                                                 |
| ----------- | -------------------------------------------- | -------- | ----------- | ----------------------------------------------------------------------------------------------------------- |
| **`label`** | `string`                                     |          | `""`        | The label for the "Select All" option.                                                                      |
| **`state`** | `"selected" \| "default" \| "indeterminate"` |          | `"default"` | The state of the "Select All" option, indicating whether it is selected, unselected, or partially selected. |

## Examples

### Default

This example shows the basic use of the autocomplete component. It works automatically without any special settings.

<ComponentPreviewUI name="autocomplete-demo" />

### Form Props

This example covers all the common ways the autocomplete component is used in forms, including features like `helperText`, `error`, `required`, `disabled`,`value`, `onChange`, `onBlur`, `onFocus` and `label`.

<ComponentPreviewUI name="autocomplete-form-props" />

### Fit & Full Width

This example shows how to make the autocomplete component fit its content or expand to take up the full width of the screen.

<ComponentPreviewUI name="autocomplete-fit-and-full-width" />

### Infinite Load

This example shows how the autocomplete component can load more options as you scroll, making it perfect for lists with lots of items.

<ComponentPreviewUI name="autocomplete-infinite-load" />

### Custom Filter Options

This example shows how you can customize which options appear in the autocomplete based on what the user types.

> This props is derived from the @mui. You could find more information [here](https://mui.com/material-ui/react-autocomplete/#custom-filter)

<ComponentPreviewUI name="autocomplete-custom-filter-options" />

### Multiple

This example shows how the autocomplete component can let users pick more than one option at a time.

<ComponentPreviewUI name="autocomplete-multiple" />

### Limit Visible Tags

This example shows how to limit the number of selected options that are displayed as tags, hiding the rest to keep the UI clean.

<ComponentPreviewUI name="autocomplete-limit-visible-tags" />

### Select All Options

This example shows how to add a "Select All" option to the autocomplete component, making it easy for users to select everything with one click.

<ComponentPreviewUI name="autocomplete-with-select-all-option" />

### Custom SelectAll Option's Label

This example shows how to change the label of the "Select All" option to something that better fits your needs.

<ComponentPreviewUI name="autocomplete-custom-select-all-option" />

### Server Side (Controlled)

This example shows how to control the options in the autocomplete component from a server, useful when the list of options comes from an external source.

<ComponentPreviewUI name="autocomplete-server-side" />

### Controlled Open State

This example shows how to control `open` state of the autocomplete component.

<ComponentPreviewUI name="autocomplete-controlled-open-state" />

### Disable Search Function

In case we don't need the input to be searchable. We could set the `searchable` prop to be `false`.

<ComponentPreviewUI name="autocomplete-not-searchable" />

## CSS

| Class Name                             | Description                                                                           |
| -------------------------------------- | ------------------------------------------------------------------------------------- |
| `.ApolloAutocomplete-root`             | Styles applied to the root container of the autocomplete.                             |
| `.ApolloAutocomplete-actionContainer`  | Styles applied to the container that holds action buttons like clear and arrow icons. |
| `.ApolloAutocomplete-clearAllButton`   | Styles applied to the clear all button inside the autocomplete.                       |
| `.ApolloAutocomplete-arrowIcon`        | Styles applied to the arrow icon used to open or close the dropdown.                  |
| `.ApolloAutocomplete-inputRoot`        | Styles applied to the root element of the input inside the autocomplete.              |
| `.ApolloAutocomplete-input`            | Styles applied to the input field of the autocomplete.                                |
| `.ApolloAutocomplete-chip`             | Styles applied to each selected option (chip) in the autocomplete.                    |
| `.ApolloAutocomplete-popupContainer`   | Styles applied to the container of the dropdown list.                                 |
| `.ApolloAutocomplete-loadingItem`      | Styles applied to the loading item in the dropdown list.                              |
| `.ApolloAutocomplete-noItem`           | Styles applied to the "No item" message when no options are available.                |
| `.ApolloAutocomplete-tagWrapper`       | Styles applied to the wrapper around the tags in the autocomplete.                    |
| `.ApolloAutocomplete-menuItem`         | Styles applied to each item in the dropdown list.                                     |
| `.ApolloAutocomplete-menuItemDisabled` | Styles applied to disabled items in the dropdown list.                                |
| `.ApolloAutocomplete-menuItemSelected` | Styles applied to selected items in the dropdown list.                                |
| `.ApolloAutocomplete-menuItemCheckbox` | Styles applied to the checkbox inside a dropdown item.                                |
| `.ApolloAutocomplete-menuItemText`     | Styles applied to the text inside each dropdown item.                                 |
