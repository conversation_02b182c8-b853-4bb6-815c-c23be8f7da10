---
title: Breadcrumbs
description: A breadcrumbs is a list of links that help visualize a page's location within a site's hierarchical structure, it allows navigation up to any of the ancestors.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Breadcrumbs/BreadcrumbsProps.ts
---

<ComponentPreviewUI name="breadcrumbs-preview" />

## Usage

```tsx
import { Breadcrumbs } from "@design-systems/apollo-ui"
```

```tsx
<Breadcrumbs aria-label="breadcrumb">
    <a href="/">Apollo</a>
    <a href="/material-ui/getting-started/installation/">
          Core
    </a>
    <Typography>
        Breadcrumbs
    </Typography>
</Breadcrumbs>
```

## Props


| Name                          | Type                                            | Default                | Description                                                                                                                                                                                                        |
|-------------------------------|-------------------------------------------------|------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **`children`**                | ` 'node' `                                      | `node`                 | The content of the component.                                                                                                                                                                                      |
| **`color`**                   | `'danger' \| 'primary'`                         | `primary`              | The color of the component. It supports those theme colors that make sense for this component.                                                                                                                     |
| **`separator`**               | `node`                                          | `>`                    | Custom separator node.                                                                                                                                                                                             |
| **`maxItems`**                | `number`                                        | 1                      | Specifies the maximum number of breadcrumbs to display. When there are more than the maximum number, only the first itemsBeforeCollapse and last itemsAfterCollapse will be shown, with an ellipsis in between.    |
| **`itemsBeforeCollapse`**     | `number`                                        | 1                      | 1 If max items is exceeded, the number of items to show after the ellipsis.                                                                                                                                        |
| **`itemsAfterCollapse`**      | `number`                                        | 8                      | If max items is exceeded, the number of items to show before the ellipsis.                                                                                                                                         |


## Examples

### breadcrumbs (default)

<ComponentPreviewUI name="breadcrumbs-preview" />

### breadcrumbs (ellipsis)

<ComponentPreviewUI name="breadcrumbs-ellipsis" />


### breadcrumbs mutiple active

<ComponentPreviewUI name="breadcrumbs-mutiple-active" />
