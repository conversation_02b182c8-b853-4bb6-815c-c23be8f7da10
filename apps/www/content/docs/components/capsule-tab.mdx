---
title: Capsule Tab
description: A variant of the tab component which display as a capsule shape.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/CapsuleTab/CapsuleTab.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8157-3517&t=MVV8x7P3MUVnN0f1-0
---

<ComponentPreviewUI name="capsule-tab-demo" />

## Usage

```tsx
import { CapsuleTab } from "@design-systems/apollo-ui"

function MyFeature() {
  const [selectedTab, setSelectedTab] = useState(0)
  return (
    <CapsuleTab
      selectedIndex={selectedTab}
      onSelect={setSelectedTab}
      tabs={[
        {
          id: "tab1",
          label: "Tab1",
        },
        {
          id: "tab2",
          label: "Tab2",
        },
        {
          id: "tab3",
          label: "Tab3",
        },
      ]}
    />
  )
}
```

## Props

| Name                | Type                      | Required | Default | Description                                                            |
| ------------------- | ------------------------- | -------- | ------- | ---------------------------------------------------------------------- |
| **`tabs`**          | `CapsuleTabItem[]`        | ✅       | -       | Array of tab items. Each item should follow the `CapsuleTabItem` type. |
| **`selectedIndex`** | `number`                  |          | -       | Index of the selected tab.                                             |
| **`onSelect`**      | `(index: number) => void` |          | -       | Callback function when a tab is selected.                              |

## CapsuleTabItem

| Name        | Type        | Required | Default | Description                            |
| ----------- | ----------- | -------- | ------- | -------------------------------------- |
| **`id`**    | `string`    | ✅       | -       | Unique identifier for the tab item.    |
| **`label`** | `ReactNode` | ✅       | -       | The label to be displayed for the tab. |

## Examples

### Default

The capsule tab is controlled component. We need to pass `selectedIndex` and `onSelect` to control the state of each tab.

<ComponentPreviewUI name="capsule-tab-demo" />

## CSS

| Class Name                       | Description                                 |
| -------------------------------- | ------------------------------------------- |
| `.ApolloCapsuleTab-root`         | Styles applied to the root container        |
| `.ApolloCapsuleTab-container`    | Styles applied to the tab container         |
| `.ApolloCapsuleTab-item`         | Styles applied to each tab item             |
| `.ApolloCapsuleTab-itemText`     | Styles applied to the text inside tab items |
| `.ApolloCapsuleTab-itemSelected` | Styles applied to the selected tab item     |
