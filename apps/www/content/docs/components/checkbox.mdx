---
title: Checkbox
description: Displays a checkbox component.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Checkbox/CheckboxProps.ts
---

<ComponentPreviewUI name="checkbox-demo" />

## Usage

```tsx
import { Checkbox } from "@design-systems/apollo-ui"
```

## Props

| Name          | Type                | Default                | required                    |
|---------------|---------------------|------------------------|---------------------------------|
| **`label`**| `string`              | `none`                | no          |
| **`labelPlacement`**| `string`              | `right`                | no          |
| **`indeterminate`**| `boolean`              | `none`                | no          |

## **CSS classes**
| Class Name | Rule name | Description |
|------------|-----------|-------------|
| `.ApolloCheckbox-root` | Root | Styles applied to checkbox |
| `.ApolloCehckbox-label` | Label | Styles applied to the label |
