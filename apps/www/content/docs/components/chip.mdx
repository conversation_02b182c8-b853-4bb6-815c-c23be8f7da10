---
title: Chip
description: Displays a chip component.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Chip/ChipProps.ts
---


<ComponentPreviewUI name="chip-demo" />

## Usage

```tsx
import { Chip } from "@design-systems/apollo-ui"
```

## Props

| Name          | Type                | Default                | Description                     |
|---------------|---------------------|------------------------|---------------------------------|
| **`label`**   | `node`                                                 | -                  |The content of the component.              |
| **`variant`** | `'outline', 'fill'`                                     | `'outline'`        |Chip variant                               |
| **`color`**   | `'default', 'primary', 'danger', 'warning'`               | `'default'`        | The color of the component.               |
| **`rounded`** | `'default','sm', 'lg', 'xl', 'xxl', 'none', 'full'`    | `'default'`        | The border radius sizes of the component. |
| **`startDecorator`**| `element `    | -              | Icon element.                                                                |
| **`deleteIcon`**| `element `        | `X Icon`       | Override the default delete icon element. Shown only if onDelete is set.     |
| **`onDelete`**| `func`              | -              | is function for delete icon                                                  |
| **`truncatedTextWidth`**| `number`  | `auto`         | max width text for ellipsis without icon element.                            |
| **`...otherChipProps`**| `Div`  | -         | The `div` property of the component                             |


