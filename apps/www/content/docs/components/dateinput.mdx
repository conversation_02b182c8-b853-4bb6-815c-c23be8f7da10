---
title: DateInput
description: The DateInput component allows users to select dates with options for era, locale, and custom formats.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/DateInput/DateInputProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=3073-21&node-type=CANVAS&m=dev
---

> Note: [`react-datepicker`](https://reactdatepicker.com/) is used as the base component for `DateInput`. For further usage, refer to the [react-datepicker documentation](https://reactdatepicker.com/).

<ComponentPreviewUI name="dateinput-demo" />

## Usage

```tsx
import { DateInput } from "@design-systems/apollo-ui"

function MyApp() {
  const [date, setDate] = useState<Date | null>(null)
  return (
    <DateInput
      value={date}
      onChange={(date) => setDate(date)}
      label="Default"
    />
  )
}
```

## Props

> **Note:** This component extends the `react-datepicker` props, with the following props omitted: `placeholderText`, `value`, `selectsRange`, `selectsMultiple`.

| Name             | Type                            | Required | Default | Description                                                                                                                                                                 |
| ---------------- | ------------------------------- | -------- | ------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **`era`**        | `"ad" \| "bd"`                  |          | `'ad'`  | The era for the date input.                                                                                                                                                 |
| **`locale`**     | `"en" \| "th"`                  |          | `'th'`  | The locale for the date input.                                                                                                                                              |
| **`format`**     | `string`                        |          | -       | The format string for the date.                                                                                                                                             |
| **`inputProps`** | `InputProps`                    |          | -       | Custom properties for the input field. [InputProps](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Input/InputProps.ts) |
| **`value`**      | `Date \| null`                  |          | -       | The current value of the date picker.                                                                                                                                       |
| **`onChange`**   | `(value: Date \| null) => void` |          | -       | Callback function triggered when the date changes.                                                                                                                          |

For other accepted props, please refer to [`react-datepicker` props](https://reactdatepicker.com).

## Examples

### Default

The default date picker example with no special configuration.

<ComponentPreviewUI name="dateinput-demo" />

### Era

This example demonstrates the use of the `era` prop to switch between AD and BD (Buddhist Era).

<ComponentPreviewUI name="dateinput-era" />

### Locale

This example shows how to set the locale for the date picker, supporting both English (`en`) and Thai (`th`).

<ComponentPreviewUI name="dateinput-locale" />

### Format

This example demonstrates using a custom format string (e.g., `dddd` or `dd`), including support for the Buddhist Era when using `date-fns` formatting.

<ComponentPreviewUI name="dateinput-format" />

### Exclude Date

This example shows how to exclude specific dates from selection.

<ComponentPreviewUI name="dateinput-excludedate" />

### Month/Year Picker

This example shows how to use date-input as MonthPicker or YearPicker.

<ComponentPreviewUI name="dateinput-month-year-picker" />

### Date Range

This example shows how to use date-input as DateRangePicker.

<ComponentPreviewUI name="dateinput-daterange" />

## CSS

| Class Name                         | Description                                 |
| ---------------------------------- | ------------------------------------------- |
| `.ApolloDateInput-inputRoot`       | Styles applied to the input root element    |
| `.ApolloDateInput-root`            | Styles applied to the root container        |
| `.ApolloDateInput-calendarHeader`  | Styles applied to the calendar header       |
| `.ApolloDateInput-prevMonthButton` | Styles applied to the previous month button |
| `.ApolloDateInput-monthPicker`     | Styles applied to the month picker          |
| `.ApolloDateInput-yearPicker`      | Styles applied to the year picker           |
| `.ApolloDateInput-nextMonthButton` | Styles applied to the next month button     |
| `.ApolloDateInput-day`             | Styles applied to individual days           |
| `.ApolloDateInput-month`           | Styles applied to individual months         |
| `.ApolloDateInput-year`            | Styles applied to individual years          |
