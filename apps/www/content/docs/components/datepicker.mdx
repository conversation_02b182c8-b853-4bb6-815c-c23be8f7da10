---
title: DatePicker
description: Displays a date-picker.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/DatePicker/DatePickerProps.ts
---

[`react-multi-date-picker`](https://shahabyazdi.github.io/react-multi-date-picker/) is being used under the hood as a base component.
for usage please refers to the [docs](https://shahabyazdi.github.io/react-multi-date-picker/).

<ComponentPreviewUI name="datepicker-preview" />

## Usage

```tsx
import { DatePicker } from "@design-systems/apollo-ui"
```

```tsx
<DatePicker />
```

## Props


| Name                  | Type                                            | Default                | Description                                                                                    |
|-----------------------|-------------------------------------------------|------------------------|------------------------------------------------------------------------------------------------|
| **`variant`**         | `'plain'`                                       | `plain`                | date-picker variant                                                                            |
| **`disableDayPicker`**| `boolean`                                       | `false`                | when `disableDayPicker` is `false`, `<TimePicker>` will be rendered                            |
| **`format`**          | `string`                                        | `D MMM YYYY`          | date format will be used when `disableDayPicker` is `false`                                      |
| **`timeFormat`**      | `string`                                        | `HH:mm:ss`             | timeFormat will be used when `disableDayPicker` is `true`                                      |
| **`error`**           | `'boolean'`                                     | `false`                | error state                                                                                    |
| **`disabled`**        | `'boolean'`                                     | `false`                | disable state

for other accepted props please refers to [`react-multi-date-picker` props](https://shahabyazdi.github.io/react-multi-date-picker/props/)


## Examples

### use time picker (by setting `disableDayPicker` to `true`)

<Callout className="mt-6 text-warning-95 bg-surface-static-warning-active dark:bg-transparent">
__caveats__: `react-multi-date-picker` currently doesn't support replacing `<Arrow/>` with custom component when rendering `<TimePicker/>`. [see source code](https://github.com/shahabyazdi/react-multi-date-picker/blob/master/src/plugins/time_picker/time_picker.js#L130C12-L130C17)
</Callout>


<ComponentPreviewUI name="datepicker-time" />

### disabled

<ComponentPreviewUI name="datepicker-disabled" />

### error state

<ComponentPreviewUI name="datepicker-error" />
