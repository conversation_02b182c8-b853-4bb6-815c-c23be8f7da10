---
title: Float Button
description: a control button designed to float on the screen. It is typically circular or round in shape and is often brightly colored to ensure it is easily noticeable by users.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/FloatButton/FloatButtonProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8619-4878
---

<ComponentPreviewUI name="float-button-preview" />

## Usage

```tsx
import { useMemo, useState } from "react"
import { Smile } from "@design-systems/apollo-icons"
import { FloatButton, useScrollDirection } from "@design-systems/apollo-ui"

export default function FloatButtonPreview() {
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null)
  const { scrollDirection } = useScrollDirection(containerRef)
  const isExpanded = useMemo(
    () => (scrollDirection ? ["up", "none"].includes(scrollDirection) : true),
    [scrollDirection]
  )

  return (
    <div className="relative  w-[320px]">
      <div
        className="w-full h-[500px] relative bg-gray-10 overflow-auto "
        ref={setContainerRef}
      >
        <div className="w-full h-[1000px] bg-red-400" />
      </div>
      <div className="absolute bottom-[64px] right-3">
        <FloatButton
          icon={<Smile />}
          iconSide="end"
          label="Help"
          isExpanded={isExpanded}
        />
      </div>
    </div>
  )
}
```

# Props

> This component extends the type from `ButtonProps` which is prop of [Button](https://cjexpress.pages-gitlab.cjexpress.io/design-systems/apollo/docs/components/button) component

| Name             | Type               | Required | Default   | Description                                         |
| ---------------- | ------------------ | -------- | --------- | --------------------------------------------------- |
| **`icon`**       | `ReactNode`        | ✅       | -         | The icon element displayed in the button.           |
| **`label`**      | `ReactNode`        | ✅       | -         | The label element displayed alongside the icon.     |
| **`isExpanded`** | `boolean`          |          | `false`   | If `true`, the button will be in an expanded state. |
| **`iconSide`**   | `'start' \| 'end'` |          | `'start'` | The position of the icon relative to the label.     |

## Examples

In the example, We have provide all use case of FloatButton.

<ComponentPreviewUI name="float-button-preview" />

## CSS

| Class Name                 | Description                                               |
| -------------------------- | --------------------------------------------------------- |
| `.ApolloFloatButton-root`  | Styles applied to the root container of the float button. |
| `.ApolloFloatButton-icon`  | Styles applied to the icon inside the float button.       |
| `.ApolloFloatButton-label` | Styles applied to the label of the float button.          |
