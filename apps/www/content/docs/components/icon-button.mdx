---
title: I<PERSON> Button
description: The Icon Button component is a compact, customizable button primarily used for actions involving icons. It supports various states, sizes, and icon configurations, making it ideal for both primary and secondary actions.
featured: true
component: true
links:
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=9538-21125&node-type=instance&m=dev
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/IconButton/IconButtonProps.ts
---

<ComponentPreviewUI name="icon-button-demo" />

## Usage

```tsx
import { Heart } from "@design-systems/apollo-icons"
import { IconButton } from "@design-systems/apollo-ui"

function MyFeature() {
  return (
    <IconButton>
      <Heart />
    </IconButton>
  )
}
```

## Props

> This component extends the type from Apollo's `ButtonProps` which is from [But<PERSON>](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Button/ButtonProps.ts).

| Name           | Type                                             | Required | Default   | Description                                 |
| -------------- | ------------------------------------------------ | -------- | --------- | ------------------------------------------- |
| **`size`**     | `'small' \| 'medium' \| 'large'`                 |          | `'large'` | The size of IconButton                      |
| **`disabled`** | `bool`                                           |          | -         | If `true`, the icon button will be disabled |
| **`onClick`**  | `(event: MouseEvent<HTMLButtonElement>) => void` |          | -         | If `true`, the icon button will be disabled |

## Examples

### Default

This example will show the default usage of component which `size` prop applied.

<ComponentPreviewUI name="icon-button-demo" />

## CSS

| Class Name               | Description                          |
| ------------------------ | ------------------------------------ |
| `.ApolloIconButton-root` | Styles applied to the root container |
