---
title: Icon
description: Displays an icon or a component that looks like an icon.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Icon/IconProps.ts
---

<ComponentPreviewUI name="icon-preview" />

## Usage

```tsx
import { Icon } from "@design-systems/apollo-ui"
```

```tsx
<Icon />
```

## Props


| Name                  | Type                                            | Default                | Description                                                                                    |
|-----------------------|-------------------------------------------------|------------------------|------------------------------------------------------------------------------------------------|
| **`variant`**         | `'plain'`                                       | `plain`                | icon variant                                                                                 |
| **`color`**           | `'primary'\|'danger'\|'black'\|'white'`         | `black`                | The color of the component.                                                                    |
| **`viewBox`**         | `string`                                        | `'0 0 24 24'`          | a [`viewBox`](https://www.digitalocean.com/community/tutorials/svg-svg-viewbox) of svg element |
