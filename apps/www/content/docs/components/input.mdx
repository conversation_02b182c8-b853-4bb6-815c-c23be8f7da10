---
title: Input
description: Displays an input or a component that looks like an input.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Input/InputProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=361-4909
---

<ComponentPreviewUI name="input-preview" />

## Usage

import { Input } from "@design-systems/apollo-ui"

```tsx
function MyFeature() {
  return <Input />
}
```

## Props

> This component extends the type from `InputProps` which is from [@mui/base](https://mui.com/base-ui/react-input).

| Name                 | Type                    | Required | Default   | Description                                                                                    |
| -------------------- | ----------------------- | -------- | --------- | ---------------------------------------------------------------------------------------------- |
| **`variant`**        | `'outline'`             |          | `outline` | Input variant                                                                                  |
| **`color`**          | `'danger' \| 'primary'` |          | `primary` | The color of the component. It supports those theme colors that make sense for this component. |
| **`size`**           | `'medium' \| 'small'`   |          | `medium`  | The size of input                                                                              |
| **`disabled`**       | `bool`                  |          | -         | If `true`, the component is disabled.                                                          |
| **`fullWidth`**      | `bool`                  |          | -         | If `true`, the input will take up the full width of its container.                             |
| **`startDecorator`** | `node`                  |          | -         | Element placed before the children.                                                            |
| **`endDecorator`**   | `node`                  |          | -         | Element placed after the children.                                                             |
| **`error`**          | `bool`                  |          | -         | If `true`, the input will indicate an error.                                                   |
| **`label`**          | `string`                |          | -         | The label of input element.                                                                    |
| **`helperText`**     | `string`                |          | -         | text displayed below the input element.                                                        |

## Examples

### Outline (default)

A simple input with the default outline variant.

<ComponentPreviewUI name="input-outline" />

### Size

There're two size of input which are `medium` and `small`.

<ComponentPreviewUI name="input-size" />

### Decorator

An input with elements placed before and after the input field.

<ComponentPreviewUI name="input-icon" />

### Disabled

An input that is disabled and cannot be interacted with.

<ComponentPreviewUI name="input-disabled" />

### Error

An input that indicates an error state.

<ComponentPreviewUI name="input-error" />

### HelperText

An input with additional descriptive text or label.

<ComponentPreviewUI name="input-with-label" />

### Multiple line (Textarea)

> Note: This use case is not official published on Figma yet

An input that is used as a textarea by setting the multiline prop.

<ComponentPreviewUI name="input-multiline" />

## CSS

| Class Name                    | Description                                     |
| ----------------------------- | ----------------------------------------------- |
| `.ApolloInput-root`           | Styles applied to the root container            |
| `.ApolloInput-input`          | Styles applied to the input element             |
| `.ApolloInput-formControl`    | Styles applied to the FormControl component     |
| `.ApolloInput-startDecorator` | Styles applied to the start decorator component |
| `.ApolloInput-endDecorator`   | Styles applied to the end decorator component   |
