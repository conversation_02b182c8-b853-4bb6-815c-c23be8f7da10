---
title: Menu Option
description: The MenuOption component is used to render individual menu items with optional decorators and custom content.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/MenuOption/MenuOptionProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=10819-24233&p=f&m=dev
---

<ComponentPreviewUI name="menu-option-demo" />

## Usage

```tsx
import { MenuOption } from "@design-systems/apollo-ui"

function MyMenu() {
  return (
    <MenuList>
      <MenuOption startDecorator="✅">Menu Item 1</MenuOption>
      <MenuOption>Menu Item 2</MenuOption>
      <MenuOption startDecorator="❤️">Menu Item 3</MenuOption>
    </MenuList>
  )
}
```

## Props

### MenuOption

| Name                 | Type                            | Required | Default | Description                                                                        |
| -------------------- | ------------------------------- | -------- | ------- | ---------------------------------------------------------------------------------- |
| **`label`**          | `string`                        |          | `""`    | The text label of the menu option.                                                 |
| **`startDecorator`** | `ReactNode`                     |          | `null`  | Adds an optional element (e.g., an icon, emoji, or custom component) at the start. |
| **`selected`**       | `boolean`                       |          | `false` | If `true`, the menu option is displayed as selected.                               |
| **`disabled`**       | `boolean`                       |          | `false` | If `true`, the menu option is displayed as disabled and cannot be clicked.         |
| **`...liProps`**     | `HTMLAttributes<HTMLLIElement>` |          | -       | Additional props spread to the underlying `<li>` element.                          |

### MenuList

> The `MenuList` component spreads its props to the underlying `<ul>` element. This means you can use any standard `HTMLAttributes<HTMLUListElement>` on it.

## Examples

### Default Menu Options

By default, MenuOption renders list items with customizable content.

<ComponentPreviewUI name="menu-option-demo" />

### Custom Decorators

You can pass icons, emojis, or custom components as decorators.

<ComponentPreviewUI name="menu-option-custom-decorator" />

## CSS

### MenuOption

| Class Name                         | Description                                                       |
| ---------------------------------- | ----------------------------------------------------------------- |
| `.ApolloMenuOption-root`           | Styles applied to the root `li` element of a menu option.         |
| `.ApolloMenuOption-startDecorator` | Styles applied to the start decorator container in a menu option. |

### MenuList

| Class Name             | Description                                               |
| ---------------------- | --------------------------------------------------------- |
| `.ApolloMenuList-root` | Styles applied to the root `ul` element of the menu list. |
