---
title: Modal
description: The Modal component is a versatile dialog box used to prompt users for action or display important information. It can be customized with various properties to control its appearance and behavior.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Modal/ModalProps.ts
---

<ComponentPreviewUI name="modal-preview" />

## Usage

```tsx
import { Modal } from "@design-systems/apollo-ui"

function ModalPreview() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <Modal
      header="Modal Title"
      open={isOpen}
      onClose={() => {
        setIsOpen(false)
      }}
      onOk={() => {
        console.log("[Click Event]: I'm okay")
      }}
    >
      <div>
        Once upon a time, there was a forest where plenty of birds lived and
        built their nests on the trees.
      </div>
    </Modal>
  )
}
```

## Props

> This component extends the type from `ModalProps` which is from [@mui/base](https://mui.com/base-ui/react-modal/components-api/).

| Name                          | Type                  | Required | Default     | Description                                                                                                      |
| ----------------------------- | --------------------- | -------- | ----------- | ---------------------------------------------------------------------------------------------------------------- |
| **`size`**                    | `'default' \| 'full'` |          | `'default'` | The size of the modal.                                                                                           |
| **`closeAfterPressEsc`**      | `boolean`             |          | `true`      | If `true`, the modal will close when the ESC key is pressed.                                                     |
| **`closeAfterClickBackdrop`** | `boolean`             |          | `true`      | If `true`, the modal will close when clicking on the backdrop.                                                   |
| **`onClose`**                 | `() => void`          | ✅       | -           | Callback function that is triggered when the modal is closed.                                                    |
| **`icon`**                    | `ReactNode`           |          | -           | Element to display as the modal's icon.                                                                          |
| **`header`**                  | `ReactNode`           |          | -           | Element to display as the modal's header.                                                                        |
| **`footer`**                  | `ReactNode`           |          | -           | Element to display as the modal's footer.                                                                        |
| **`hideCloseIcon`**           | `boolean`             |          | `false`     | If `true`, the close icon will be hidden.                                                                        |
| **`onOk`**                    | `() => void`          |          | -           | Callback function that is triggered when the OK button is clicked. This also makes the OK button display.        |
| **`disabledOkButton`**        | `boolean`             |          | -           | If `true`, the OK button will be disabled.                                                                       |
| **`okButtonText`**            | `string`              |          | -           | Text to display on the OK button.                                                                                |
| **`onCancel`**                | `() => void`          |          | -           | Callback function that is triggered when the Cancel button is clicked. This also makes the Cancel button display |
| **`disabledCancelButton`**    | `boolean`             |          | -           | If `true`, the Cancel button will be disabled.                                                                   |
| **`cancelButtonText`**        | `string`              |          | -           | Text to display on the Cancel button.                                                                            |
| **`onDelete`**                | `() => void`          |          | -           | Callback function that is triggered when the Delete button is clicked. This also makes the Delete button display |
| **`disabledDeleteButton`**    | `boolean`             |          | -           | If `true`, the Delete button will be disabled.                                                                   |
| **`deleteButtonText`**        | `string`              |          | -           | Text to display on the Delete button.                                                                            |

## Examples

### Default

A simple modal with default settings.

<ComponentPreviewUI name="modal-preview" />

### Full-size

A modal that takes up the full screen.

<ComponentPreviewUI name="modal-full-size" />

### With Close on ESC

A modal that closes when the ESC key is pressed.

<ComponentPreviewUI name="modal-with-close-on-esc" />

### With Close on Backdrop Click

A modal that closes when clicking on the backdrop.

<ComponentPreviewUI name="modal-with-close-on-backdrop-click" />

### With Custom Header and Footer

A modal that includes custom header and footer elements.

**Note:** When the `footer` prop is set, the OK/Cancel and delete buttons will be hidden.

<ComponentPreviewUI name="modal-with-custom-header-and-footer" />

### With Icon

A modal that includes an icon.

<ComponentPreviewUI name="modal-with-icon" />

### With OK Button

A modal that includes an OK button with a callback function.

<ComponentPreviewUI name="modal-with-ok-button" />

### With Cancel Button

A modal that includes a Cancel button with a callback function.

<ComponentPreviewUI name="modal-with-cancel-button" />

### With Delete Button

A modal that includes a Delete button with a callback function.

**Note:** When the Delete button is displayed, the OK and Cancel buttons will be hidden.

<ComponentPreviewUI name="modal-with-delete-button" />

### With Disabled Buttons

A modal with disabled OK, Cancel, and Delete buttons.

<ComponentPreviewUI name="modal-with-disabled-buttons" />

## Negative Modal

This modal is specifically used for negative action alerts, such as Delete or Cancel operations.

<ComponentPreviewUI name="modal-negative-variant" />

## Props

> This component extends the type from `ModalProps` which is from the original modal.
> Since this is a variant with pre-implemented UI styling, some props are omitted: `"onOk" | "icon" | "onDelete" | "deleteButtonText" | "disabledDeleteButton"`.

| Name                        | Type         | Required | Default | Description                                                                                                           |
| --------------------------- | ------------ | -------- | ------- | --------------------------------------------------------------------------------------------------------------------- |
| **`onConfirm`**             | `() => void` |          | -       | Callback function triggered when the Confirm button is clicked. This also controls the display of the Confirm button. |
| **`disabledConfirmButton`** | `boolean`    |          | -       | If `true`, the Confirm button will be disabled.                                                                       |
| **`confirmButtonText`**     | `string`     |          | -       | Text to display on the Confirm button.                                                                                |

## CSS

| Class Name                       | Description                             |
| -------------------------------- | --------------------------------------- |
| `.Apollo-Modal-root`             | Styles applied to the root container    |
| `.Apollo-Modal-contentContainer` | Styles applied to the content container |
| `.Apollo-Modal-content`          | Styles applied to the content area      |
| `.Apollo-Modal-icon`             | Styles applied to the icon container    |
| `.Apollo-Modal-closeButton`      | Styles applied to the close button      |
| `.Apollo-Modal-headerContainer`  | Styles applied to the header container  |
| `.Apollo-Modal-footerContainer`  | Styles applied to the footer container  |
