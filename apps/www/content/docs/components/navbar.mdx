---
title: Navbar
description: "The Navbar component offers a customizable and responsive navigation menu with icons and labels for easy user navigation."
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/NavBar/NavBarProos.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=7939-7310&t=2L8UxIjxS32NA4T4-0
---

<ComponentPreviewUI name="navbar-demo" />

## Usage

```tsx
import { Accordion } from "@design-systems/apollo-ui"

function MyFeature() {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const handleChange = useCallback((index: number, menu: NavBarMenuItem) => {
    console.log("[Click] Menu: ", menu)
    setSelectedIndex(index)
  }, [])

  return (
    <NavBar
      activeIndex={selectedIndex}
      onChange={handleChange}
      menu={[
        {
          label: "Home",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Hidden Menu",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
          hidden: true,
        },
      ]}
    />
  )
}
```

## Props

> This component extends the type from `HTMLAttributes<HTMLElement>` which is native html attributes

| Name              | Type                                               | Required | Default | Description                                              |
| ----------------- | -------------------------------------------------- | -------- | ------- | -------------------------------------------------------- |
| **`menu`**        | `NavBarMenuItem<T>[]`                              | ✅       | -       | The list of menu items to display in the navbar.         |
| **`activeIndex`** | `number`                                           |          | -       | The index of the currently active menu item.             |
| **`hideShadow`**  | `boolean`                                          |          | -       | If `true`, the shadow under the navbar will be hidden.   |
| **`onChange`**    | `(index: number, menu: NavBarMenuItem<T>) => void` |          | -       | Callback function triggered when a menu item is clicked. |

## API

### NavBarMenuItem

> Note: NavBarMenuItem can be extended with any additional properties as needed. This allows for greater flexibility when adding custom attributes.

| Name             | Type        | Required | Default | Description                                       |
| ---------------- | ----------- | -------- | ------- | ------------------------------------------------- |
| **`label`**      | `string`    | ✅       | -       | The text label for the menu item.                 |
| **`icon`**       | `ReactNode` | ✅       | -       | The icon to display for the menu item.            |
| **`activeIcon`** | `ReactNode` |          | -       | The icon to display when the menu item is active. |
| **`hidden`**     | `boolean`   |          | -       | If `true`, the menu item will be hidden.          |

## Examples

### Default

By default, the NavBar will be an uncontrolled component and will function correctly without any active index specified.

<ComponentPreviewUI name="navbar-demo" />

### With Active Icons

You can customize the active icons for the NavBar items by passing the `activeIcon` prop. This will display the active icon when the menu is selected.

<ComponentPreviewUI name="navbar-with-active-icons" />

### Hidden Menu Items

When `hidden` is set to true on a menu item, it will not be displayed in the NavBar.

<ComponentPreviewUI name="navbar-with-hidden-item" />

### No Shadow

When `hideShadow` is true, the NavBar will be displayed without a shadow.

<ComponentPreviewUI name="navbar-no-shadow" />

### With Badge

When `badge` is set to any value on a menu item, The badge will be displayed on that menu item.

<ComponentPreviewUI name="navbar-with-badge" />

### With Additional Property

You can add custom properties to the NavBar menu items by extending the `NavBarMenuItem` type.

<ComponentPreviewUI name="navbar-with-additional-property" />

## CSS

| Class Name                    | Description                           |
| ----------------------------- | ------------------------------------- |
| `.ApolloNavBar-root`          | Styles applied to the root container  |
| `.ApolloNavBar-menuContainer` | Styles applied to the menu container  |
| `.ApolloNavBar-menuItem`      | Styles applied to each menu item      |
| `.ApolloNavBar-activeIcon`    | Styles applied to the active icon     |
| `.ApolloNavBar-icon`          | Styles applied to the non-active icon |
| `.ApolloNavBar-menuItemLabel` | Styles applied to the menu item label |
| `.ApolloNavBar-badge`         | Styles applied to the menu item badge |
