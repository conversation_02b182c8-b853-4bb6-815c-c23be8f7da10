---
title: Pagination
description: The Pagination component allows users to navigate through a large set of content by dividing it into smaller pages.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Pagination/PaginationProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=5522-7203&node-type=instance&m=dev
---

<ComponentPreviewUI name="pagination-demo" />

## Usage

```tsx
import { Pagination } from "@design-systems/apollo-ui"

function MyFeature() {
  return <Pagination />
}
```

## Props

| Name                  | Type                                                        | Required | Default     | Description                                                                            |
| --------------------- | ----------------------------------------------------------- | -------- | ----------- | -------------------------------------------------------------------------------------- |
| **`count`**           | `number`                                                    |          | `1`         | The total number of pages.                                                             |
| **`page`**            | `number`                                                    |          | `1`         | The current active page (controlled).                                                  |
| **`defaultPage`**     | `number`                                                    |          | `1`         | The default active page (uncontrolled).                                                |
| **`onChange`**        | `(event: React.ChangeEvent<unknown>, page: number) => void` |          | `undefined` | Callback triggered when the page changes. Provides the new page number as an argument. |
| **`siblingCount`**    | `number`                                                    |          | `1`         | The number of sibling pages to display around the current page.                        |
| **`boundaryCount`**   | `number`                                                    |          | `1`         | The number of boundary pages to display at the start and end.                          |
| **`showFirstButton`** | `boolean`                                                   |          | `false`     | If `true`, the "First" button will be displayed.                                       |
| **`showLastButton`**  | `boolean`                                                   |          | `false`     | If `true`, the "Last" button will be displayed.                                        |
| **`disabled`**        | `boolean`                                                   |          | `false`     | If `true`, disables the component, preventing interactions.                            |
| **`renderItem`**      | `(params: PaginationRenderItemParams) => ReactNode`         |          | `undefined` | Custom render function for pagination items (e.g., for adding tooltips or icons).      |
| **`className`**       | `string`                                                    |          | `undefined` | Custom CSS class for the root element.                                                 |
| **`sx`**              | `SxProps`                                                   |          | `undefined` | The system prop for styling with the design system.                                    |
| **`aria-label`**      | `string`                                                    |          | `undefined` | Accessible label for the component.                                                    |
| **`aria-labelledby`** | `string`                                                    |          | `undefined` | Element ID to label the component for accessibility.                                   |

## Examples

### Default Usage

This example demonstrates the default behavior of the Pagination component. It operates as an uncontrolled component, where you only need to provide the total page count.

<ComponentPreviewUI name="pagination-demo" />

### Controlled Pagination

This example shows how to fully control the Pagination component. The current page is managed via a state, and the `onChange` handler updates the state whenever the page changes.

<ComponentPreviewUI name="pagination-controlled" />

### Custom Page Ranges

This example demonstrates how to customize the number of sibling and boundary pages displayed. Adjust the `siblingCount` and `boundaryCount` props to suit your needs.

<ComponentPreviewUI name="pagination-custom-ranges" />

### Disabled Pagination

This example shows the Pagination component in a disabled state. All interactions are disabled, making it non-interactive for scenarios where navigation is restricted.

<ComponentPreviewUI name="pagination-disabled" />

## CSS

| Class Name                                | Description                                                           |
| ----------------------------------------- | --------------------------------------------------------------------- |
| `.ApolloPagination-root`                  | Styles applied to the root container.                                 |
| `.ApolloPagination-prevPageButton`        | Styles applied to the "Previous Page" button.                         |
| `.ApolloPagination-nextPageButton`        | Styles applied to the "Next Page" button.                             |
| `.ApolloPaginationItem-button`            | Styles applied to each pagination item button.                        |
| `.ApolloPaginationItem-selectedButton`    | Styles applied to the selected pagination item button.                |
| `.ApolloPaginationItem-disabled`          | Styles applied to a disabled pagination item button.                  |
| `.ApolloPaginationItem-button.borderless` | Styles applied to a pagination item button when `borderless` is true. |
