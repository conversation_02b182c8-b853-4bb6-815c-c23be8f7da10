---
title: Product Card
description: The ProductCard component is a flexible, styled card designed to display product information.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/ProductCard/ProductCardProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=7819-7832&t=qRavVQGsbrBEPMMq-0
---

<ComponentPreviewUI name="product-card-demo" />

## Usage

```tsx
import { ProductCard } from "@design-systems/apollo-ui"

function MyFeature() {
  return (
    <ProductCard
      title="ผ้าอ้อมสำเร็จรูป Moby"
      imageSrc="https://picsum.photos/200/300?random=1"
      body={
        <Typography level="caption" className="text-content-danger-default">
          หมดอายุ 12/07/2567 (23:59)
        </Typography>
      }
      footer={
        <Button variant="solid" color="primary" className="w-full">
          เก็บคูปอง
        </Button>
      }
    />
  )
}
```

## Props

> Type of `imageProps` will be dynamic based on ImageComponent's props. By default, the type is `ImgHTMLAttributes<HTMLImageElement>`, which are the props of the `img` tag.

| Name                 | Type                                              | Required | Default   | Description                                                                                                                           |
| -------------------- | ------------------------------------------------- | -------- | --------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| **`title`**          | `ReactNode`                                       | ✅       | -         | The title of the product card. If the type of `title` is a string, the default style will be applied.                                 |
| **`extra`**          | `ReactNode`                                       |          | -         | The extra content of the product card.                                                                                                |
| **`body`**           | `ReactNode`                                       |          | -         | The body content of the product card.                                                                                                 |
| **`footer`**         | `ReactNode`                                       |          | -         | The footer content of the product card.                                                                                               |
| **`noImage`**        | `ReactNode`                                       |          | -         | The fallback image when `imageSrc` prop is empty or not provided.                                                                     |
| **`size`**           | `'fill' \| string`                                |          | `"160px"` | The size of the product card. If the size is set to `fill`, the card will be responsive. Anything else will be considered a CSS unit. |
| **`imageSrc`**       | `string`                                          |          | -         | The source URL for the image.                                                                                                         |
| **`imageProps`**     | `Partial<typeof ImageComponent>`                  |          | -         | Additional properties to pass to the image element.                                                                                   |
| **`ImageComponent`** | `ComponentType<typeof ImageComponent>` or `"img"` |          | `"img"`   | The component to use for displaying the image.                                                                                        |
| **`imageOverlay`**   | `ReactNode`                                       |          | -         | Element to display as an overlay on the image.                                                                                        |

## Examples

### Default

By default, the product card will display with its default properties and will function correctly without any additional props.

<ComponentPreviewUI name="product-card-default" />

### Size

A product card that adjusts its size based on the `size` prop. The card can either fill the available space or take up a specified size.

<ComponentPreviewUI name="product-card-size" />

### Extra

A product card that includes extra content, allowing for additional details or actions.

<ComponentPreviewUI name="product-card-extra" />

### With Image

A product card that includes an image.

> By default, without an image, the card will display a gray box.
> It will be the image from `noImage` prop if it's provided.

<ComponentPreviewUI name="product-card-with-image" />

### With Image Overlay

A product card that includes an image with an overlay. This overlay can be used to display additional information or actions.

<ComponentPreviewUI name="product-card-with-image-overlay" />

### With Custom Image Component

A product card that uses a custom image component, allowing for more control over how the image is rendered.

<ComponentPreviewUI name="product-card-with-custom-image-component" />

### With Body

A product card that includes body content, providing space for detailed product information.

<ComponentPreviewUI name="product-card-with-body" />

### With Footer

A product card that includes a footer element, typically used for actions like buttons or links.

<ComponentPreviewUI name="product-card-with-footer" />

## CSS

| Class Name                            | Description                                                 |
| ------------------------------------- | ----------------------------------------------------------- |
| `.ApolloProductCard-root`             | Styles applied to the root container of the product card.   |
| `.ApolloProductCard-mediaContainer`   | Styles applied to the media container within the card.      |
| `.ApolloProductCard-imageContainer`   | Styles applied to the image container within the card.      |
| `.ApolloProductCard-image`            | Styles applied to the image element within the card.        |
| `.ApolloProductCard-noImageBox`       | Styles applied to the no image box element within the card. |
| `.ApolloProductCard-imageOverlay`     | Styles applied to the overlay on top of the image.          |
| `.ApolloProductCard-imageOverlayText` | Styles applied to the overlay text on the image.            |
| `.ApolloProductCard-titleContainer`   | Styles applied to the container holding the title.          |
| `.ApolloProductCard-extraContainer`   | Styles applied to the container holding the extra content.  |
| `.ApolloProductCard-title`            | Styles applied to the title text.                           |
| `.ApolloProductCard-footerContainer`  | Styles applied to the footer container within the card.     |
