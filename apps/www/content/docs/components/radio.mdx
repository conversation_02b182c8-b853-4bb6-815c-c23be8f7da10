---
title: Radio
description: A input is a form element that allows users to select one option from a set of mutually exclusive options.
featured: false
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Radio/RadioProps.ts
---

<ComponentPreviewUI name="radio-demo" />

## Usage

```tsx
import { RadioGroup, RadioGroup } from "@design-systems/apollo-ui"

function MyApp() {
  const [selectedOptions, setSelectedOptions] = useState<number>(1)

  return (
    <RadioGroup value={selectedOptions} onChange={setSelectedOptions}>
      <Radio label="Option1" value={1} />
      <Radio label="Option2" value={2} />
      <Radio label="Disabled Option" disabled value={3} />
    </RadioGroup>
  )
}

```

## Props

### RadioProps

| Name           | Type                                      | Required | Default | Description                                        |
| -------------- | ----------------------------------------- | -------- | ------- | -------------------------------------------------- |
| **`label`**    | `string`                                  |          | -       | Label for the radio input.                         |
| **`onChange`** | `(value: T, event?: ChangeEvent) => void` | ✅       | -       | Callback function when the selected value changes. |
| **`value`**    | `string \| number`                        |          | -       | Value for the radio input.                         |
| **`disabled`** | `boolean`                                 |          | -       | If `true`, the radio input is disabled.            |

### RadioGroupProps

| Name            | Type                                      | Required | Default      | Description                                                |
| --------------- | ----------------------------------------- | -------- | ------------ | ---------------------------------------------------------- |
| **`value`**     | `T`                                       | ✅       | -            | The value of the selected radio input within the group.    |
| **`onChange`**  | `(value: T, event?: ChangeEvent) => void` | ✅       | -            | Callback function when the selected value changes.         |
| **`direction`** | `"vertical"` \| `"horizontal"`            |          | `"vertical"` | Direction of the radio group layout.                       |
| **`disabled`**  | `boolean`                                 |          | -            | If `true`, all radio inputs within the group are disabled. |

## Examples

### Radio Group

This section demonstrates the usage of the `RadioGroup` component. It allows you to
group multiple radio buttons together, managing the selected state and layout direction
(`'vertical'` or `'horizontal'`). This is useful when you need users to select one option
from a list of choices.

<ComponentPreviewUI name="radio-group" />

### Standalone

This section showcases the `Radio` component used independently, outside of a RadioGroup.
It is useful for cases where a single radio button is needed, or when you need more
customized control over the individual radio button's behavior.

<ComponentPreviewUI name="radio-standalone" />
