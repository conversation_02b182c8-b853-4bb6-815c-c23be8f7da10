---
title: Select
description: Displays a select or a component that looks like a select.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Select/SelectProps.ts
---

<ComponentPreviewUI name="select-preview" />

## Usage

```tsx
import { Option, Select } from "@design-systems/apollo-ui"
```

```tsx
<Select defaultValue={10} id="named-select" name="demo-select">
  <Option value={10}>Ten</Option>
  <Option value={20}>Twenty</Option>
  <Option value={30}>Thirty</Option>
</Select>
```

## Props

| Name             | Type                    | Default   | Description                                                                                    |
| ---------------- | ----------------------- | --------- | ---------------------------------------------------------------------------------------------- |
| **`label`**      | `string`                | -         | The label for the select component.                                                            |
| **`helperText`** | `string`                | -         | The helper text for the select component.                                                      |
| **`disabled`**   | `bool`                  | -         | If `true`, the component is disabled.                                                          |
| **`error`**      | `bool`                  | -         | If `true`, the select will indicate an error.                                                  |
| **`variant`**    | `'outline'`             | `outline` | Select variant.                                                                                |
| **`color`**      | `'danger' \| 'primary'` | `primary` | The color of the component. It supports those theme colors that make sense for this component. |
| **`fullWidth`**  | `bool`                  | -         | If `true`, the select will take up the full width of its container.                            |

## Examples

### Label and Helper Text

The select component displaying label and helper text.

<ComponentPreviewUI name="select-label-helper-text" />

### Default

The default outline style of the select component.

<ComponentPreviewUI name="select-preview" />

### Disabled

The select component in a disabled state, preventing user interaction.

<ComponentPreviewUI name="select-disabled" />

### Error

The select component displaying an error state, typically used for validation feedback.

<ComponentPreviewUI name="select-error" />

### Full Width

The select component taking up the full width of its container for better layout control.

<ComponentPreviewUI name="select-fullwidth" />

### Controlled Select

A demonstration of the select component being used as a controlled component, where its state is managed by the parent.

<ComponentPreviewUI name="select-controlled" />

### Multiple Select

Allows users to select multiple options from a dropdown list.

<ComponentPreviewUI name="select-multiple" />

### Custom Display for Selected Value

In some cases, we need to display different text from the option's label.

<ComponentPreviewUI name="select-value-appearance" />
