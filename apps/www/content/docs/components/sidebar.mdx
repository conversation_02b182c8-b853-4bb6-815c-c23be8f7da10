---
title: Sidebar
description: The Sidebar component provides a flexible navigation structure with support for headers, footers, nested groups, and various customization options. Perfect for organizing links and actions within a collapsible layout.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Sidebar/SidebarProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=10925-60359&m=dev
---

<ComponentPreviewUI name="sidebar-demo" />

## Usage

```tsx
import { useState } from "react"
import { Sidebar, Typography, type SidebarMenu } from "@/components"
import { AreaChart, Home, Setting, User } from "@design-systems/apollo-icons"

import { Icons } from "@/components/icons"
import { MenuItem, MenuItemGroup } from "@/components/MenuItem"

export default function SidebarDemo() {
  const [selectedMenu, setSelectedMenu] = useState<string | number>("overview")
  const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>([
    "analytics",
  ])

  const handleExpandStateChange = (key: string | number, expanded: boolean) => {
    setExpandedMenuKeys((prev) =>
      expanded
        ? [...prev, key as string]
        : prev.filter((prevKey) => prevKey !== key)
    )
  }

  return (
    <div className="w-full h-[500px] self-stretch bg-slate-300 border border-border-default overflow-auto">
      <Sidebar
        header={
          <div className="flex flex-row justify-start items-center gap-2 p-4">
            <Icons.logo className="h-6 w-6" />{" "}
            <Typography level="h5">Apollo</Typography>
          </div>
        }
        menus={menuConfig}
        selectedMenuKey={selectedMenu}
        onSelectMenu={setSelectedMenu}
        expandedMenuKeys={expandedMenuKeys}
        onExpandedChange={handleExpandStateChange}
        footer={
          <MenuItemGroup icon={<User />} label="Profile" selected>
            <MenuItem label="My Products" subItem />
          </MenuItemGroup>
        }
        onLogOut={() => {
          alert("Logout!")
        }}
      />
    </div>
  )
}

const menuConfig: SidebarMenu[] = [
  {
    key: "dashboard",
    label: "Dashboard",
    items: [
      {
        key: "home",
        label: "Home",
        icon: <Home />,
        href: "https://www.google.com",
      },
      {
        key: "analytics",
        label: "Analytics",
        icon: <AreaChart />,
        children: [
          {
            key: "overview",
            label: "Overview",
            icon: <AreaChart />,
            onClick() {
              alert("Overview!")
            },
          },
          { key: "reports", label: "Reports", icon: <AreaChart /> },
        ],
      },
      {
        key: "analytics2",
        label: "Analytics #2",
        icon: <AreaChart />,
        children: [
          { key: "overview2", label: "Overview", icon: <AreaChart /> },
          { key: "reports2", label: "Reports", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    items: [
      { key: "projects", label: "Projects", icon: <AreaChart /> },
      { key: "teams", label: "Teams", icon: <AreaChart /> },
      {
        key: "resources",
        label: "Resources",
        icon: <AreaChart />,
        children: [
          { key: "assets", label: "Assets", icon: <AreaChart /> },
          { key: "inventory", label: "Inventory", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "settings",
    label: "Settings",
    items: [
      { key: "profile", label: "Profile", icon: <Home /> },
      { key: "preferences", label: "Preferences", icon: <AreaChart /> },
      {
        key: "notifications",
        label: "Notifications",
        icon: <AreaChart />,
        children: [
          { key: "email", label: "Email", icon: <AreaChart /> },
          { key: "sms", label: "SMS", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "support",
    label: "Support",
    items: [
      { key: "faq", label: "FAQ", icon: <Home /> },
      { key: "contact", label: "Contact Us", icon: <AreaChart /> },
      {
        key: "feedback",
        label: "Feedback",
        icon: <AreaChart />,
        children: [
          { key: "suggestions", label: "Suggestions", icon: <AreaChart /> },
          { key: "complaints", label: "Complaints", icon: <AreaChart /> },
        ],
      },
    ],
  },
]
```

## Props

| Name                    | Type                                                                    | Required | Default   | Description                                                                                              |
| ----------------------- | ----------------------------------------------------------------------- | -------- | --------- | -------------------------------------------------------------------------------------------------------- |
| **`header`**            | `ReactNode`                                                             |          | -         | Custom component or content displayed in the header section of the sidebar.                              |
| **`title`**             | `string`                                                                |          | -         | Used as the sidebar’s title if no value is provided for the header prop.                                 |
| **`logo`**              | `ReactNode`                                                             |          | -         | In case we don't pass any value into header props. the sidebar will use this prop as logo.               |
| **`footer`**            | `ReactNode \| SidebarMenu[]`                                            |          | -         | Custom component or content displayed in the footer section of the sidebar.                              |
| **`menus`**             | `SidebarMenu[]`                                                         | ✅       | -         | Array of menu configurations, which can be either a section (`MenuSectionConfig`) or an individual item. |
| **`expandedMenuKeys`**  | `Array<string \| number>`                                               |          | -         | Keys of expanded menu groups, used to control expanded state for nested groups only.                     |
| **`onExpandedChange`**  | `(key: string \| number, expanded: boolean, groupKey?: string) => void` |          | -         | Callback triggered when the expanded state of a menu group changes.                                      |
| **`selectedMenuKey`**   | `string \| number`                                                      |          | -         | Key of the currently selected menu item.                                                                 |
| **`onSelectMenu`**      | `(key: string \| number, groupKey?: string \| number \| null) => void`  |          | -         | Callback triggered when a menu item is selected.                                                         |
| **`onLogOut`**          | `() => void`                                                            |          | -         | Callback for a logout action.                                                                            |
| **`logOutButtonLabel`** | `string`                                                                |          | -         | Label for the logout button in the sidebar.                                                              |
| **`width`**             | `string`                                                                |          | `'210px'` | Width of the sidebar, inherited from `DrawerProps`.                                                      |

## SidebarMenu

`SidebarMenu` is a union type that can be either a `MenuSectionConfig` or a `MenuItemConfig`, allowing the sidebar to include both sections and individual menu items.

### MenuSectionConfig

| Name        | Type               | Required | Default | Description                                        |
| ----------- | ------------------ | -------- | ------- | -------------------------------------------------- |
| **`key`**   | `string \| number` | ✅       | -       | Unique identifier for the menu section.            |
| **`label`** | `ReactNode`        |          | -       | Label for the section in the sidebar.              |
| **`items`** | `MenuItemConfig[]` | ✅       | -       | Array of menu items and groups within the section. |

### MenuItemConfig

`MenuItemConfig` extends `MenuItemBaseProps`, inheriting its core properties to define individual menu items.

| Name                     | Type                                              | Required | Default | Description                                                                              |
| ------------------------ | ------------------------------------------------- | -------- | ------- | ---------------------------------------------------------------------------------------- |
| **`key`**                | `string                                \| number` | ✅       | -       | Unique identifier for the menu item.                                                     |
| **`label`**              | `ReactNode`                                       | ✅       | -       | Display label of the menu item.                                                          |
| **`icon`**               | `ReactNode`                                       |          | -       | Icon displayed next to the label.                                                        |
| **`onClick`**            | `MouseEventHandler<HTMLButtonElement>`            |          | -       | Click handler for the menu item.                                                         |
| **`LinkComponent`**      | `ComponentType<any>`                              |          | -       | Custom link component to wrap the menu item. Useful for integrating with routers.        |
| **`href`**               | `string`                                          |          | -       | URL for the link if `LinkComponent` is provided.                                         |
| **`linkComponentProps`** | `any`                                             |          | -       | Additional props passed to `LinkComponent`.                                              |
| **`selected`**           | `boolean`                                         |          | -       | Indicates if the menu item is currently selected.                                        |
| **`children`**           | `MenuItemConfig[]`                                |          | -       | Nested array of menu items for menu groups. Only applicable if the item is a group type. |

In this setup, `MenuItemConfig` leverages the properties from `MenuItemBaseProps` for flexibility, allowing for routing, icons, and nested configurations. This provides a comprehensive structure for each menu item within the sidebar.

## Examples

### Header, Footer, and Logout Configuration

The `Sidebar` component can include a custom header, footer, and logout button. Here’s how to set it up:

<ComponentPreviewUI name="sidebar-anatomy-element" />

### Collapsible Sidebar

If we want the sidebar to be collapsible to make the content area wider, we can use the `collapsed` and `onCollapsedChange` props to control its collapsing state.

<ComponentPreviewUI name="sidebar-collapsible" />

### Menu Configuration

Use the `menus` prop to define sections, groups, and items within the sidebar. Each entry can either be a section with its own items or an individual top-level item.

<ComponentPreviewUI name="sidebar-configure-menus" />

#### Example Configuration

```jsx
const menuConfig: SidebarMenu[] = [
  {
    key: "dashboard",
    label: "Dashboard",
    items: [
      { key: "home", label: "Home", icon: <AreaChart /> },
      {
        key: "analytics",
        label: "Analytics",
        icon: <AreaChart />,
        children: [
          { key: "overview", icon: <AreaChart />,label: "Overview" },
          { key: "reports", icon: <AreaChart />,label: "Reports" },
        ],
      },
    ],
  },
  { key: "settings", label: "Settings", icon: <AreaChart /> },
  {
    key: "profile",
    label: "Profile",
    icon: <AreaChart />,
    children: [
      { key: "account", icon: <AreaChart />, label: "Account Settings" },
      { key: "security", icon: <AreaChart />, label: "Security" },
    ],
  },
]

```

### Uncontrolled Expanded Groups

To set initial expanded menu groups, use the `expandedMenuKeys` prop. This allows for uncontrolled behavior where only specific groups start expanded when `onExpandedChange` is not provided.

<ComponentPreviewUI name="sidebar-default-expanded" />

```tsx
function ExampleComponent() {
  return <Sidebar menus={menuConfig} expandedMenuKeys={["analytics"]} />
}
```

### Controlled Expanded Groups

For a controlled expanded state, use the `expandedMenuKeys` prop along with the `onExpandedChange` callback to manage the expanded state of menu groups programmatically.

<ComponentPreviewUI name="sidebar-controlled-expanded" />

### Selected Menu Item

Similar to Expanded group props usage. Use `selectedMenuKey` only to set initial selected menu items or controlled it with `selectedMenuKey` and `onSelectMenu` to track changes in the selected state.

<ComponentPreviewUI name="sidebar-selected-item" />

### Footer menu as configurations

Alternatively, We also make the footer prop could be pass as menu config. So we could pass as we passed on the menu config

<ComponentPreviewUI name="sidebar-footer-with-config" />

### Using as Layout component

We have implement the sidebar as layout component. to make it to be more convinience in case we don't want to wrap the sidebar with container by self.

<ComponentPreviewUI name="sidebar-layout-demo" />

## CSS

| Class Name                                 | Description                                        |
| ------------------------------------------ | -------------------------------------------------- |
| `.ApolloSidebar-container`                 | Styles applied to the main sidebar container       |
| `.ApolloSidebar-headerContainer`           | Styles applied to the header container section     |
| `.ApolloSidebar-menuContainer`             | Styles applied to the menu container section       |
| `.ApolloSidebar-menuSectionContainer`      | Styles applied to each menu section container      |
| `.ApolloSidebar-menuSectionLabelContainer` | Styles applied to the menu section label container |
| `.ApolloSidebar-menuItem`                  | Styles applied to individual menu items            |
| `.ApolloSidebar-menuGroup`                 | Styles applied to the menu group container         |
| `.ApolloSidebar-footerContainer`           | Styles applied to the footer container             |
