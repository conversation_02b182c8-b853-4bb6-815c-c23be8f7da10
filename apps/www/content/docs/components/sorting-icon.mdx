---
title: Sorting Icon
description: The SortingIcon component indicates the sorting status of a table, providing users with a visual representation of the sorting state.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/SortingIcon/SortingIconProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=10881-37753&t=AGPtHXawTtbaZyy2-1
---

<ComponentPreviewUI name="sorting-icon-demo" />

## Usage

```tsx
import { SortingIcon } from "@design-systems/apollo-ui"

function MyTable() {
  return <SortingIcon status="asc" />
}
```

## Props

> Note: The SortingIcon component props extend HTMLAttributes\<HTMLSpanElement\>, which allows you to pass any standard HTML attributes (like id, style, etc.) to the underlying \<span\> element.

| Name         | Type                               | Required | Default     | Description                                                                                          |
| ------------ | ---------------------------------- | -------- | ----------- | ---------------------------------------------------------------------------------------------------- |
| **`status`** | `"default"  \| "asc"    \| "desc"` |          | `"default"` | The sorting status of the table column. It can be one of the following: `default`, `asc`, or `desc`. |

## CSS

| Class Name                    | Description                                             |
| ----------------------------- | ------------------------------------------------------- |
| `.ApolloSortingIcon-root`     | Styles applied to the root container of the SortingIcon |
| `.ApolloSortingIcon-iconASC`  | Styles applied to the ascending arrow icon (CaretUp)    |
| `.ApolloSortingIcon-iconDESC` | Styles applied to the descending arrow icon (CaretDown) |
