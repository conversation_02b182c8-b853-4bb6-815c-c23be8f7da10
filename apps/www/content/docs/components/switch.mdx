---
title: Switch
description: Displays a switch component.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Switch/SwitchProps.ts
---


<ComponentPreviewUI name="switch-demo" />

## Usage

```tsx
import { Switch } from "@design-systems/apollo-ui"
```

## Props

| Name                | Type                                                              | Default | require |
|---------------------|-------------------------------------------------------------------|---------|---------|
| labelPlacement      | `top`, `bottom`, `left`, `right`                                  | `right` | no      |
| label               | string                                                            | "On"    | no      |
| disabled            | boolean                                                           | false   | no      |
| ...otherSwitchProps | https://mui.com/base-ui/react-switch/components-api/#switch-props |         |         |
| ...otherSwitchSlots | https://mui.com/base-ui/react-switch/components-api/#switch-slots |         |         |

## CSS classes

| ClassName             | Rule name                                                           | description                              |
|-----------------------|---------------------------------------------------------------------|------------------------------------------|
| `.ApolloSwitch-root`  | Root                                                                | Styles applied to the Root element       |
| `.ApolloSwitch-label` | string                                                              | Styles applied to the Typography element |
| ...otherCssClasses    | https://mui.com/base-ui/react-switch/components-api/#switch-classes |                                          |
