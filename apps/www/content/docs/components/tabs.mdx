---
title: Tab
description: The Tab component displays a series of tabs and corresponding panels for organizing content.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Tabs/TabsProps.ts
---

<ComponentPreviewUI name="tabs-demo" />

## Props

To properly implement the Tabs component, you need to use the following elements:

- `Tabs`: The main container that holds the entire tab system.
- `TabList`: A wrapper for the individual tabs, organizing them into a list.
- `Tab`: Represents each clickable tab that users can select to view different content.
- `TabPanel`: The content area that is displayed when the corresponding tab is selected.

All these components work together to create a functional tab interface, where clicking on a Tab will display its related TabPanel.

### Tabs

| Name               | Type             | Required | Default | Description                                                                                                 |
| ------------------ | ---------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------- |
| **`defaultValue`** | `number, string` |          | -       | The default value. Use when the component is not controlled.                                                |
| **`value`**        | `number, string` |          | -       | The value of the currently selected Tab. If you don't want any selected Tab, you can set this prop to null. |

### TabList

| Name                | Type     | Required | Default | Description                        |
| ------------------- | -------- | -------- | ------- | ---------------------------------- |
| **`children`**      | `node`   |          | -       | The content of the component.      |
| **`idButtonLeft`**  | `string` |          | -       | The id applied to the button left  |
| **`idButtonRight`** | `string` |          | -       | The id applied to the button right |

### Tab

| Name           | Type              | Required | Default  | Description                                                                           |
| -------------- | ----------------- | -------- | -------- | ------------------------------------------------------------------------------------- |
| **`value`**    | `number, string`  |          | -        | You can provide your own value. Otherwise, it falls back to the child position index. |
| **`variant`**  | `'fit' \| 'fill'` |          | `'fill'` | You can provide your own value. Otherwise, it falls back to the child position index. |
| **`onChange`** | `func`            |          | -        | Callback invoked when new value is being set.                                         |
| **`disabled`** | `bool`            |          | `false`  | If true, the component is disabled.                                                   |

### TabPanel

| Name           | Type             | Required | Default | Description                                                                                                                                                                                                                                                                   |
| -------------- | ---------------- | -------- | ------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **`children`** | `node`           |          | -       | The content of the component.                                                                                                                                                                                                                                                 |
| **`value`**    | `number, string` |          | -       | The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected. If not provided, it will fall back to the index of the panel. It is recommended to explicitly provide it, as it's required for the tab panel to be rendered on the server. |

## Examples

### Default

The example of usage by default

<ComponentPreviewUI name="tabs-demo" />

### Variant

The `fill` and `fit` variants control the tab layout behavior:

- **Fill**: Tabs will fill the available space evenly.
- **Fit**: Tabs will fit their content naturally without expanding.

<ComponentPreviewUI name="tabs-variants" />

## CSS

| Class Name                    | Description                        |
| ----------------------------- | ---------------------------------- |
| `.ApolloTabList-button-left`  | Styles applied to the button left  |
| `.ApolloTabList-button-right` | Styles applied to the button right |
