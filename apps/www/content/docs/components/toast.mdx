---
title: Toast
description: The Toast component displays brief notifications to inform users of events or status changes.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Toast/ToastProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=380-5268&node-type=canvas
---

<ComponentPreviewUI name="toast-with-hook" />

## Usage

To using toast. We will focusing on a hook name `useToast` with provided provider on the top level of the app `ToastProvider`

```tsx
import { Toast } from "@design-systems/apollo-ui"

function MyAppLayout() {
  const options = { ... } // Similar to notistack SnackbarProvider props
  return (
    <ToastProvider {...options}>
      <App />
    </ToastProvider>
  )
}

function App() {
  const { showSuccessToast, showErrorToast } = useToast()
  return (
    <div className="flex flex-col gap-2">
      <Button
        onClick={() =>
          showSuccessToast({
            title: "Sucess",
            endDecorator: (
              <Button variant="outline" size="md">
                Detail
              </Button>
            ),
            description: "Lorem ipsum ipsum",
          })
        }
      >
        Show Success
      </Button>
      <Button
        onClick={() =>
          showErrorToast({
            title: "Error",
            description: "Lorem ipsum ipsum",
          })
        }
      >
        Show Error
      </Button>
    </div>
  )
}
```

## Hook Options

the `useToast` extends [notistack](https://notistack.com/). For more information, We could check on the document

## Frequent Using Options

> Note: the available `options` will be the same as `enqueueSnackbar` options but extended with `AlertProps`

| Name                 | Type        | Required | Default | Description                                     |
| -------------------- | ----------- | -------- | ------- | ----------------------------------------------- |
| **`title`**          | `string`    | ✅       | -       | The main title of the toast.                    |
| **`description`**    | `string`    |          | -       | An optional description providing more details. |
| **`startDecorator`** | `ReactNode` |          | -       | The element displayed at the begining of alert. |
| **`endDecorator`**   | `ReactNode` |          | -       | The element displayed at the end of alert       |

## Examples

### Using Hook (Recommended)

Show the toast by using `useToast` hook.

To ensure the useToast hook works correctly, you must wrap your component tree with `ToastProvider` at a higher level, as it’s necessary for providing the required context.

> Note: This hook extends [notistack](https://notistack.com/). For more information, We could check on the document

<ComponentPreviewUI name="toast-with-hook" />

## CSS

| Class Name                     | Description                                           |
| ------------------------------ | ----------------------------------------------------- |
| `.ApolloToast-root`            | Styles applied to the root container                  |
| `.ApolloToast-title`           | Styles applied to the title                           |
| `.ApolloToast-description`     | Styles applied to the description text                |
| `.ApolloToast-icon`            | Styles applied to the start decorator icon            |
| `.ApolloToast-portalContainer` | Styles applied to the portal of toast (ToastProvider) |

## Legacy 🚨

> The content below are deprecated. Please avoids to use it.

## Props

> Note: This component extends [Snackbar Props](https://mui.com/base-ui/react-snackbar/components-api/#snackbar-props) and [Snackbar Slots](https://mui.com/base-ui/react-snackbar/components-api/#snackbar-slots) from `@mui/base-ui/react-snackbar`.

| Name                   | Type                                                                                                        | Required | Default        | Description                                                                 |
| ---------------------- | ----------------------------------------------------------------------------------------------------------- | -------- | -------------- | --------------------------------------------------------------------------- |
| **`autoHideDuration`** | `number`                                                                                                    |          | `3000`         | The duration in milliseconds after which the toast will automatically hide. |
| **`transitionTime`**   | `number`                                                                                                    |          | `300`          | The time taken for the transition effect when the toast appears/disappears. |
| **`lineClamp`**        | `number`                                                                                                    |          | `2`            | Limits the number of lines for the toast description text.                  |
| **`position`**         | `"top-center"` \| `"top-right"` \| `"top-left"` \| `"bottom-center"` \| `"bottom-right"` \| `"bottom-left"` |          | `"top-center"` | The position where the toast will appear on the screen.                     |
| **`severity`**         | `"success"` \| `"info"` \| `"warning"` \| `"error"`                                                         |          | `"success"`    | Defines the type of toast (success, info, warning, error).                  |
| **`startDecorator`**   | `ReactNode`                                                                                                 |          | -              | Optional icon or element placed at the start of the toast.                  |
| **`title`**            | `string`                                                                                                    | ✅       | -              | The main title of the toast.                                                |
| **`description`**      | `string`                                                                                                    |          | -              | An optional description providing more details.                             |

### Default

By default, the Toast component will automatically hide after a set duration.

> To be easier to use. Please use `useToast` instead.

<ComponentPreviewUI name="toast-demo" />
