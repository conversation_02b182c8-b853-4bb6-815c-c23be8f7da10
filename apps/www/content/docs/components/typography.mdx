---
title: Typography
description: The Typography component helps present design and content clearly and efficiently.
featured: true
component: true
published: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Typography/TypographyProps.ts
---

<ComponentPreviewUI name="typography-preview" />

## Usage

```tsx
import { Typography } from "@design-systems/apollo-ui"
```

```tsx
<Typography>Button</Typography>
```

## Props

| Name                 | Type                                                   | Default   | Description                                                                                   |
| -------------------- | ------------------------------------------------------ | --------- | --------------------------------------------------------------------------------------------- |
| **`align`**          | `'center'\|'inherit'\|'justify'\|'left'\|'right'`      | `left`    | the `text-align` of the component.                                                            |
| **`color`**          | `'danger' \| 'primary'`                                | `primary` | The color of the component.                                                                   |
| **`level`**          | `'h1' 'h2' 'h3' 'h4' 'h5' 'body-1' 'body-2' 'caption'` | `body-1`  | Applies the typographic scale.                                                                |
| **`startDecorator`** | `node`                                                 | -         | Element placed before the children.                                                           |
| **`endDecorator`**   | `node`                                                 | -         | Element placed after the children.                                                            |
| **`noWrap`**         | `bool`                                                 | `false`   | If `true`, the text will not wrap. but instead will truncate with a `text-overflow: ellipsis` |
| **`gutterBottom`**   | `bool`                                                 | `false`   | If `true`, the text will have a bottom margin.                                                |

## Examples

### level

- `h1` = `1.75rem`
- `h2` = `1.5rem`
- `h3` = `1.25rem`
- `h4` = `1rem`
- `h5` = `0.875rem`
- `body-1` = `1rem`
- `body-2` = `0.875rem`
- `caption` = `0.75rem`

<Callout className="mt-6 bg-surface-static-ui-primary dark:bg-transparent">
we uses `rem` (relative units) for the font size. The browser `<html>` element default font size is `16px`, but browsers have an option to change this value,
`rem` units allow us to accommodate the user's settings, resulting in a better accessibility support.
</Callout>

for example if `body-1`=`16px`, then `h5`=`14px`, `h4`=`16px`, `h3`=`20px`,`h2`=`24px`, `h1`=`28px` and so on.
by setting global css (eg. `html { font-size: 20px }`), the `<Typography />`'s ratio will changes accordingly.

<ComponentPreviewUI name="typography-demo" />

### align

`align` works with `block-level` element, which means `level='caption'` by default won't effected by `text-align` since `<span/>` is `inline-level`
[read more](https://www.w3schools.com/html/html_blocks.asp)

<ComponentPreviewUI name="typography-align" />

### overriding default style

- using `className` if you prefer using `Tailwind`.
- [inline style](https://www.w3schools.com/react/react_css.asp) is also allowed.

<ComponentPreviewUI name="typography-override" />
