---
title: Upload Box
description: A component that allows users to upload files with validation for file types, sizes, and limits on the number of files.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Accordion/AccordionProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=9786-1716&node-type=text&m=dev
---

<ComponentPreviewUI name="upload-box-demo" />

## Usage

```tsx
import {
  Typography,
  UploadBox,
  useUploadMultipleFile,
  useUploadSingleFile,
} from "@design-systems/apollo-ui"

function MyFeature() {
  const uploadFile = async (file: File): Promise<File> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(file)
      }, 3000)
    })
  }

  const singleFileUploadBoxProps = useUploadSingleFile({
    uploadFileFn: uploadFile,
    onDelete: () => {
      console.log("[DEBUG] event> delete file")
    },
    onCancelUpload: (file) => {
      console.log("[DEBUG] event> cancel upload:", file)
    },
  })

  const multipleFileUploadBoxProps = useUploadMultipleFile({
    uploadFileFn: uploadFile,
    onDelete: (fileIndex) => {
      console.log("[DEBUG] event> delete file index: ", fileIndex)
    },
    onCancelUpload: (file, fileIndex) => {
      console.log(
        "[DEBUG] event> cancel upload file index and file:",
        fileIndex,
        file
      )
    },
  })

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <Typography level="h4">Single File</Typography>
      <UploadBox
        {...singleFileUploadBoxProps}
        fullWidth
        label="File Upload: (Loading State)"
        allowedFilesExtension={["jpg", "png"]}
        required
        maxFileSizeInBytes={24 * 1024 * 1024}
      />
      <br />
      <Typography level="h4">Multiple File</Typography>
      <UploadBox
        {...multipleFileUploadBoxProps}
        fullWidth
        label="File Upload: (Loading State)"
        allowedFilesExtension={["jpg", "png"]}
        required
        maxFileSizeInBytes={24 * 1024 * 1024}
        multiple
      />
    </div>
  )
}
```

## Props

> Note: The prop types for `UploadBox` extend the `FormControlProps` type. [Ref](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/FormControl/FormControlProps.ts)

| Name                        | Type                                                                          | Required | Default                 | Description                                                                                              |
| --------------------------- | ----------------------------------------------------------------------------- | -------- | ----------------------- | -------------------------------------------------------------------------------------------------------- |
| **`value`**                 | `File \| File[] \| null`                                                      |          | -                       | The value of the upload box. It could be a single file or an array of files depending on `multiple`.     |
| **`loading`**               | `boolean`                                                                     |          | -                       | Show the loading indicator and disabled upload button when the value is `true`.                          |
| **`multiple`**              | `boolean`                                                                     |          | -                       | Determines if multiple files can be uploaded. If `true`, multiple files can be uploaded.                 |
| **`errorMessage`**          | `string`                                                                      |          | -                       | Message to be displayed if there's an error.                                                             |
| **`allowedFilesExtension`** | `string[]`                                                                    |          | `['jpg','png','svg']`   | An array of allowed file extensions.                                                                     |
| **`maxFileSizeInBytes`**    | `number`                                                                      |          | `5 * 1024 * 1024` (5MB) | Maximum size for each uploaded file, in bytes.                                                           |
| **`fileLimit`**             | `number`                                                                      |          | `6`                     | The maximum number of files that can be uploaded.                                                        |
| **`onDelete`**              | `Multiple extends true ? (fileIndex: number) => void : () => void`            |          | -                       | Callback function for deleting files. The behavior changes based on whether `multiple` is true or false. |
| **`onUpload`**              | `(files: File \| File[]) => void`                                             |          | -                       | Callback function triggered when files are uploaded.                                                     |
| **`label`**                 | `string`                                                                      |          | -                       | Label to be displayed for the form control.                                                              |
| **`helperText`**            | `string`                                                                      |          | -                       | Helper text to provide additional information about the input.                                           |
| **`fullWidth`**             | `boolean`                                                                     |          | -                       | If `true`, the form control takes up the full width of its container.                                    |
| **`renderErrorMessage`**    | `(state: {errors: { code: string; message: string}[]}) => ReactNode`          |          | -                       | The element renderer for error message alert.                                                            |
| **`renderDescrition`**      | `(state: {errors: { code: string; message: string}[]}) => ReactNode`          |          | -                       | The element renderer for description section.                                                            |
| **`fileState`**             | `(Multiple extends true ? UploadBoxFileState[] : UploadBoxFileState) \| null` |          | -                       | The state of the uploaded files, can be a single file or an array based on the `multiple` prop.          |
| **`onCancelUpload`**        | `(file: UploadBoxFile<false>, index: number) => void`                         |          | -                       | Callback function triggered when a file upload is canceled.                                              |

## Types

| Type                        | Definition                                                                                    | Description                                                                                                                                                |
| --------------------------- | --------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **`UploadBoxFileState`**    | `{ key: string; uploading?: boolean; errorMessage?: string }`                                 | Represents the state of an individual uploaded file, including a unique key, upload status, and error message if applicable.                               |
| **`UploadBoxFileType`**     | `Multiple extends boolean = false, FileType = any`                                            | Determines if the type should be an array of files (for `multiple` mode) or a single file.                                                                 |
| **`UploadBoxBaseFileType`** | `{ name: string }`                                                                            | The base file type, containing only a `name` property.                                                                                                     |
| **`UploadBoxFile`**         | `Multiple extends boolean = false, FileType extends { name: string } = UploadBoxBaseFileType` | Defines the file type, which can either be a single file or an array of files depending on `multiple`. Includes the `name` property of the base file type. |

## Examples

### Default

Basic example of using the `UploadBox` component to upload files.

<ComponentPreviewUI name="upload-box-demo" />

### Multiple Mode

Example of using `UploadBox` with the `multiple` prop enabled to upload multiple files.

<ComponentPreviewUI name="upload-box-multiple" />

### Loading State

In reality, We might need to show the loading state because of the file upload are take seconds or minutes. During that time we need to show the user that the file still on progress.

<ComponentPreviewUI name="upload-box-loading-state" />

### File Type and Size Limitation

Example demonstrating how to restrict file types and file size in the `UploadBox` component.

<ComponentPreviewUI name="upload-box-allowed-file-type-and-limit-size" />

### Error State

Example showing how the `UploadBox` displays error messages when there’s an issue with file upload.

<ComponentPreviewUI name="upload-box-error-state" />

### File Limit (Only Multiple mode)

Example demonstrating how to limit the number of files uploaded when in `multiple` mode.

<ComponentPreviewUI name="upload-box-file-limit" />

### Custom Message Element ( Error Alert / Description)

In case the default UI is not match with our requirement. We can use props to custom the alert and description UI using `renderErrorMessage` or `renderDescrition`.

<ComponentPreviewUI name="upload-box-custom-messsage-ui" />

## CSS

| Class Name                                      | Description                                                   |
| ----------------------------------------------- | ------------------------------------------------------------- |
| `.ApolloUploadBox-formControl`                  | Styles applied to the form control container                  |
| `.ApolloUploadBox-uploadSection`                | Styles applied to the upload section wrapper                  |
| `.ApolloUploadBox-fileConditionContainer`       | Styles applied to the file condition container                |
| `.ApolloUploadBox-fileConditionList`            | Styles applied to the list of file conditions                 |
| `.ApolloUploadBox-uploadButton`                 | Styles applied to the upload button                           |
| `.ApolloUploadBox-uploadedSingleModeFileItem`   | Styles applied to the uploaded file item in single mode       |
| `.ApolloUploadBox-uploadedFileList`             | Styles applied to the uploaded file list container (multiple) |
| `.ApolloUploadBox-uploadedFilesCount`           | Styles applied to the uploaded files count text               |
| `.ApolloUploadBox-uploadedMultipleModeFileList` | Styles applied to the uploaded file list in multiple mode     |
| `.ApolloUploadBox-uploadedMultipleModeFileItem` | Styles applied to each uploaded file item in multiple mode    |
