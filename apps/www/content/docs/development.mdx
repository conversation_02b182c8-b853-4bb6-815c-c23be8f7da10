---
title: Contributing & Development
description: help us develop this project
links:
  edit: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/edit/main/apps/www/content/docs/development.mdx
---

Thank you for your interest in contributing to `@design-systems/apollo-ui`! Please feel free to put up a PR for any issue, feature request or enhancement.

Even if you have little to no experience with `Tailwind CSS`, `JavaScript` or `React`, we'd be more than happy to help you with any information or guidance in order to fulfill your PR.

## Tech Stacks

- 🚀 Turborepo, monorepo system
- 🧱 BaseUI, a headless ui from `MUI`
- ✨ TailwindCSS, (v3) CSS framework
- 📦 Rollup, module bundler
- 📐 Commitlint, Lint commit messages

## Development Setup

- currently, `@design-systems/apollo-tokens` (being used for developing `@design-systems/apollo-ui`) has been published in [custom registry](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/packages), in order to install the package you can choose **one of the following methods**.
  - rename **`.npmrc.example`** to **`.npmrc`** and replace **`ACCESS_TOKEN`** with actual ones.
  - or run **`npm config set -- //gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=${AUTH_TOKEN}`**

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
please contact maintainers for `AUTH_TOKEN`
</Callout>

this project use [`Turberepo`](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks) to manange monorepo (for `documentation` and `ui component`). you need to execute the following commands after cloning the repository.

**0. Cloning the project**

```bash
<NAME_EMAIL>:cjexpress/design-systems/apollo.git

# go into the project
cd apollo
```

<Callout className="mt-6 bg-surface-static-success-default text-surface-action-primary-default dark:bg-transparent">
make sure you use `node >= 18`. if not, we recommend using [`nvm`](https://github.com/nvm-sh/nvm?tab=readme-ov-file#installing-and-updating)
then run `nvm install 18.17.0 && nvm use 18.17.0`
</Callout>

<Callout className="mt-6 bg-surface-static-success-default text-surface-action-primary-default dark:bg-transparent">
make sure you already install [`pnpm`](https://pnpm.io/installation)
</Callout>

**1. Install dependencies**

```bash
pnpm install
```

**2. Run the project**

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
sometimes you might wanted to run `pnpm build` first. for building `apollo-icons` that will be used in `apollo-ui` component [example from sourcecodes](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Select/Select.tsx#L14)
</Callout>

```bash
pnpm dev
```

**3. go to http://localhost:3001 and start developing (all the components live at `/packages/ui/components`)**
<Image src="/images/dev-mode.png" width={300} height={100} className="w-full bg-black rounded-lg"/>

**4. try to build the project to see whather it's working or not**

```bash
pnpm build
```

<Callout slotClass="[&>*]:!leading-[1.6rem] [&>*]:!mt-0" className="mt-6 border-none rounded-b-none bg-black text-white dark:bg-transparent [&>div>p>code]:text-black">
<Image src="/images/turborepo-logo.svg" width={30} height={30} className="" />
by running `pnpm build` (or any script), `Turborepo` will run the workspace's script simultaneously (in this example `build` script (in `package.json`) at `apps/www` and `packages/ui`  will get called)
[read more](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks).
</Callout>
<Image src="/images/turbo-repo-runing-tasks.webp" width={400} height={100} className="rounded-b-lg w-full" />
<p className="text-xs text-center mt-2">src: https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks</p>


## Structure

There are different files and folders inside the `@design-systems/apollo-ui` directory:
This repository is structured as follows:

```bash
apps
└── www
    ├── app
    └── ...
packages
├── icons
└── ui
    └── src
        ├── components
        │    └── Button
        │    └── ...
        └── utils
tools
├── eslint-config
├── typescript-config
└── build-icon
```

| Path           | Description                                                                                   |
| -------------- | ----------------------------------------------------------------------------------------------|
| `apps/www/app` | The documentation website (this site you're reading now). (`pnpm --filter=www`)               |
| `packages/ui`  | ui components package. (`pnpm --filter=@design-systems/apollo-ui`)                            |
| `packages/icons`  | apollo icon collections package. (`pnpm --filter=@design-systems/apollo-icons`)            |

1. documentation(`apps/www/app`): all of the documentation pages are there as `mdx` files, if you want to fix any issue, typo or add something new then you need to add it right there.
2. packages(`packages/ui`): all of the components live here, [`@apollo/token`](https://gitlab.cjexpress.io/cjexpress/design-systems/tokens) is being bundled as a single package along with the components when you run `pnpm build`, this is an intention to reduce an installation steps, if you're curious please look at the [rollup.config.js](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/rollup.config.js) file.
3. packages(`packages/icons`): all of the default icons live here.

## Reporting Issues & Features Requests

If you notice any bugs in the code, see some code that can be improved, or have features you would like to be added, please create a [bug report](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/issues/new) or a [feature request](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/issues/new)!

If you want to open a PR that fixes a bug or adds a feature, then we can't thank you enough!

## Working on Issues

Please feel free to take on any issue that's currently open. Just send a comment in order to let us know you're working on it so we can assign that specific issue to you.

## Opening a Pull Request

`@apollo/token` is an open-source project, so pull requests are always welcomed.
What we ask you, is that before working on a large change, it is best to open an issue first to discuss it with the maintainers or if an issue was already opened, comment your intention of opening up a PR.

When in doubt, keep your pull requests small. To give a PR the best chance of getting accepted, don't bundle more than one feature or bug fix per pull request. It's always best to create two smaller PRs than one big one.

### Commit Formatting

Every file changed should have its own commit message, please don't do one commit for multiple changes.

### contributing

- commit msg follows [Commitlint's convention rules](https://github.com/conventional-changelog/commitlint/tree/master/%40commitlint/config-conventional#type-enum) (subject to change, eg.depends on Jira Card task number)

### commit msg rules

Must be one of the following:

- `build:` Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)
- `ci:` Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)
- `docs:` Documentation only changes
- `feat:` A new feature
- `fix:` A bug fix
- `perf:` A code change that improves performance
- `refactor:` A code change that neither fixes a bug nor adds a feature
- `style:` Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- `test:` Adding missing tests or correcting existing tests
- `chore:` all changes to the repository that do not fit into any of the above categories
- `revert:`

## publishing package

somtimes you might wanted to test publishing the package locally.
luckily [`Verdaccio`](https://verdaccio.org/docs/what-is-verdaccio) got your back.

basically, `Verdaccio` is a local proxy npm registry.

<Steps>
  <Step> install `Verdaccio` </Step>

  depends on what package manager you're using

  ```bash
  # npm
  npm install --location=global verdaccio

  # yarn
  yarn global add verdaccio

  # pnpm
  pnpm install -g verdaccio
  ```

  then spin up the server (default port: `4873`)

  ```bash
  verdaccio
  ```
  <Step> setup npm </Step>

  copy texts in `.npmrc.dev.example` into `.npmrc` (by doing this we're using local registry instead of the actual one)

  <Step> dry-run </Step>

  make sure you're at the `root` then run

  ```bash
  pnpm release:publish:dry-run
  ```

  <Step> access local registry </Step>

  go to `http://localhost:4873` you'll see the published packages.

</Steps>
