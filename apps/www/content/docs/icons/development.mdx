---
title: Development
description: development guideline for apollo-icons
toc: false
links:
  project: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/icons
---

```bash title=""
# make sure you're at the root project
pnpm --filter=@design-systems/apollo-icons build
```

the above script will run `pnpm clean && pnpm build:icons && pnpm build:bundles` where

- `pnpm clean`: will remove old build folder (`dist` folder).
- `pnpm build:icons`: prepare icons for bundling.
- `build:bundles`: will bundle files with different formats (`cjs`, `esm`, `umd`) and generate type declaration files.


## steps for adding new icon

- make sure you're at the root folder
- put new svg file into `/icons` folder

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
currently, we NEED to manually set `fill: 'currentColor'` to inner `<path>` for svg file

by setting `fill: currentColor` to inner `<path>` we can dynamically set color of svg file from any wrapper (eg. when wrapping `apollo-icons` with `<Icons>`)
</Callout>

- run `pnpm --filter=apollo-build-icons normalize --inputFile=YOUR_NEW_SVG_FILE`, replace `YOUR_NEW_SVG_FILE` with new svg file (eg. `--inputFile=new_avatar.svg`)
  - `normalize` script will modify target `.svg` (eg. collapse multiple paths (eg. `<g>`,`<path>`) into single path) purposely for being parsed and building react element via `React.createElement` later. [see source codes](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/icons/src/createIcon.ts#L51)

- run `pnpm --filter=@design-systems/apollo-icons build`, then you can use new svg as `React` component eg. `import { new_svg_file } from "@design-systems/apollo-icons"`
- done
