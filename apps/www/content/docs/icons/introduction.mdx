---
title: Icon Collections
description: list of icon collections provided by apollo.
toc: false
links:
  project: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/icons
---

<Callout className="mt-6 bg-surface-static-success-default text-surface-action-primary-default dark:bg-transparent">
`icons` is a collection of default icons, and can be installed independently.
if you already setting up `.npmrc` you should be fine installing this.
otherwise `.npmrc` is required.

please see [installation](https://cjexpress.pages-gitlab.cjexpress.io/design-systems/apollo/docs/installation) for more details.
</Callout>

<Steps>
<Step>install package:</Step>

then you can use any package management to install the library:
```bash title=""
# with pnpm
pnpm add @design-systems/apollo-icons
# with yarn
yarn add @design-systems/apollo-icons
# with npm
npm install @design-systems/apollo-icons
```

<Step>example usage:</Step>

```js  title="index.jsx"
import { <PERSON><PERSON> } from "@design-systems/apollo-icons"

<Alert />
```

<Callout className="mt-6 bg-surface-static-success-default text-surface-action-primary-default dark:bg-transparent">
click at the icon to copy to clipboard.
</Callout>
</Steps>

<IconCollectionsComponent />

