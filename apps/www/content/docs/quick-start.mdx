---
title: Installation & Basic Usages
description: install library at ease.
links:
  edit: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/edit/main/apps/www/content/docs/installation.mdx
---

## Installation

<Tabs defaultValue="manual">

<TabsContent value="manual">

<Steps>

<Step>pre-configuration:</Step>

to install `apollo/ui` package, you need to configure `.npmrc` (you have to create this file (at project's root) first) to point to custom registry

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>
```

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
  please contact maintainers for `AUTH_TOKEN`
</Callout>

<Step>install package:</Step>

<Callout className="mt-6 bg-surface-static-success-default text-surface-action-primary-default dark:bg-transparent">
  you should ensure that [`tailwind`](https://tailwindcss.com/docs/installation)
  is installed and setup before installing `@design-systems/apollo-ui`
</Callout>

then you can use any package management to install the library:

```bash title=""
# with pnpm
pnpm add @design-systems/apollo-ui
# with yarn
yarn add @design-systems/apollo-ui
# with npm
npm install @design-systems/apollo-ui
```

<Step>setup `Tailwind` configuration:</Step>

use `withApollo` to apply `@design-systems/apollo-ui`'s default configuration, you can use the following `tailwind.config.js` (in this example we use `next.js` page routing folder structure)
for any additional `Tailwind` Configuration, please read [docs](https://tailwindcss.com/docs/configuration)

```js {1,6} title="tailwind.config.js"
const { withApollo } = require("@design-systems/apollo-ui")

// any additional Tailwind's config
// can be passed as an argument
// and it'll be merged to UI library's preset
module.exports = withApollo({
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}", // depends on the setup/folder structure
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
})
```

<Step>Add `ThemeProvider` into the app</Step>

To ensure consistent styling throughout your application, you need to use the ThemeProvider from the `@design-systems/apollo-ui` package.

This provider will inject all design token CSS variables into the DOM.

```js
import { ThemeProvider } from "@design-systems/apollo-ui"

const appTheme = createTheme()

function App({ children }) {
  return <ThemeProvider theme={appTheme}>{children}</ThemeProvider>
}
```

</Steps>
</TabsContent>
</Tabs>

## Usages

just import component and use

```jsx {1,4}
import { Button } from "@design-systems/apollo-ui"

export default () => {
  return <Button>Hello World</Button>
}
```

using integrated tokens from `@design-systems/apollo-tokens` (no need to install since it's already bundled when installing `@design-systems/apollo-ui`)
[see documentation here](https://cjexpress.pages-gitlab.cjexpress.io/design-systems/apollo/docs/tokens/colors)

```jsx {1,4}
import { Button } from "@design-systems/apollo-ui"

export default () => {
  return (
    <Button className="bg-surface-action-primary-default">Hello World</Button>
  )
}
```
