---
title: Colors
description: default design tokens colors.
featured: true
component: true
links:
  project: https://gitlab.cjexpress.io/cjexpress/design-systems/tokens
---

## Core tokens

`core` means the origin of all design intent, (similarly, a [`Seed`](https://ant.design/docs/react/customize-theme#seed-token) token in [`AntD`](https://ant.design/)'s term)

<div className="mt-10 overflow-hidden">
  <ColorPalette type="core" />
</div>


### Tailwind class

eg. `color-green-10` is equivalent to `#f5fff7`


## Alias tokens

`alias` derived from `core` token.

by design, we will use `alias` token instead of using `core` token directly.

<div className="mt-10 grid grid-cols-4 gap-x-4 gap-y-8">
  <ColorPaletteAlias />
</div>
