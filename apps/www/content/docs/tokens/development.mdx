---
title: Development
description: development guideline for apollo tokens.
links:
  project: https://gitlab.cjexpress.io/cjexpress/design-systems/tokens
---

```bash title=""
yarn build
```

the above script will run `yarn build:raw && yarn extract:raw && yarn build:sd && yarn build:sdtw` where

- `yarn build:raw`: resolve `raw-token.json` (token exported/synced from `Figma`) then output a file called `core-resolved.json`
- `yarn extract:raw`: get specific field from `core-resolved.json` and output file called `core.json`
- `yarn build:sd`: build design-tokens by using `Style Dictionary` library.
- `yarn build:sdtw`: build design-tokens by using `Style Dictionary` library but specific for using with `TailwindCSS`.
