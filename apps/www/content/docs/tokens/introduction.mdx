---
title: Design Tokens
description: design tokens informations.
links:
  project: https://gitlab.cjexpress.io/cjexpress/design-systems/tokens
---

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
In most cases, it is typically unnecessary to install the `@design-systems/apollo-tokens` package separately, as it is already integrated within the `@design-systems/apollo-ui`.

unless you specifically intend to develop UI components utilizing a particular framework, such as `AntD`.
</Callout>

`design tokens` using [`Style Dictionary`](https://amzn.github.io/style-dictionary/#/) and [`@tokens-studio/sd-transforms`](https://github.com/tokens-studio/sd-transforms#readme) to parse tokens.
the repository is managed separately from `@design-systems/apollo-ui`, for more flexible to "sync" with `Figma` (everytime designer push/pull an updated tokens).

<Steps>
  <Step><Image src="/images/tokens-00.png" width={300} height={100} className="w-full bg-white rounded-lg"/></Step>
  <Step><Image src="/images/tokens-01.png" width={300} height={100} className="w-full bg-white rounded-lg"/></Step>
  <Step><Image src="/images/tokens-02.png" width={300} height={100} className="w-full bg-white rounded-lg"/></Step>
</Steps>

### Category/Type/Item structure (CTI)

`Style Dictionary` suggested how to structure a tokens by using a method called [`Category/Type/Item`](https://amzn.github.io/style-dictionary/#/tokens?id=category-type-item) or (`CTI`), with built-in support for parsing.

<Callout className="[&_blockquote]:mt-0 my-4">
> Design tokens are organized into a hierarchical tree structure with 'category' defining the primitive nature of the design token (excerpted from official Style Dictionary website)

</Callout>

<Image src="/images/tokens-03.png" width={300} height={100} className="w-full bg-white rounded-lg"/>
<Image src="/images/tokens-04.png" width={300} height={100} className="w-full bg-white rounded-lg"/>

## Installation

<Steps>

<Step>pre-configuration:</Step>

to install `design-systems/tokens` package, you need to configure `.npmrc` (you have to create this file (at project's root) first) to point to custom registry

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1873/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1873/packages/npm/:_authToken=<AUTH_TOKEN>
```

<Callout className="mt-6 bg-surface-static-warning-default text-surface-action-delete-active">
please contact maintainers for `AUTH_TOKEN`
</Callout>

<Step>install package:</Step>

then you can use any package management to install the library:
```bash title=""
# with pnpm
pnpm add @design-systems/apollo-tokens
# with yarn
yarn add @design-systems/apollo-tokens
# with npm
npm install @design-systems/apollo-tokens
```
</Steps>

## Usages

currently, `@design-systems/apollo-tokens` supports the following formats
- `tokens.css`, a css variables (eg. `--apollo-colors-surface-action-primary-default: #006D2E;`)
- `tokens.js`, JavaScript variables (eg. `export const COLORS_SURFACE_ACTION_PRIMARY_DEFAULT = "#006D2E";`)
- `tokens.json`, a json format
- `tokenModule.js`, a commonjs format
- `tailwind/**.js/`, tailwind config format, current support (`colors`,`typography`)

just import and use

```jsx {1,4}
import colors from '@design-systems/apollo-tokens/build/tailwind/colors.tailwind.js'

// #006d2e
console.log(colors.surface.action.primary.default)
```
