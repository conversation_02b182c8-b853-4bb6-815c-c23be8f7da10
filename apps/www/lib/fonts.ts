// import { JetBrains_Mono as <PERSON>ontMono, Inter as <PERSON>ontS<PERSON> } from "next/font/google"
import { JetBrains_Mono as <PERSON>ontMono, IBM_Plex_Sans_Thai as <PERSON>ontT<PERSON> } from "next/font/google"
// import { GeistMono } from "geist/font/mono"
import { GeistSans } from "geist/font/sans"

// export const fontSans = FontSans({
//   subsets: ["latin"],
//   variable: "--font-sans",
// })
export const fontSans = GeistSans

export const fontMono = FontMono({
  subsets: ["latin"],
  variable: "--font-mono",
})

export const fontThai = FontThai({
  subsets: ["latin", "thai"],
  weight: ["400", "500", "500"],
  display: "swap",
  variable: "--font-ibm-plex-sans-thai",
})
