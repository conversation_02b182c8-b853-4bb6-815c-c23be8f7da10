import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: string | number): string {
  const date = new Date(input)
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL}${path}`
}

/**
 * Generator function that streams the response body from a fetch request.
 */
export async function* streamingFetch(
  input: RequestInfo | URL,
  init?: RequestInit
) {
  const response = await fetch(input, init)
  if (!response.body) {
    yield null
    return
  }
  const reader = response.body.getReader()
  const decoder = new TextDecoder("utf-8")

  for (;;) {
    const { done, value } = await reader.read()
    if (done) break

    try {
      yield decoder.decode(value)
    } catch (e: any) {
      console.warn(e.message)
    }
  }
}
