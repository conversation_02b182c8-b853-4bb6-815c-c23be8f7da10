{"inlineColors": {"light": {"background": "white", "foreground": "neutral-950", "card": "white", "card-foreground": "neutral-950", "popover": "white", "popover-foreground": "neutral-950", "primary": "neutral-900", "primary-foreground": "neutral-50", "secondary": "neutral-100", "secondary-foreground": "neutral-900", "muted": "neutral-100", "muted-foreground": "neutral-500", "accent": "neutral-100", "accent-foreground": "neutral-900", "destructive": "red-500", "destructive-foreground": "neutral-50", "border": "neutral-200", "input": "neutral-200", "ring": "neutral-950"}, "dark": {"background": "neutral-950", "foreground": "neutral-50", "card": "neutral-950", "card-foreground": "neutral-50", "popover": "neutral-950", "popover-foreground": "neutral-50", "primary": "neutral-50", "primary-foreground": "neutral-900", "secondary": "neutral-800", "secondary-foreground": "neutral-50", "muted": "neutral-800", "muted-foreground": "neutral-400", "accent": "neutral-800", "accent-foreground": "neutral-50", "destructive": "red-900", "destructive-foreground": "neutral-50", "border": "neutral-800", "input": "neutral-800", "ring": "neutral-300"}}, "cssVars": {"light": {"background": "0 0% 100%", "foreground": "0 0% 3.9%", "card": "0 0% 100%", "card-foreground": "0 0% 3.9%", "popover": "0 0% 100%", "popover-foreground": "0 0% 3.9%", "primary": "0 0% 9%", "primary-foreground": "0 0% 98%", "secondary": "0 0% 96.1%", "secondary-foreground": "0 0% 9%", "muted": "0 0% 96.1%", "muted-foreground": "0 0% 45.1%", "accent": "0 0% 96.1%", "accent-foreground": "0 0% 9%", "destructive": "0 84.2% 60.2%", "destructive-foreground": "0 0% 98%", "border": "0 0% 89.8%", "input": "0 0% 89.8%", "ring": "0 0% 3.9%"}, "dark": {"background": "0 0% 3.9%", "foreground": "0 0% 98%", "card": "0 0% 3.9%", "card-foreground": "0 0% 98%", "popover": "0 0% 3.9%", "popover-foreground": "0 0% 98%", "primary": "0 0% 98%", "primary-foreground": "0 0% 9%", "secondary": "0 0% 14.9%", "secondary-foreground": "0 0% 98%", "muted": "0 0% 14.9%", "muted-foreground": "0 0% 63.9%", "accent": "0 0% 14.9%", "accent-foreground": "0 0% 98%", "destructive": "0 62.8% 30.6%", "destructive-foreground": "0 0% 98%", "border": "0 0% 14.9%", "input": "0 0% 14.9%", "ring": "0 0% 83.1%"}}, "inlineColorsTemplate": "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n", "cssVarsTemplate": "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 0 0% 3.9%;\n\n    --card: 0 0% 100%;\n    --card-foreground: 0 0% 3.9%;\n\n    --popover: 0 0% 100%;\n    --popover-foreground: 0 0% 3.9%;\n\n    --primary: 0 0% 9%;\n    --primary-foreground: 0 0% 98%;\n\n    --secondary: 0 0% 96.1%;\n    --secondary-foreground: 0 0% 9%;\n\n    --muted: 0 0% 96.1%;\n    --muted-foreground: 0 0% 45.1%;\n\n    --accent: 0 0% 96.1%;\n    --accent-foreground: 0 0% 9%;\n\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 98%;\n\n    --border: 0 0% 89.8%;\n    --input: 0 0% 89.8%;\n    --ring: 0 0% 3.9%;\n\n    --radius: 0.5rem;\n  }\n\n  .dark {\n    --background: 0 0% 3.9%;\n    --foreground: 0 0% 98%;\n\n    --card: 0 0% 3.9%;\n    --card-foreground: 0 0% 98%;\n\n    --popover: 0 0% 3.9%;\n    --popover-foreground: 0 0% 98%;\n\n    --primary: 0 0% 98%;\n    --primary-foreground: 0 0% 9%;\n\n    --secondary: 0 0% 14.9%;\n    --secondary-foreground: 0 0% 98%;\n\n    --muted: 0 0% 14.9%;\n    --muted-foreground: 0 0% 63.9%;\n\n    --accent: 0 0% 14.9%;\n    --accent-foreground: 0 0% 98%;\n\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 0 0% 98%;\n\n    --border: 0 0% 14.9%;\n    --input: 0 0% 14.9%;\n    --ring: 0 0% 83.1%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n  body {\n    @apply bg-background text-foreground;\n  }\n}"}