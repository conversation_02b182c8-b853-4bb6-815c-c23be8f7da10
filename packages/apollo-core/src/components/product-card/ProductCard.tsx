import type { ElementType } from "react"

import { useSlotProps } from "../../hooks"
import type {
  ProductCardBodyContainerSlotProps,
  ProductCardContainerSlotProps,
  ProductCardFooterContainerSlotProps,
  ProductCardImageContainerSlotProps,
  ProductCardProps,
} from "./ProductCardProps"

const COMPONENT_NAME = "ProductCard"

export function ProductCard<TImageComponent extends ElementType = "img">({
  imageAlt,
  imageSrc,
  slots,
}: ProductCardProps<TImageComponent>) {
  const { Component: Container } = useSlotProps<ProductCardContainerSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "container",
    props: {},
    slotProps: slots.container,
    render: ({ className, children, ...props }) => (
      <article className={className} {...props}>
        {children}
      </article>
    ),
  })

  const { Component: Image } = useSlotProps({
    componentName: COMPONENT_NAME,
    slotKey: "image",
    props: { src: imageSrc, alt: imageAlt } as never,
    slotProps: slots.image,
    render: (props) => {
      return <img {...props} />
    },
  })

  const { Component: NoImage } = useSlotProps({
    componentName: COMPONENT_NAME,
    slotKey: "noImage",
    props: {} as never,
    slotProps: slots.noImage,
    render: (props) => {
      return <div {...props} />
    },
  })

  const { Component: ImageOverlay } = useSlotProps({
    componentName: COMPONENT_NAME,
    slotKey: "imageOverlay",
    props: {} as never,
    slotProps: slots.imageOverlay,
    render: (props) => <div {...props} />,
  })

  const { Component: ImageContainer } =
    useSlotProps<ProductCardImageContainerSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "imageContainer",
      props: {},
      slotProps: slots.imageContainer,
      render: ({ className, children, ...props }) => (
        <figure className={className} {...props}>
          {children}
        </figure>
      ),
    })

  const { Component: BodyContainer } =
    useSlotProps<ProductCardBodyContainerSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "bodyContainer",
      props: {},
      slotProps: slots.bodyContainer,
      render: ({ className, children, ...props }) => (
        <section className={className} {...props}>
          {children}
        </section>
      ),
    })

  const { Component: FooterContainer } =
    useSlotProps<ProductCardFooterContainerSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "footerContainer",
      props: {},
      slotProps: slots.footerContainer,
      render: ({ className, children, ...props }) => (
        <footer className={className} {...props}>
          {children}
        </footer>
      ),
    })

  return (
    <Container>
      <ImageContainer>
        {imageSrc ? <Image /> : <NoImage />}
        <ImageOverlay />
      </ImageContainer>
      <BodyContainer />
      <FooterContainer />
    </Container>
  )
}
