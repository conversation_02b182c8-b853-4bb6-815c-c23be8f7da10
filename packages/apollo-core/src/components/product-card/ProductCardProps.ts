import type {
  ComponentProps,
  ElementType,
  HTMLAttributes,
  ImgHTMLAttributes,
} from "react"

import type { ComponentSlotProps } from "../../types"

export type ProductCardContainerSlotProps = HTMLAttributes<HTMLDivElement>
export type ProductCardImageContainerSlotProps = HTMLAttributes<HTMLDivElement>
export type ProductCardBodyContainerSlotProps = HTMLAttributes<HTMLDivElement>
export type ProductCardFooterContainerSlotProps = HTMLAttributes<HTMLDivElement>
export type ProductCardImageOverlayProps = HTMLAttributes<HTMLDivElement>
export type ProductCardNoImageProps = HTMLAttributes<HTMLDivElement>

export type ProductCardSlotProps<
  TImageComponent extends ElementType = "img",
  TImageProps = TImageComponent extends "img"
    ? ImgHTMLAttributes<HTMLImageElement>
    : ComponentProps<TImageComponent>,
> = {
  container?: ComponentSlotProps<ProductCardContainerSlotProps>
  imageContainer?: ComponentSlotProps<ProductCardImageContainerSlotProps>
  image?: ComponentSlotProps<TImageProps>
  noImage?: ComponentSlotProps<ProductCardNoImageProps>
  imageOverlay?: ComponentSlotProps<ProductCardImageOverlayProps>
  bodyContainer?: ComponentSlotProps<ProductCardBodyContainerSlotProps>
  footerContainer?: ComponentSlotProps<ProductCardFooterContainerSlotProps>
}

export type ProductCardProps<
  TImageComponent extends ElementType = "img",
  TImageProps = TImageComponent extends "img"
    ? ImgHTMLAttributes<HTMLImageElement>
    : ComponentProps<TImageComponent>,
> = {
  imageSrc?: string
  imageAlt?: string
  slots: ProductCardSlotProps<TImageComponent, TImageProps>
}
