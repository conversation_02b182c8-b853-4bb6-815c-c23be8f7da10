import { Badge } from "@apollo/ui"
import { cva } from "class-variance-authority"
import classNames from "classnames"

import styles from "./product-badge.module.css"
import type { ProductBadgeProps } from "./ProductBadgeProps"

const badgeVariants = cva("ApolloStorefrontProductBadge", {
  variants: {
    variant: {
      moreItem: styles.moreItemProductBadge,
      discount: styles.discountProductBadge,
      monthly: styles.monthlyProductBadge,
      lowStock: styles.lowStockProductBadge,
    },
  },
  defaultVariants: {
    variant: undefined,
  },
})

export function ProductBadge(props: ProductBadgeProps) {
  return (
    <Badge
      {...props}
      className={classNames(
        badgeVariants({
          variant: props.variant,
        }),
        props?.className
      )}
    />
  )
}
