import { Typography } from "@apollo/ui"
import classNames from "classnames"

import { ProductPrice } from "../product-price/ProductPrice"
import styles from "./product-card-content.module.css"
import type { ProductCardContentProps } from "./ProductCardContentProps"

export function ProductCardContent({
  title,
  caption,
  price,
  titleMaxLines = 3,
  captionMaxLines = 2,
}: ProductCardContentProps) {
  return (
    <div className={styles.content}>
      <Typography
        className={classNames(
          styles.title,
          caption ? styles.titleWithCaption : styles.titleCompact
        )}
        level="bodyLarge"
        style={{
          WebkitLineClamp: caption ? titleMaxLines : 2,
        }}
      >
        {title}
      </Typography>
      {price && <ProductPrice {...price} />}
      {caption && (
        <Typography
          className={styles.contentCaption}
          level="labelSmall"
          style={{
            WebkitLineClamp: captionMaxLines,
          }}
        >
          {caption}
        </Typography>
      )}
    </div>
  )
}
