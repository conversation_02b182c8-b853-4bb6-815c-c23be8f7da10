.content {
    display: flex;
    flex-direction: column;
    gap: var(--apl-alias-spacing-padding-padding2, 2px);
    height: 100%;
}

.title {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.titleCompact {
    -webkit-line-clamp: 2;
    line-clamp: 2;
}

.titleWithCaption {
    -webkit-line-clamp: 2;
    line-clamp: 2;
}

.contentCaption {
    color: var(--apl-alias-color-error-error);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
