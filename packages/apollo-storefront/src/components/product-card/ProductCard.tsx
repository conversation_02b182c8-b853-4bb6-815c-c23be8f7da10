import { useMemo } from "react"
import type { CSSProperties, ElementType } from "react"
import {
  ProductCard as CoreProductCard,
  type ProductCardProps as CoreProductCardProps,
} from "@apollo/core"
import classNames from "classnames"

import { ProductCardContent } from "../product-card-content/ProductCardContent"
import styles from "./product-card.module.css"
import type { ProductCardProps } from "./ProductCardProps"

export function ProductCard<TImageComponent extends ElementType = "img">({
  imageSrc,
  imageAlt,
  title,
  caption,
  price,
  fill,
  slots: extendedSlots,
  titleMaxLines = 3,
  captionMaxLines = 2,
  ...props
}: ProductCardProps) {
  const slots = useMemo(
    () =>
      ({
        container: {
          className: classNames(styles.productCard, styles.productCardRoot),
          style: {
            ...(fill
              ? {}
              : {
                  ["--apl-storefront-productCard-maxWidth"]: "177px",
                }),
          } as CSSProperties,
        },
        imageContainer: {
          className: classNames(styles.productCardImageContainer),
        },
        image: {
          className: classNames(styles.productCardImage),
          src: imageSrc,
          render: props?.renderImage,
        },
        noImage: {
          className: classNames(styles.productCardImageFallback),
        },
        imageOverlay: {
          className: classNames(styles.productCardImageOverlay),
          render: props?.renderImageOverlay,
        },
        bodyContainer: {
          className: classNames(styles.productCardBodyContainer),
          render: props?.renderContent,
          children: props?.renderContent?.({
            title,
            caption,
            price,
            titleMaxLines: titleMaxLines,
            captionMaxLines: captionMaxLines,
          }) ?? (
            <ProductCardContent
              title={title}
              caption={caption}
              price={price}
              titleMaxLines={titleMaxLines}
              captionMaxLines={captionMaxLines}
            />
          ),
        },
        footerContainer: {
          className: classNames(styles.productCardFooterContainer),
          children: props?.renderFooter?.(),
        },
        ...extendedSlots,
      }) as CoreProductCardProps<TImageComponent>["slots"],
    [
      fill,
      imageSrc,
      props,
      title,
      caption,
      price,
      titleMaxLines,
      captionMaxLines,
      extendedSlots,
    ]
  )

  return (
    <CoreProductCard imageSrc={imageSrc} imageAlt={imageAlt} slots={slots} />
  )
}
