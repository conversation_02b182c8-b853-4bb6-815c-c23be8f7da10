import type { ReactElement } from "react"
import type { ProductCardProps as CoreProductCardProps } from "@apollo/core"

import type { ProductCardContentProps } from "../product-card-content"
import type { ProductPriceProps } from "../product-price"

export type ProductImageProps = {
  imageSrc?: string
  imageAlt?: string
}

export type ProductCardContentPropss = {
  title?: string
  caption?: string
  price?: ProductPriceProps
  titleMaxLines?: number
  captionMaxLines?: number
}

export type ProductCardProps = {
  fill?: boolean
  /**
   * Render Functions
   */
  renderImageOverlay?: () => ReactElement
  renderImage?: (props: ProductImageProps) => ReactElement
  renderContent?: (props: ProductCardContentProps) => ReactElement
  renderFooter?: () => ReactElement
} & ProductImageProps &
  ProductCardContentProps &
  Partial<Pick<CoreProductCardProps, "slots">>
