.productCard {
    /* Container */
    --apl-storefront-productCard-height: 349px;
    --apl-storefront-productCard-borderRadius: var(--apl-alias-radius-radius4, 8px);
    --apl-storefront-productCard-padding: var(--apl-alias-spacing-padding-padding5, 8px);
    --apl-storefront-productCard-boxShadow: 0 var(--apl-alias-elevation-elevations1-y-axis, 2px) var(--apl-alias-elevation-elevations1-blur, 4px) var(--apl-alias-spacing-padding-padding1, 0) var(--apl-alias-color-overlay-black-10, rgba(0, 0, 0, 0.10));
}

.productCardRoot {
    display: flex;
    width: 100%;
    max-width: var(--apl-storefront-productCard-maxWidth);
    height: var(--apl-storefront-productCard-height);
    padding: var(--apl-storefront-productCard-padding);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: var(--apl-alias-spacing-padding-padding3, 4px);
    border-radius: var(--apl-storefront-productCard-borderRadius);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);
    box-shadow: var(--apl-storefront-productCard-boxShadow);
}

.productCardImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.productCardBodyContainer {
    flex: 1;
    align-self: stretch;
}

.productCardFooterContainer {
    align-self: stretch;
}

.productCardImageOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

.productCardImageContainer {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    aspect-ratio: 1/1;
    border-radius: var(--apl-alias-radius-radius4, 8px);
    width: 100%;
    margin: 0;
    padding: 0;
}

.productCardImageFallback {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-color: var(--apl-alias-color-background-and-surface-on-background);
}