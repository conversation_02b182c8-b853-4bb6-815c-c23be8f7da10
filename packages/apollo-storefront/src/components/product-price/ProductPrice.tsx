import { Typography } from "@apollo/ui"

import styles from "./product-price.module.css"
import type { ProductPriceProps } from "./ProductPriceProps"

export function ProductPrice({
  price,
  unit,
  discountPrice,
  currency = "฿",
}: ProductPriceProps) {
  return (
    <div className={styles.priceContainer}>
      <span>
        <Typography className={styles.price} level="labelLarge">
          {price}
          {currency}
        </Typography>
        {unit && (
          <Typography className={styles.productUnit} level="labelLarge">
            /{unit}
          </Typography>
        )}
      </span>
      {discountPrice && (
        <Typography className={styles.discountPrice} level="labelSmall">
          {discountPrice}
          {currency}
        </Typography>
      )}
    </div>
  )
}
