import { Tabs } from "@apollo/ui"
import classNames from "classnames"

import styles from "./tab-with-image.module.css"
import type { TabProps } from "./TabWithImageProps"

export function Tab({ className, children, ...props }: TabProps) {
  return (
    <Tabs.Tab
      className={classNames("ApolloTabWithImage-tab", styles.tabWithImageItem, className)}
      {...props}
    >
      {children}
    </Tabs.Tab>
  )
}
