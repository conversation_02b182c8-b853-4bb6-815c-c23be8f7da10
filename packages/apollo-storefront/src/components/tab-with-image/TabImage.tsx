import classNames from "classnames"

import styles from "./tab-with-image.module.css"
import type { TabImageProps } from "./TabWithImageProps"

export function TabImage({ src, alt, children, className, ...props }: TabImageProps) {
  const renderContent = () => {
    if (src && alt) {
      return <img src={src} alt={alt} className={classNames("ApolloTabWithImage-image", styles.tabImage)} />
    }
    if (children) {
      return children
    }
    return null
  }

  return (
    <div className={classNames("ApolloTabWithImage-imageRoot",styles.tabImageRoot, className)} {...props}>
      {renderContent()}
    </div>
  )
}
