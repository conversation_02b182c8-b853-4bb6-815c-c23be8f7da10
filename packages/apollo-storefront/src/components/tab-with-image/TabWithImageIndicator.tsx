import { useLayoutEffect, useRef, useState } from "react"
import { Tabs } from "@apollo/ui"
import classNames from "classnames"

import styles from "./tab-with-image.module.css"
import type { TabWithImageIndicatorProps } from "./TabWithImageProps"


export function TabWithImageIndicator({ className, ...props }: TabWithImageIndicatorProps) {
    const indicatorRef = useRef<HTMLSpanElement>(null)
    const [indicatorStyle, setIndicatorStyle] = useState<React.CSSProperties>({})

    useLayoutEffect(() => {
        const updateIndicatorPosition = () => {
            const indicator = indicatorRef.current
            if (!indicator) return

            const tabsList = indicator.closest('[role="tablist"]') as HTMLElement
            const activeTab = tabsList?.querySelector('[aria-selected="true"]') as HTMLElement
            if (!activeTab || !tabsList) return

            const tabsListRect = tabsList.getBoundingClientRect()
            const activeTabRect = activeTab.getBoundingClientRect()

            // Calculate position relative to the tabs list container
            const left = activeTabRect.left - tabsListRect.left + tabsList.scrollLeft
            const width = activeTabRect.width

            setIndicatorStyle({
                left: `${left}px`,
                width: `${width}px`,
            })
        }

        // Update position on mount and when tabs change
        updateIndicatorPosition()

        // Listen for scroll events on the tabs list
        const tabsList = indicatorRef.current?.closest('[role="tablist"]') as HTMLElement
        if (tabsList) {
            tabsList.addEventListener('scroll', updateIndicatorPosition)

            // Also listen for tab changes
            const observer = new MutationObserver(updateIndicatorPosition)
            observer.observe(tabsList, {
                attributes: true,
                subtree: true,
                attributeFilter: ['aria-selected']
            })

            return () => {
                tabsList.removeEventListener('scroll', updateIndicatorPosition)
                observer.disconnect()
            }
        }
    }, [])
    

    return (
        <Tabs.Indicator
            ref={indicatorRef}
            className={classNames(
                "ApolloTabWithImage-indicator",
                styles.tabWithImageIndicator,
                className
            )}
            style={indicatorStyle}
            {...props}
        />
    )
}
