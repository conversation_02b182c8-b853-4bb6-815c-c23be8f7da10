
import { Tabs } from "@apollo/ui"
import classNames from "classnames"

import styles from "./tab-with-image.module.css"
import type { TabWithImageListProps } from "./TabWithImageProps"

export function TabWithImageList({ className, ...props }: TabWithImageListProps) {
  return (
    <Tabs.List
      className={classNames(
        "ApolloTabWithImage-list",
        styles.tabWithImageList,
        className
      )}
      {...props}
    />
  )
}
