import { Tabs } from "@apollo/ui"
import { ComponentProps, HTMLAttributes, PropsWithChildren } from "react"


export type TabWithImageRootProps = ComponentProps<typeof Tabs.Root>
export type TabWithImageListProps = ComponentProps<typeof Tabs.List>
export type TabProps = ComponentProps<typeof Tabs.Tab> 

export type TabImageProps = PropsWithChildren<{
  src?: string
  alt?: string
  ref?: React.Ref<HTMLDivElement>
}> & HTMLAttributes<HTMLDivElement>
export type TabTextProps = HTMLAttributes<HTMLDivElement>

export type TabWithImageIndicatorProps = ComponentProps<typeof Tabs.Indicator>
export type TabWithImagePanelProps = ComponentProps<typeof Tabs.Panel>