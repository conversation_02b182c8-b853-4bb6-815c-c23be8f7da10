
import { Tabs } from "@apollo/ui"
import classNames from "classnames"

import styles from "./tab-with-image.module.css"
import type { TabWithImageRootProps } from "./TabWithImageProps"

export function TabWithImageRoot({ className, ...props }: TabWithImageRootProps) {
  return (
    <Tabs.Root
      className={classNames(
        "ApolloTabWithImage-root",
        styles.tabWithImageRoot,
        className
      )}
      {...props}
    />
  )
}
