import { Tab } from "./Tab"
import { TabImage } from "./TabImage"
import { TabText } from "./TabText"
import { TabWithImageIndicator } from "./TabWithImageIndicator"
import { TabWithImageList } from "./TabWithImageList"
import { TabWithImagePanel } from "./TabWithImagePanel"
import { TabWithImageRoot } from "./TabWithImageRoot"

export const TabWithImage = {
  Root: TabWithImageRoot,
  List: TabWithImageList,
  Tab: Tab,
  Image: TabImage,
  Text: TabText,
  Indicator: TabWithImageIndicator,
  Panel: TabWithImagePanel,
}

export {
  TabWithImageRoot,
  TabWithImageList,
  Tab,
  TabImage,
  TabText,
  TabWithImageIndicator,
  TabWithImagePanel,
}
export type {
  TabWithImageRootProps,
  TabWithImageListProps,
  TabProps,
  TabImageProps,
  TabTextProps,
  TabWithImageIndicatorProps,
  TabWithImagePanelProps,
} from "./TabWithImageProps"
