# Apollo Token

Apollo Token is a design token library that provides a set of consistent and reusable design tokens for building user interfaces. It includes tokens for colors, typography, spacing, and other design elements that can be used across different platforms and devices.

## Generate Design Tokens (Apollo M3)

To generate design tokens, you need to run the following command:

```bash
npm run token:all
```

After running the command, the design tokens will be generated in the `tokens` folder.

Run the following command update new token:

```bash
pnpm build
```
