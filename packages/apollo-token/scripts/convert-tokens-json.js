#!/usr/bin/env node

/**
 * Convert Figma Variables to Design Tokens
 *
 * This script processes design tokens from the figma/variables folder
 * and converts them to Design Token format with proper organization.
 *
 * Usage: node convert-tokens-json.js [project-name]
 * Example: node convert-tokens-json.js apollo
 *
 * Input: figma/*.json files
 * Output: build/tokens/ folder structure with W3C format
 */

const fs = require("fs")
const path = require("path")

// Show help if requested
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log(`
Usage: node convert-tokens-json.js [project-name]

Arguments:
  project-name    Name of the project to process (default: apollo)

Examples:
  node convert-tokens-json.js          # Process 'apollo' project
  node convert-tokens-json.js apollo   # Process 'apollo' project
  node convert-tokens-json.js myapp    # Process 'myapp' project

The script will look for JSON files in: figma/[project-name]/
And output to: src/tokens/json/[project-name]/
`)
  process.exit(0)
}

// Configuration
const SELECTED_PROJECT = process.argv[2] || "apollo" // Get project from CLI args or default to "apollo"
const GLOBAL_FILE_NAME = "global"
const INPUT_DIR = path.join(__dirname, `../figma/${SELECTED_PROJECT}`)
const OUTPUT_DIR = path.join(
  __dirname,
  `../src/tokens/json/${SELECTED_PROJECT}`
)

function getCollectionType(collectionName, fileName) {
  const name = collectionName.toLowerCase().trim()

  if (fileName.includes(GLOBAL_FILE_NAME)) {
    return ""
  }
  // For other files, use normal logic
  if (name.includes("alias")) return "alias"

  return "base"
}

/**
 * Sanitize token names for use in paths and references
 * @param {string} name - Original token name
 * @returns {string} Sanitized name
 */
function sanitizeTokenName(name) {
  let sanitizedName = name.toLowerCase()
  if (sanitizedName.startsWith("-")) {
    sanitizedName = `reverse-${sanitizedName.substring(1)}`
  }
  return sanitizedName
    .replace(/[^a-z0-9\-\/]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "")
}

/**
 * Get token type from collection name
 * @param {string} collectionName - Original collection name
 * @returns {string} Token type
 */
function getTokenTypeFromCollection(collectionName) {
  const name = collectionName.toLowerCase().trim()

  if (name.includes("color")) return "color"
  if (name.includes("spacing")) return "spacing"
  if (name.includes("typography")) return "typography"
  if (name.includes("radius")) return "radius"
  if (name.includes("elevation")) return "elevation"

  // Default fallback
  return sanitizeTokenName(collectionName)
}

/**
 * Process token name into nested structure
 * @param {string} name - Token name (e.g., "green-pine/40" or "Alias Color/Schemes/primary/primary")
 * @returns {array} Path segments
 */
function processTokenName(name) {
  // Remove common prefixes and clean up the name
  let cleanName = name
    .replace(/^Alias Color\//, "")
    .replace(/^Base Color\//, "")
    .replace(/^Schemes\//, "")
    .replace(/^Extended\//, "")

  // Split by / and sanitize each segment
  return cleanName.split("/").map((segment) => sanitizeTokenName(segment))
}

/**
 * Create reference path from Figma token name
 * @param {string} figmaName - Original Figma token name
 * @returns {string} Dot-separated path for references
 */
function createReferencePath(figmaName) {
  let sanitizedName = figmaName.replace(/\//g, ".").toLowerCase()
  if (sanitizedName.startsWith("-")) {
    sanitizedName = `reverse-${sanitizedName.substring(1)}`
  }
  return sanitizedName
    .replace(/\//g, ".")
    .toLowerCase()
    .replace(/[^a-z0-9\.\-]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "")
}

/**
 * Process alias token reference
 * @param {object} variable - Variable with alias reference
 * @param {string} collectionName - Current collection name
 * @param {string} tokenType - Token type (color, spacing, etc.)
 * @returns {string} W3C reference format
 */
function processAlias(variable, collectionName, tokenType) {
  if (!variable.value || typeof variable.value !== "object") {
    return variable.value
  }

  let reference

  // Reference without collection (assume same collection)
  const referencedPath = createReferencePath(variable.value.name)
  const currentCollectionOriginal = collectionName.toLowerCase()
  const isCurrentAlias = currentCollectionOriginal.includes("alias")

  // Determine prefix based on current context and referenced token
  let prefix
  if (isCurrentAlias) {
    // Alias tokens should reference base tokens
    prefix = "base"
  }
  if (variable?.value?.collection) {
    prefix = getCollectionType(variable.value.collection, "")
    tokenType = getTokenTypeFromCollection(variable.value.collection)
  }

  reference = prefix
    ? `{${prefix}.${tokenType}.${referencedPath}}`
    : `{${tokenType}.${referencedPath}}`

  return reference
}

function convertFontWeight(weight) {
  const weightMap = {
    Thin: 100,
    "Thin It": 100,
    "Extra Light": 200,
    Light: 300,
    Regular: 400,
    Medium: 500,
    "Medium It": 500,
    "Semi Bold": 600,
    Bold: 700,
    "Bold It": 700,
    Black: 900,
    "Black It": 900,
  }

  return weightMap[weight] || weight
}

/**
 * Convert Figma token to W3C format
 * @param {object} variable - Figma variable
 * @param {string} collectionName - Collection name
 * @param {string} tokenType - Token type
 * @returns {object} W3C token
 */
function convertToW3C(variable, collectionName, tokenType) {
  const token = {
    $type: variable.type === "number" ? "dimension" : variable.type,
  }

  if (variable.isAlias) {
    token.$value = processAlias(variable, collectionName, tokenType)
  } else {
    token.$value = variable.value

    if (token.$type === "string") {
      if (variable.name.includes("font-family")) {
        token.$type = "fontFamily"
      } else if (variable.name.includes("font-weight")) {
        token.$type = "fontWeight"
        token.$value = convertFontWeight(token.$value)
      }
    }
    // Add units for dimension tokens
    if (token.$type === "dimension") {
      token.$value = `${token.$value}px`
    }
  }

  return token
}

console.log("🚀 Starting Figma Variables to W3C Design Tokens conversion...")
console.log(`🎯 Project: ${SELECTED_PROJECT}`)
console.log(`📁 Input directory: ${INPUT_DIR}`)
console.log(`📁 Output directory: ${OUTPUT_DIR}`)

// Check if input directory exists
if (!fs.existsSync(INPUT_DIR)) {
  console.error(`❌ Input directory does not exist: ${INPUT_DIR}`)
  console.error(
    `💡 Make sure you have a figma/${SELECTED_PROJECT} folder with JSON files`
  )
  process.exit(1)
}

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true })
}

// Process each JSON file in the variables directory
const files = fs.readdirSync(INPUT_DIR).filter((file) => file.endsWith(".json"))

console.log(`📄 Found ${files.length} files to process`)

files.forEach((file) => {
  console.log(`📖 Processing ${file}...`)

  const filePath = path.join(INPUT_DIR, file)
  const figmaData = JSON.parse(fs.readFileSync(filePath, "utf8"))

  // Create a master token structure for this file
  const fileTokens = {}

  // Process each collection in the file
  figmaData.collections.forEach((collection) => {
    const collectionType = getCollectionType(collection.name, file)
    const tokenType = getTokenTypeFromCollection(collection.name)

    console.log(
      `   📦 Processing collection: ${collection.name} (${collectionType})`
    )

    const description = collectionType
      ? `${collectionType.charAt(0).toUpperCase() + collectionType.slice(1)} tokens from ${collection.name} collection`
      : `${collection.name} collection`
    if (collectionType) {
      fileTokens[collectionType] ??= {}
      fileTokens[collectionType][tokenType] ??= {
        $description: description,
      }
    } else {
      fileTokens[tokenType] ??= { $description: description }
    }

    // Process each mode in the collection
    collection.modes
      .filter((mode) => mode.name !== "Style")
      .forEach((mode, modeIndex) => {
        // For color tokens, process all modes
        const isColorToken = tokenType === "color"
        //   const isFirstMode = modeIndex === 0

        if (!isColorToken && modeIndex !== 0) {
          return // Skip non-first modes for non-color tokens
        }

        // Always use the main fileTokens structure
        let currentTokens = fileTokens

        // Process each variable in the mode
        mode.variables.forEach((variable) => {
          const pathSegments = processTokenName(variable.name)
          const w3cToken = convertToW3C(variable, collection.name, tokenType)

          // Start from the token type section
          let current = collectionType
            ? currentTokens[collectionType][tokenType]
            : currentTokens[tokenType]

          // For color tokens with multiple modes
          if (isColorToken && collection.modes.length > 1) {
            const modeName = sanitizeTokenName(mode.name).replace("-mode", "") // Remove 'mode' word
            if (!current[modeName]) {
              current[modeName] = {}
            }
            current = current[modeName]
          }

          // Create nested structure for the rest of the path
          for (let i = 0; i < pathSegments.length - 1; i++) {
            if (!current[pathSegments[i]]) {
              current[pathSegments[i]] = {}
            }
            current = current[pathSegments[i]]
          }

          // Set the final token
          current[pathSegments[pathSegments.length - 1]] = w3cToken
        })
      })
  })

  // Write one comprehensive file for this input file
  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true })
  }

  // Combine all token types into one file
  const filename = `${path.parse(file).name}.json`

  // Write the combined file
  const outputFile = path.join(OUTPUT_DIR, filename)
  fs.writeFileSync(outputFile, JSON.stringify(fileTokens, null, 2))

  console.log(`   ✅ Generated ${filename}`)

  console.log(`✅ Successfully processed ${file}`)
})

console.log("🎉 Conversion completed successfully!")
