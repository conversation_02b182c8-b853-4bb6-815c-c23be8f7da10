#!/usr/bin/env node

/**
 * Convert JSON tokens to TypeScript format
 *
 * Usage: node convert-tokens-ts.js [project-name]
 * Example: node convert-tokens-ts.js apollo
 */

const fs = require("fs")
const path = require("path")

// Show help if requested
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log(`
Usage: node convert-tokens-ts.js [project-name]

Arguments:
  project-name    Name of the project to process (default: apollo)

Examples:
  node convert-tokens-ts.js          # Process 'apollo' project  
  node convert-tokens-ts.js apollo   # Process 'apollo' project
  node convert-tokens-ts.js myapp    # Process 'myapp' project

The script will look for JSON tokens in: src/tokens/json/[project-name]/
And output to: src/tokens/ts/[project-name].ts
`)
  process.exit(0)
}

// Configuration
const SELECTED_PROJECT = process.argv[2] || "apollo" // Get project from CLI args or default to "apollo"
const TOKENS_DIR = path.join(
  __dirname,
  `../src/tokens/json/${SELECTED_PROJECT}`
)
const OUTPUT_FILE = path.join(
  __dirname,
  `../src/tokens/ts/${SELECTED_PROJECT}.ts`
)

/**
 * Recursively merge objects, with later objects taking precedence
 */
function mergeDeep(target, ...sources) {
  if (!sources.length) return target
  const source = sources.shift()

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        mergeDeep(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return mergeDeep(target, ...sources)
}

function isObject(item) {
  return item && typeof item === "object" && !Array.isArray(item)
}

/**
 * Transform token objects to direct values
 * Converts { "$type": "color", "$value": "#000000" } to "#000000"
 */
function transformTokensToValues(obj) {
  if (typeof obj !== "object" || obj === null) {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(transformTokensToValues)
  }

  // If this is a token object with $value, return the value directly
  if (obj.$value !== undefined) {
    return obj.$value
  }

  // Otherwise, recursively transform the object
  const transformed = {}
  for (const [key, value] of Object.entries(obj)) {
    // Skip $type and $description properties
    if (key.startsWith("$") && key !== "$value") {
      continue
    }
    transformed[key] = transformTokensToValues(value)
  }

  return transformed
}

/**
 * Read and parse all JSON token files
 */
function readTokenFiles() {
  const tokenFiles = {}

  try {
    // Check if tokens directory exists
    if (!fs.existsSync(TOKENS_DIR)) {
      console.error(`❌ Tokens directory does not exist: ${TOKENS_DIR}`)
      console.error(
        `💡 Run 'npm run tokens:json ${SELECTED_PROJECT}' first to generate JSON tokens`
      )
      return {}
    }

    const files = fs.readdirSync(TOKENS_DIR)
    const jsonFiles = files.filter((file) => file.endsWith(".json"))

    console.log(`📖 Found ${jsonFiles.length} token files:`)

    for (const file of jsonFiles) {
      const filePath = path.join(TOKENS_DIR, file)
      const fileName = path.basename(file, ".json")

      try {
        const content = fs.readFileSync(filePath, "utf8")
        const tokens = JSON.parse(content)
        tokenFiles[fileName] = tokens
        console.log(`   ✅ Loaded ${file}`)
      } catch (error) {
        console.error(`   ❌ Error reading ${file}:`, error.message)
      }
    }

    return tokenFiles
  } catch (error) {
    console.error("❌ Error reading tokens directory:", error.message)
    return {}
  }
}

/**
 * Generate TypeScript module content
 */
function generateTypeScriptModule(tokenFiles) {
  const timestamp = new Date().toISOString()

  let tsContent = `/**
 * Apollo Design Tokens
 * Generated on: ${timestamp}
 *
 * This file contains all design tokens merged from:
 * ${Object.keys(tokenFiles)
   .map((name) => `- ${name}.json`)
   .join("\n * ")}
 */

`

  // Create merged tokens object (merge all token files into single tokens object)
  const mergedTokens = {}
  for (const tokens of Object.values(tokenFiles)) {
    mergeDeep(mergedTokens, tokens)
  }

  // Transform tokens to direct values
  const transformedTokens = transformTokensToValues(mergedTokens)

  tsContent += `// All design tokens merged into single object with direct values\n`
  tsContent += `export const tokens = ${JSON.stringify(transformedTokens, null, 2)} as const;\n\n`

  // Add type definitions
  tsContent += `// Type definitions\n`
  tsContent += `export type Tokens = typeof tokens;\n\n`

  // Default export
  tsContent += `// Default export\n`
  tsContent += `export default tokens;\n`

  return tsContent
}

/**
 * Main function
 */
function main() {
  console.log("🚀 Starting token merge process...")
  console.log(`🎯 Project: ${SELECTED_PROJECT}\n`)

  // Read all token files
  const tokenFiles = readTokenFiles()

  if (Object.keys(tokenFiles).length === 0) {
    console.error("❌ No token files found to merge")
    process.exit(1)
  }

  console.log(
    `\n📦 Processing ${Object.keys(tokenFiles).length} token files...`
  )

  // Generate TypeScript content
  const tsContent = generateTypeScriptModule(tokenFiles)

  // Write output file
  try {
    console.log(OUTPUT_FILE)

    // Ensure output directory exists
    const outputDir = path.dirname(OUTPUT_FILE)
    fs.mkdirSync(outputDir, { recursive: true })

    fs.writeFileSync(OUTPUT_FILE, tsContent, "utf8")
    console.log(`✅ Successfully generated: ${OUTPUT_FILE}`)

    // Show file size
    const stats = fs.statSync(OUTPUT_FILE)
    const fileSizeKB = (stats.size / 1024).toFixed(2)
    console.log(`📊 File size: ${fileSizeKB} KB`)
  } catch (error) {
    console.error("❌ Error writing output file:", error.message)
    process.exit(1)
  }

  console.log("\n🎉 TypeScript token file generated successfully!")
  console.log("\n📋 Available exports:")
  console.log("   - tokens (all design tokens with direct values)")
  console.log("   - Tokens (TypeScript type definition)")
  console.log("   - default export (same as tokens)")
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  readTokenFiles,
  generateTypeScriptModule,
  mergeDeep,
  transformTokensToValues,
}
