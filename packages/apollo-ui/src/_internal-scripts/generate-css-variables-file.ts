import { copyFileSync, mkdirSync, readdirSync, writeFileSync } from "fs"
import { dirname, join } from "path"
import { fileURLToPath } from "url"
import { ApolloStorefrontToken, ApolloToken } from "@apollo/token"

import { parseTokenV2 } from "../theme/utils"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

function generateTokenVariableFile(token: object, alternateName?: string) {
  const tokens = parseTokenV2(token as never)

  // Define the output path relative to the package root
  const packageRoot = join(__dirname, "../..")
  const outputPath = join(
    packageRoot,
    "src",
    alternateName ? `_variables-${alternateName}.css` : `_variables.css`
  )

  // Ensure the dist directory exists
  mkdirSync(dirname(outputPath), { recursive: true })

  // Write the CSS variables to the file with a header comment
  const cssContent = `/* This file is auto-generated. Do not edit manually. 
 * At ${new Date().toISOString()} 
*/
  \n:root {\n${tokens}\n}`
  writeFileSync(outputPath, cssContent, "utf8")

  console.log(`CSS variables file generated at: ${outputPath}`)
}

function copyVariablesFilesToDist() {
  const packageRoot = join(__dirname, "../..")
  const srcDir = join(packageRoot, "src")
  const distDir = join(packageRoot, "dist")

  // Ensure the dist directory exists
  mkdirSync(distDir, { recursive: true })

  // Find all _variables*.css files in src directory
  const files = readdirSync(srcDir).filter(
    (file) => file.startsWith("_variables") && file.endsWith(".css")
  )

  // Copy each file to dist
  files.forEach((file) => {
    const srcPath = join(srcDir, file)
    const distPath = join(distDir, file)
    copyFileSync(srcPath, distPath)
    console.log(`Copied ${file} to dist directory`)
  })

  if (files.length === 0) {
    console.log("No _variables*.css files found to copy")
  }
}

// Execute the function if this script is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTokenVariableFile(ApolloToken)
  generateTokenVariableFile(ApolloStorefrontToken, "storefront")
  copyVariablesFilesToDist()
}
