/* This file is auto-generated. Do not edit manually. 
 * At 2025-09-12T03:13:09.888Z 
*/
  
:root {
  --apl-alias-color-background-and-surface-background: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-0));
  --apl-alias-color-background-and-surface-container-disabled: light-dark(var(--apl-base-color-neutral-95), var(--apl-base-color-neutral-10));
  --apl-alias-color-background-and-surface-on-background: light-dark(var(--apl-base-color-neutral-99), var(--apl-base-color-neutral-20));
  --apl-alias-color-background-and-surface-on-surface-variant: light-dark(var(--apl-base-color-neutral-80), var(--apl-base-color-neutral-20));
  --apl-alias-color-background-and-surface-on-surface: light-dark(var(--apl-base-color-neutral-30), var(--apl-base-color-neutral-99));
  --apl-alias-color-background-and-surface-surface-variant: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-70));
  --apl-alias-color-background-and-surface-surface: light-dark(var(--apl-base-color-neutral-99), var(--apl-base-color-neutral-10));
  --apl-alias-color-background-and-surface-text-icon-disabled: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-20));
  --apl-alias-color-effects-overlay-surface-black: light-dark(var(--apl-base-color-overlay-black-20), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-effects-overlay-surface-disabled: light-dark(var(--apl-base-color-overlay-white-70), var(--apl-base-color-overlay-black-70));
  --apl-alias-color-effects-overlay-surface-white: light-dark(var(--apl-base-color-overlay-white-20), var(--apl-base-color-overlay-white-20));
  --apl-alias-color-effects-scrim: light-dark(var(--apl-base-color-overlay-black-40), var(--apl-base-color-overlay-black-60));
  --apl-alias-color-effects-shadow: light-dark(var(--apl-base-color-overlay-black-40), var(--apl-base-color-overlay-black-60));
  --apl-alias-color-error-container-disabled: light-dark(var(--apl-base-color-danger-99), var(--apl-base-color-danger-10));
  --apl-alias-color-error-disable: light-dark(var(--apl-base-color-danger-90), var(--apl-base-color-danger-10));
  --apl-alias-color-error-error-container: light-dark(var(--apl-base-color-danger-90), var(--apl-base-color-danger-70));
  --apl-alias-color-error-error: light-dark(var(--apl-base-color-danger-40), var(--apl-base-color-danger-90));
  --apl-alias-color-error-focused: light-dark(var(--apl-base-color-danger-60), var(--apl-base-color-danger-90));
  --apl-alias-color-error-hovered: light-dark(var(--apl-base-color-danger-50), var(--apl-base-color-danger-70));
  --apl-alias-color-error-on-error-container: light-dark(var(--apl-base-color-danger-30), var(--apl-base-color-danger-95));
  --apl-alias-color-error-on-error: light-dark(var(--apl-base-color-danger-100), var(--apl-base-color-danger-20));
  --apl-alias-color-error-pressed: light-dark(var(--apl-base-color-danger-30), var(--apl-base-color-danger-30));
  --apl-alias-color-error-text-icon-disabled: light-dark(var(--apl-base-color-danger-40), var(--apl-base-color-danger-30));
  --apl-alias-color-error-text-only-background-hovered: light-dark(var(--apl-base-color-neutral-90), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-other-badge-discount: light-dark(var(--apl-base-color-danger-50), var(--apl-base-color-danger-50));
  --apl-alias-color-other-badge-low-stock: light-dark(var(--apl-base-color-danger-90), var(--apl-base-color-danger-90));
  --apl-alias-color-other-badge-monthly: light-dark(var(--apl-base-color-warning-60), var(--apl-base-color-warning-60));
  --apl-alias-color-other-badge-more-item: light-dark(var(--apl-base-color-danger-60), var(--apl-base-color-danger-60));
  --apl-alias-color-outline-and-border-border-disabled: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-30));
  --apl-alias-color-outline-and-border-border: light-dark(var(--apl-base-color-neutral-30), var(--apl-base-color-neutral-95));
  --apl-alias-color-outline-and-border-outline-variant: light-dark(var(--apl-base-color-neutral-80), var(--apl-base-color-neutral-80));
  --apl-alias-color-outline-and-border-outline: light-dark(var(--apl-base-color-neutral-70), var(--apl-base-color-neutral-30));
  --apl-alias-color-primary-focused: light-dark(var(--apl-base-color-primary-60), var(--apl-base-color-primary-90));
  --apl-alias-color-primary-hovered: light-dark(var(--apl-base-color-primary-50), var(--apl-base-color-primary-70));
  --apl-alias-color-primary-on-primary: light-dark(var(--apl-base-color-primary-100), var(--apl-base-color-primary-20));
  --apl-alias-color-primary-pressed: light-dark(var(--apl-base-color-primary-30), var(--apl-base-color-primary-30));
  --apl-alias-color-primary-primary-container: light-dark(var(--apl-base-color-primary-95), var(--apl-base-color-primary-30));
  --apl-alias-color-primary-primary: light-dark(var(--apl-base-color-primary-40), var(--apl-base-color-primary-50));
  --apl-alias-color-primary-surface-tint: light-dark(var(--apl-base-color-primary-40), var(--apl-base-color-primary-70));
  --apl-alias-color-primary-text-only-background-hovered: light-dark(var(--apl-base-color-neutral-90), var(--apl-base-color-overlay-black-20));
  --apl-alias-color-secondary-on-secondary-container: light-dark(var(--apl-base-color-secondary-30), var(--apl-base-color-secondary-95));
  --apl-alias-color-secondary-on-secondary: light-dark(var(--apl-base-color-secondary-100), var(--apl-base-color-secondary-20));
  --apl-alias-color-secondary-secondary-container: light-dark(var(--apl-base-color-secondary-95), var(--apl-base-color-secondary-30));
  --apl-alias-color-secondary-seconday: light-dark(var(--apl-base-color-secondary-40), var(--apl-base-color-secondary-70));
  --apl-alias-color-static-text-icon-text-icon-dark: light-dark(var(--apl-base-color-neutral-10), var(--apl-base-color-neutral-10));
  --apl-alias-color-static-text-icon-text-icon-light: light-dark(var(--apl-base-color-neutral-100), var(--apl-base-color-neutral-100));
  --apl-alias-color-success-on-success-container: light-dark(var(--apl-base-color-success-30), var(--apl-base-color-success-95));
  --apl-alias-color-success-on-success: light-dark(var(--apl-base-color-success-100), var(--apl-base-color-success-20));
  --apl-alias-color-success-success-container: light-dark(var(--apl-base-color-success-99), var(--apl-base-color-success-30));
  --apl-alias-color-success-success: light-dark(var(--apl-base-color-success-50), var(--apl-base-color-primary-90));
  --apl-alias-color-tertiary-on-tertiary-container: light-dark(var(--apl-base-color-tertiary-30), var(--apl-base-color-tertiary-95));
  --apl-alias-color-tertiary-on-tertiary: light-dark(var(--apl-base-color-tertiary-100), var(--apl-base-color-tertiary-20));
  --apl-alias-color-tertiary-tertiary-container: light-dark(var(--apl-base-color-tertiary-95), var(--apl-base-color-tertiary-30));
  --apl-alias-color-tertiary-tertiary: light-dark(var(--apl-base-color-tertiary-40), var(--apl-base-color-tertiary-70));
  --apl-alias-color-warning-on-warning-container: light-dark(var(--apl-base-color-warning-30), var(--apl-base-color-warning-99));
  --apl-alias-color-warning-on-warning: light-dark(var(--apl-base-color-warning-100), var(--apl-base-color-warning-20));
  --apl-alias-color-warning-warning-container: light-dark(var(--apl-base-color-warning-99), var(--apl-base-color-warning-30));
  --apl-alias-color-warning-warning: light-dark(var(--apl-base-color-warning-50), var(--apl-base-color-warning-90));
  --apl-alias-elevation-elevations1-blur: var(--apl-base-elevation-blur-4);
  --apl-alias-elevation-elevations1-color: var(--apl-base-color-overlay-black-10);
  --apl-alias-elevation-elevations1-spread: var(--apl-base-spacing-space1);
  --apl-alias-elevation-elevations1-x-axis: var(--apl-base-elevation-x-axis-none);
  --apl-alias-elevation-elevations1-y-axis: var(--apl-base-elevation-y-axis-2);
  --apl-alias-elevation-elevations2-blur: 0px;
  --apl-alias-elevation-elevations2-color: 0px;
  --apl-alias-elevation-elevations2-spread: 0px;
  --apl-alias-elevation-elevations2-x-axis: 0px;
  --apl-alias-elevation-elevations2-y-axis: 0px;
  --apl-alias-elevation-elevations3-blur: 0px;
  --apl-alias-elevation-elevations3-color: 0px;
  --apl-alias-elevation-elevations3-spread: 0px;
  --apl-alias-elevation-elevations3-x-axis: 0px;
  --apl-alias-elevation-elevations3-y-axis: 0px;
  --apl-alias-radius-radius1: var(--apl-base-radius-none);
  --apl-alias-radius-radius10: var(--apl-base-radius-24);
  --apl-alias-radius-radius11: var(--apl-base-radius-full);
  --apl-alias-radius-radius2: var(--apl-base-radius-4);
  --apl-alias-radius-radius3: var(--apl-base-radius-6);
  --apl-alias-radius-radius4: var(--apl-base-radius-8);
  --apl-alias-radius-radius5: var(--apl-base-radius-10);
  --apl-alias-radius-radius6: var(--apl-base-radius-12);
  --apl-alias-radius-radius7: var(--apl-base-radius-14);
  --apl-alias-radius-radius8: var(--apl-base-radius-16);
  --apl-alias-radius-radius9: var(--apl-base-radius-20);
  --apl-alias-spacing-gap-gap1: var(--apl-base-spacing-space1);
  --apl-alias-spacing-gap-gap10: var(--apl-base-spacing-space10);
  --apl-alias-spacing-gap-gap11: var(--apl-base-spacing-space11);
  --apl-alias-spacing-gap-gap12: var(--apl-base-spacing-space12);
  --apl-alias-spacing-gap-gap2: var(--apl-base-spacing-space2);
  --apl-alias-spacing-gap-gap3: var(--apl-base-spacing-space3);
  --apl-alias-spacing-gap-gap4: var(--apl-base-spacing-space4);
  --apl-alias-spacing-gap-gap5: var(--apl-base-spacing-space5);
  --apl-alias-spacing-gap-gap6: var(--apl-base-spacing-space6);
  --apl-alias-spacing-gap-gap7: var(--apl-base-spacing-space7);
  --apl-alias-spacing-gap-gap8: var(--apl-base-spacing-space8);
  --apl-alias-spacing-gap-gap9: var(--apl-base-spacing-space9);
  --apl-alias-spacing-margin-horizontal-horizontal: var(--apl-base-spacing-space15);
  --apl-alias-spacing-margin-vertical-vertical: var(--apl-base-spacing-space15);
  --apl-alias-spacing-padding-padding1: var(--apl-base-spacing-space1);
  --apl-alias-spacing-padding-padding10: var(--apl-base-spacing-space10);
  --apl-alias-spacing-padding-padding11: var(--apl-base-spacing-space11);
  --apl-alias-spacing-padding-padding12: var(--apl-base-spacing-space12);
  --apl-alias-spacing-padding-padding2: var(--apl-base-spacing-space2);
  --apl-alias-spacing-padding-padding3: var(--apl-base-spacing-space3);
  --apl-alias-spacing-padding-padding4: var(--apl-base-spacing-space4);
  --apl-alias-spacing-padding-padding5: var(--apl-base-spacing-space5);
  --apl-alias-spacing-padding-padding6: var(--apl-base-spacing-space6);
  --apl-alias-spacing-padding-padding7: var(--apl-base-spacing-space7);
  --apl-alias-spacing-padding-padding8: var(--apl-base-spacing-space8);
  --apl-alias-spacing-padding-padding9: var(--apl-base-spacing-space9);
  --apl-alias-typography-body-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-large-font-size: var(--apl-base-typography-font-size-xs);
  --apl-alias-typography-body-large-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-large-line-height: var(--apl-base-typography-line-height-line-height13);
  --apl-alias-typography-body-large-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-body-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-medium-font-size: var(--apl-base-typography-font-size-2xs);
  --apl-alias-typography-body-medium-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-medium-line-height: var(--apl-base-typography-line-height-line-height13);
  --apl-alias-typography-body-medium-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-body-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-small-font-size: var(--apl-base-typography-font-size-3xs);
  --apl-alias-typography-body-small-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-small-line-height: var(--apl-base-typography-line-height-line-height10);
  --apl-alias-typography-body-small-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-display-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-large-font-size: var(--apl-base-typography-font-size-3xl);
  --apl-alias-typography-display-large-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-large-line-height: var(--apl-base-typography-line-height-line-height49);
  --apl-alias-typography-display-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-medium-font-size: var(--apl-base-typography-font-size-2xl);
  --apl-alias-typography-display-medium-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-medium-line-height: var(--apl-base-typography-line-height-line-height49);
  --apl-alias-typography-display-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-small-font-size: var(--apl-base-typography-font-size-xl);
  --apl-alias-typography-display-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-small-line-height: var(--apl-base-typography-line-height-line-height31);
  --apl-alias-typography-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-large-font-size: var(--apl-base-typography-font-size-lg);
  --apl-alias-typography-headline-large-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-large-line-height: var(--apl-base-typography-line-height-line-height29);
  --apl-alias-typography-headline-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-medium-font-size: var(--apl-base-typography-font-size-md);
  --apl-alias-typography-headline-medium-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-medium-line-height: var(--apl-base-typography-line-height-line-height44);
  --apl-alias-typography-headline-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-small-font-size: var(--apl-base-typography-font-size-sm);
  --apl-alias-typography-headline-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-small-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-label-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-large-font-size: var(--apl-base-typography-font-size-2xs);
  --apl-alias-typography-label-large-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-large-line-height: var(--apl-base-typography-line-height-line-height10);
  --apl-alias-typography-label-large-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-label-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-medium-font-size: var(--apl-base-typography-font-size-3xs);
  --apl-alias-typography-label-medium-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-medium-line-height: var(--apl-base-typography-line-height-line-height10);
  --apl-alias-typography-label-medium-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-label-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-small-font-size: var(--apl-base-typography-font-size-4xs);
  --apl-alias-typography-label-small-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-small-line-height: var(--apl-base-typography-line-height-line-height13);
  --apl-alias-typography-label-small-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-title-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-large-font-size: var(--apl-base-typography-font-size-xs-plus);
  --apl-alias-typography-title-large-font-weight: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-title-large-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-title-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-medium-font-size: var(--apl-base-typography-font-size-xs-md);
  --apl-alias-typography-title-medium-font-weight: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-title-medium-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-title-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-small-font-size: var(--apl-base-typography-font-size-xs-sm);
  --apl-alias-typography-title-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-title-small-line-height: var(--apl-base-typography-line-height-line-height17);
  --apl-base-color-danger-0: var(--apl-color-red-cherry-0);
  --apl-base-color-danger-10: var(--apl-color-red-cherry-10);
  --apl-base-color-danger-100: var(--apl-color-red-cherry-100);
  --apl-base-color-danger-20: var(--apl-color-red-cherry-20);
  --apl-base-color-danger-30: var(--apl-color-red-cherry-30);
  --apl-base-color-danger-40: var(--apl-color-red-cherry-40);
  --apl-base-color-danger-50: var(--apl-color-red-cherry-50);
  --apl-base-color-danger-60: var(--apl-color-red-cherry-60);
  --apl-base-color-danger-70: var(--apl-color-red-cherry-70);
  --apl-base-color-danger-80: var(--apl-color-red-cherry-80);
  --apl-base-color-danger-90: var(--apl-color-red-cherry-90);
  --apl-base-color-danger-95: var(--apl-color-red-cherry-95);
  --apl-base-color-danger-99: var(--apl-color-red-cherry-99);
  --apl-base-color-kid-club-0: var(--apl-color-lilac-soft-0);
  --apl-base-color-kid-club-color-10: #FFFFFF;
  --apl-base-color-kid-club-color-11: #FFFFFF;
  --apl-base-color-kid-club-color-12: #FFFFFF;
  --apl-base-color-kid-club-color-2: #FFFFFF;
  --apl-base-color-kid-club-color-3: #FFFFFF;
  --apl-base-color-kid-club-color-4: #FFFFFF;
  --apl-base-color-kid-club-color-5: #FFFFFF;
  --apl-base-color-kid-club-color-6: #FFFFFF;
  --apl-base-color-kid-club-color-7: #FFFFFF;
  --apl-base-color-kid-club-color-8: #FFFFFF;
  --apl-base-color-kid-club-color-9: #FFFFFF;
  --apl-base-color-kid-club-color: #FFFFFF;
  --apl-base-color-neutral-0: var(--apl-color-gray-smoke-0);
  --apl-base-color-neutral-10: var(--apl-color-gray-smoke-10);
  --apl-base-color-neutral-100: var(--apl-color-gray-smoke-100);
  --apl-base-color-neutral-20: var(--apl-color-gray-smoke-20);
  --apl-base-color-neutral-30: var(--apl-color-gray-smoke-30);
  --apl-base-color-neutral-40: var(--apl-color-gray-smoke-40);
  --apl-base-color-neutral-50: var(--apl-color-gray-smoke-50);
  --apl-base-color-neutral-60: var(--apl-color-gray-smoke-60);
  --apl-base-color-neutral-70: var(--apl-color-gray-smoke-70);
  --apl-base-color-neutral-80: var(--apl-color-gray-smoke-80);
  --apl-base-color-neutral-90: var(--apl-color-gray-smoke-90);
  --apl-base-color-neutral-95: var(--apl-color-gray-smoke-95);
  --apl-base-color-neutral-99: var(--apl-color-gray-smoke-99);
  --apl-base-color-overlay-black-0: var(--apl-color-black-soft-0);
  --apl-base-color-overlay-black-10: var(--apl-color-black-soft-10);
  --apl-base-color-overlay-black-100: var(--apl-color-black-soft-100);
  --apl-base-color-overlay-black-20: var(--apl-color-black-soft-20);
  --apl-base-color-overlay-black-30: var(--apl-color-black-soft-30);
  --apl-base-color-overlay-black-40: var(--apl-color-black-soft-40);
  --apl-base-color-overlay-black-50: var(--apl-color-black-soft-50);
  --apl-base-color-overlay-black-60: var(--apl-color-black-soft-60);
  --apl-base-color-overlay-black-70: var(--apl-color-black-soft-70);
  --apl-base-color-overlay-black-80: var(--apl-color-black-soft-80);
  --apl-base-color-overlay-black-90: var(--apl-color-black-soft-90);
  --apl-base-color-overlay-white-0: var(--apl-color-white-soft-0);
  --apl-base-color-overlay-white-10: var(--apl-color-white-soft-10);
  --apl-base-color-overlay-white-100: var(--apl-color-white-soft-100);
  --apl-base-color-overlay-white-20: var(--apl-color-white-soft-20);
  --apl-base-color-overlay-white-30: var(--apl-color-white-soft-30);
  --apl-base-color-overlay-white-40: var(--apl-color-white-soft-40);
  --apl-base-color-overlay-white-50: var(--apl-color-white-soft-50);
  --apl-base-color-overlay-white-60: var(--apl-color-white-soft-60);
  --apl-base-color-overlay-white-70: var(--apl-color-white-soft-70);
  --apl-base-color-overlay-white-80: var(--apl-color-white-soft-80);
  --apl-base-color-overlay-white-90: var(--apl-color-white-soft-90);
  --apl-base-color-primary-0: var(--apl-color-green-pine-0);
  --apl-base-color-primary-10: var(--apl-color-green-pine-10);
  --apl-base-color-primary-100: var(--apl-color-green-pine-100);
  --apl-base-color-primary-20: var(--apl-color-green-pine-20);
  --apl-base-color-primary-30: var(--apl-color-green-pine-30);
  --apl-base-color-primary-40: var(--apl-color-green-pine-40);
  --apl-base-color-primary-50: var(--apl-color-green-pine-50);
  --apl-base-color-primary-60: var(--apl-color-green-pine-60);
  --apl-base-color-primary-70: var(--apl-color-green-pine-70);
  --apl-base-color-primary-80: var(--apl-color-green-pine-80);
  --apl-base-color-primary-90: var(--apl-color-green-pine-90);
  --apl-base-color-primary-95: var(--apl-color-green-pine-95);
  --apl-base-color-primary-99: var(--apl-color-green-pine-99);
  --apl-base-color-secondary-0: var(--apl-color-gray-bluish-0);
  --apl-base-color-secondary-10: var(--apl-color-gray-bluish-10);
  --apl-base-color-secondary-100: var(--apl-color-gray-bluish-100);
  --apl-base-color-secondary-20: var(--apl-color-gray-bluish-20);
  --apl-base-color-secondary-30: var(--apl-color-gray-bluish-30);
  --apl-base-color-secondary-40: var(--apl-color-gray-bluish-40);
  --apl-base-color-secondary-50: var(--apl-color-gray-bluish-50);
  --apl-base-color-secondary-60: var(--apl-color-gray-bluish-60);
  --apl-base-color-secondary-70: var(--apl-color-gray-bluish-70);
  --apl-base-color-secondary-80: var(--apl-color-gray-bluish-80);
  --apl-base-color-secondary-90: var(--apl-color-gray-bluish-90);
  --apl-base-color-secondary-95: var(--apl-color-gray-bluish-95);
  --apl-base-color-secondary-99: var(--apl-color-gray-bluish-99);
  --apl-base-color-success-0: var(--apl-color-green-matcha-0);
  --apl-base-color-success-10: var(--apl-color-green-matcha-10);
  --apl-base-color-success-100: var(--apl-color-green-matcha-100);
  --apl-base-color-success-20: var(--apl-color-green-matcha-20);
  --apl-base-color-success-30: var(--apl-color-green-matcha-30);
  --apl-base-color-success-40: var(--apl-color-green-matcha-40);
  --apl-base-color-success-50: var(--apl-color-green-matcha-50);
  --apl-base-color-success-60: var(--apl-color-green-matcha-60);
  --apl-base-color-success-70: var(--apl-color-green-matcha-70);
  --apl-base-color-success-80: var(--apl-color-green-matcha-80);
  --apl-base-color-success-90: var(--apl-color-green-matcha-90);
  --apl-base-color-success-95: var(--apl-color-green-matcha-95);
  --apl-base-color-success-99: var(--apl-color-green-matcha-99);
  --apl-base-color-tertiary-0: var(--apl-color-blue-ocean-0);
  --apl-base-color-tertiary-10: var(--apl-color-blue-ocean-10);
  --apl-base-color-tertiary-100: var(--apl-color-blue-ocean-100);
  --apl-base-color-tertiary-20: var(--apl-color-blue-ocean-20);
  --apl-base-color-tertiary-30: var(--apl-color-blue-ocean-30);
  --apl-base-color-tertiary-40: var(--apl-color-blue-ocean-40);
  --apl-base-color-tertiary-50: var(--apl-color-blue-ocean-50);
  --apl-base-color-tertiary-60: var(--apl-color-blue-ocean-60);
  --apl-base-color-tertiary-70: var(--apl-color-blue-ocean-70);
  --apl-base-color-tertiary-80: var(--apl-color-blue-ocean-80);
  --apl-base-color-tertiary-90: var(--apl-color-blue-ocean-90);
  --apl-base-color-tertiary-95: var(--apl-color-blue-ocean-95);
  --apl-base-color-tertiary-99: var(--apl-color-blue-ocean-99);
  --apl-base-color-warning-0: var(--apl-color-yellow-peanut-0);
  --apl-base-color-warning-10: var(--apl-color-yellow-peanut-10);
  --apl-base-color-warning-100: var(--apl-color-yellow-peanut-100);
  --apl-base-color-warning-20: var(--apl-color-yellow-peanut-20);
  --apl-base-color-warning-30: var(--apl-color-yellow-peanut-30);
  --apl-base-color-warning-40: var(--apl-color-yellow-peanut-40);
  --apl-base-color-warning-50: var(--apl-color-yellow-peanut-50);
  --apl-base-color-warning-60: var(--apl-color-yellow-peanut-60);
  --apl-base-color-warning-70: var(--apl-color-yellow-peanut-70);
  --apl-base-color-warning-80: var(--apl-color-yellow-peanut-80);
  --apl-base-color-warning-90: var(--apl-color-yellow-peanut-90);
  --apl-base-color-warning-95: var(--apl-color-yellow-peanut-95);
  --apl-base-color-warning-99: var(--apl-color-yellow-peanut-99);
  --apl-base-elevation-blur-10: var(--apl-elevation-blur-10);
  --apl-base-elevation-blur-12: var(--apl-elevation-blur-12);
  --apl-base-elevation-blur-14: var(--apl-elevation-blur-14);
  --apl-base-elevation-blur-16: var(--apl-elevation-blur-16);
  --apl-base-elevation-blur-18: var(--apl-elevation-blur-18);
  --apl-base-elevation-blur-2: var(--apl-elevation-blur-2);
  --apl-base-elevation-blur-20: var(--apl-elevation-blur-20);
  --apl-base-elevation-blur-22: var(--apl-elevation-blur-22);
  --apl-base-elevation-blur-24: var(--apl-elevation-blur-24);
  --apl-base-elevation-blur-26: var(--apl-elevation-blur-26);
  --apl-base-elevation-blur-28: var(--apl-elevation-blur-28);
  --apl-base-elevation-blur-30: var(--apl-elevation-blur-30);
  --apl-base-elevation-blur-32: var(--apl-elevation-blur-32);
  --apl-base-elevation-blur-4: var(--apl-elevation-blur-4);
  --apl-base-elevation-blur-6: var(--apl-elevation-blur-6);
  --apl-base-elevation-blur-8: var(--apl-elevation-blur-8);
  --apl-base-elevation-blur-none: var(--apl-elevation-blur-none);
  --apl-base-elevation-spread-1: var(--apl-elevation-spread-1);
  --apl-base-elevation-spread-10: var(--apl-elevation-spread-10);
  --apl-base-elevation-spread-12: var(--apl-elevation-spread-12);
  --apl-base-elevation-spread-14: var(--apl-elevation-spread-14);
  --apl-base-elevation-spread-16: var(--apl-elevation-spread-16);
  --apl-base-elevation-spread-18: var(--apl-elevation-spread-18);
  --apl-base-elevation-spread-2: var(--apl-elevation-spread-2);
  --apl-base-elevation-spread-20: var(--apl-elevation-spread-20);
  --apl-base-elevation-spread-22: var(--apl-elevation-spread-22);
  --apl-base-elevation-spread-24: var(--apl-elevation-spread-24);
  --apl-base-elevation-spread-4: var(--apl-elevation-spread-4);
  --apl-base-elevation-spread-6: var(--apl-elevation-spread-8);
  --apl-base-elevation-spread-8: var(--apl-elevation-spread-8);
  --apl-base-elevation-spread-none: var(--apl-elevation-spread-none);
  --apl-base-elevation-x-axis-1: var(--apl-elevation-x-axis-1);
  --apl-base-elevation-x-axis-10: var(--apl-elevation-x-axis-10);
  --apl-base-elevation-x-axis-12: var(--apl-elevation-x-axis-12);
  --apl-base-elevation-x-axis-14: var(--apl-elevation-x-axis-14);
  --apl-base-elevation-x-axis-16: var(--apl-elevation-x-axis-16);
  --apl-base-elevation-x-axis-18: var(--apl-elevation-x-axis-18);
  --apl-base-elevation-x-axis-2: var(--apl-elevation-x-axis-2);
  --apl-base-elevation-x-axis-20: var(--apl-elevation-x-axis-20);
  --apl-base-elevation-x-axis-22: var(--apl-elevation-y-axis-22);
  --apl-base-elevation-x-axis-24: var(--apl-elevation-x-axis-24);
  --apl-base-elevation-x-axis-4: var(--apl-elevation-x-axis-4);
  --apl-base-elevation-x-axis-6: var(--apl-elevation-x-axis-6);
  --apl-base-elevation-x-axis-8: var(--apl-elevation-x-axis-8);
  --apl-base-elevation-x-axis-none: var(--apl-elevation-x-axis-none);
  --apl-base-elevation-y-axis-1: var(--apl-elevation-y-axis-1);
  --apl-base-elevation-y-axis-10: var(--apl-elevation-y-axis-10);
  --apl-base-elevation-y-axis-12: var(--apl-elevation-y-axis-12);
  --apl-base-elevation-y-axis-14: var(--apl-elevation-y-axis-14);
  --apl-base-elevation-y-axis-16: var(--apl-elevation-y-axis-16);
  --apl-base-elevation-y-axis-18: var(--apl-elevation-y-axis-18);
  --apl-base-elevation-y-axis-2: var(--apl-elevation-y-axis-2);
  --apl-base-elevation-y-axis-20: var(--apl-elevation-y-axis-20);
  --apl-base-elevation-y-axis-22: var(--apl-elevation-y-axis-22);
  --apl-base-elevation-y-axis-24: var(--apl-elevation-y-axis-24);
  --apl-base-elevation-y-axis-4: var(--apl-elevation-y-axis-4);
  --apl-base-elevation-y-axis-6: var(--apl-elevation-y-axis-6);
  --apl-base-elevation-y-axis-8: var(--apl-elevation-y-axis-8);
  --apl-base-elevation-y-axis-none: var(--apl-elevation-y-axis-none);
  --apl-base-radius-10: var(--apl-radius-10);
  --apl-base-radius-12: var(--apl-radius-12);
  --apl-base-radius-14: var(--apl-radius-14);
  --apl-base-radius-16: var(--apl-radius-16);
  --apl-base-radius-18: var(--apl-radius-18);
  --apl-base-radius-2: var(--apl-radius-2);
  --apl-base-radius-20: var(--apl-radius-20);
  --apl-base-radius-22: var(--apl-radius-22);
  --apl-base-radius-24: var(--apl-radius-24);
  --apl-base-radius-26: var(--apl-radius-26);
  --apl-base-radius-28: var(--apl-radius-28);
  --apl-base-radius-30: var(--apl-radius-30);
  --apl-base-radius-32: var(--apl-radius-32);
  --apl-base-radius-34: var(--apl-radius-34);
  --apl-base-radius-36: var(--apl-radius-36);
  --apl-base-radius-38: var(--apl-radius-38);
  --apl-base-radius-4: var(--apl-radius-4);
  --apl-base-radius-40: var(--apl-radius-40);
  --apl-base-radius-42: var(--apl-radius-42);
  --apl-base-radius-44: var(--apl-radius-44);
  --apl-base-radius-46: var(--apl-radius-46);
  --apl-base-radius-48: var(--apl-radius-48);
  --apl-base-radius-50: var(--apl-radius-50);
  --apl-base-radius-52: var(--apl-radius-52);
  --apl-base-radius-54: var(--apl-radius-54);
  --apl-base-radius-56: var(--apl-radius-56);
  --apl-base-radius-58: var(--apl-radius-58);
  --apl-base-radius-6: var(--apl-radius-6);
  --apl-base-radius-60: var(--apl-radius-60);
  --apl-base-radius-62: var(--apl-radius-62);
  --apl-base-radius-64: var(--apl-radius-64);
  --apl-base-radius-66: var(--apl-radius-66);
  --apl-base-radius-68: var(--apl-radius-68);
  --apl-base-radius-70: var(--apl-radius-70);
  --apl-base-radius-72: var(--apl-radius-72);
  --apl-base-radius-74: var(--apl-radius-74);
  --apl-base-radius-76: var(--apl-radius-76);
  --apl-base-radius-78: var(--apl-radius-78);
  --apl-base-radius-8: var(--apl-radius-8);
  --apl-base-radius-80: var(--apl-radius-80);
  --apl-base-radius-full: var(--apl-radius-full);
  --apl-base-radius-none: var(--apl-radius-none);
  --apl-base-spacing-neg-space1: var(--apl-spacing-reverse-2);
  --apl-base-spacing-neg-space2: var(--apl-spacing-reverse-4);
  --apl-base-spacing-neg-space3: var(--apl-spacing-reverse-6);
  --apl-base-spacing-neg-space4: var(--apl-spacing-reverse-8);
  --apl-base-spacing-space1: var(--apl-spacing-none);
  --apl-base-spacing-space10: var(--apl-spacing-24);
  --apl-base-spacing-space11: var(--apl-spacing-28);
  --apl-base-spacing-space12: var(--apl-spacing-32);
  --apl-base-spacing-space13: var(--apl-spacing-36);
  --apl-base-spacing-space14: var(--apl-spacing-40);
  --apl-base-spacing-space15: var(--apl-spacing-48);
  --apl-base-spacing-space2: var(--apl-spacing-2);
  --apl-base-spacing-space3: var(--apl-spacing-4);
  --apl-base-spacing-space4: var(--apl-spacing-6);
  --apl-base-spacing-space5: var(--apl-spacing-8);
  --apl-base-spacing-space6: var(--apl-spacing-10);
  --apl-base-spacing-space7: var(--apl-spacing-12);
  --apl-base-spacing-space8: var(--apl-spacing-16);
  --apl-base-spacing-space9: var(--apl-spacing-20);
  --apl-base-spacing-tertiary-tertiary: var(--apl-base-color-tertiary-40);
  --apl-base-spacing-title-large-font-size: var(--apl-spacing-font-scale-scale8);
  --apl-base-spacing-title-large-letter-spacing: var(--apl-spacing-letter-spacing-letter3);
  --apl-base-spacing-title-large-line-height: var(--apl-spacing-line-height-height8);
  --apl-base-typography-font-family-ibm-plex-sans-thai: var(--apl-typography-font-family-ibm-plex-sans-thai);
  --apl-base-typography-font-size-2xl: var(--apl-typography-font-size-45);
  --apl-base-typography-font-size-2xs: var(--apl-typography-font-size-14);
  --apl-base-typography-font-size-3xl: var(--apl-typography-font-size-57);
  --apl-base-typography-font-size-3xs: var(--apl-typography-font-size-12);
  --apl-base-typography-font-size-4xs: var(--apl-typography-font-size-10);
  --apl-base-typography-font-size-lg: var(--apl-typography-font-size-32);
  --apl-base-typography-font-size-md: var(--apl-typography-font-size-28);
  --apl-base-typography-font-size-sm: var(--apl-typography-font-size-24);
  --apl-base-typography-font-size-xl: var(--apl-typography-font-size-36);
  --apl-base-typography-font-size-xs-md: var(--apl-typography-font-size-20);
  --apl-base-typography-font-size-xs-plus: var(--apl-typography-font-size-22);
  --apl-base-typography-font-size-xs-sm: var(--apl-typography-font-size-18);
  --apl-base-typography-font-size-xs: var(--apl-typography-font-size-16);
  --apl-base-typography-font-weight-weight1: var(--apl-typography-font-weight-regular);
  --apl-base-typography-font-weight-weight2: var(--apl-typography-font-weight-medium);
  --apl-base-typography-font-weight-weight3: var(--apl-typography-font-weight-bold);
  --apl-base-typography-line-height-line-height1: var(--apl-typography-line-height-4);
  --apl-base-typography-line-height-line-height10: var(--apl-typography-line-height-20);
  --apl-base-typography-line-height-line-height11: var(--apl-typography-line-height-21);
  --apl-base-typography-line-height-line-height12: var(--apl-typography-line-height-22);
  --apl-base-typography-line-height-line-height13: var(--apl-typography-line-height-24);
  --apl-base-typography-line-height-line-height14: var(--apl-typography-line-height-26);
  --apl-base-typography-line-height-line-height15: var(--apl-typography-line-height-27);
  --apl-base-typography-line-height-line-height16: var(--apl-typography-line-height-28);
  --apl-base-typography-line-height-line-height17: var(--apl-typography-line-height-30);
  --apl-base-typography-line-height-line-height18: var(--apl-typography-line-height-32);
  --apl-base-typography-line-height-line-height19: var(--apl-typography-line-height-33);
  --apl-base-typography-line-height-line-height2: var(--apl-typography-line-height-6);
  --apl-base-typography-line-height-line-height20: var(--apl-typography-line-height-34);
  --apl-base-typography-line-height-line-height21: var(--apl-typography-line-height-36);
  --apl-base-typography-line-height-line-height22: var(--apl-typography-line-height-38);
  --apl-base-typography-line-height-line-height23: var(--apl-typography-line-height-40);
  --apl-base-typography-line-height-line-height24: var(--apl-typography-line-height-42);
  --apl-base-typography-line-height-line-height25: var(--apl-typography-line-height-44);
  --apl-base-typography-line-height-line-height26: var(--apl-typography-line-height-46);
  --apl-base-typography-line-height-line-height27: var(--apl-typography-line-height-48);
  --apl-base-typography-line-height-line-height28: var(--apl-typography-line-height-50);
  --apl-base-typography-line-height-line-height29: var(--apl-typography-line-height-52);
  --apl-base-typography-line-height-line-height3: var(--apl-typography-line-height-8);
  --apl-base-typography-line-height-line-height30: var(--apl-typography-line-height-54);
  --apl-base-typography-line-height-line-height31: var(--apl-typography-line-height-56);
  --apl-base-typography-line-height-line-height32: var(--apl-typography-line-height-58);
  --apl-base-typography-line-height-line-height33: var(--apl-typography-line-height-60);
  --apl-base-typography-line-height-line-height34: var(--apl-typography-line-height-62);
  --apl-base-typography-line-height-line-height35: var(--apl-typography-line-height-64);
  --apl-base-typography-line-height-line-height36: var(--apl-typography-line-height-66);
  --apl-base-typography-line-height-line-height37: var(--apl-typography-line-height-67);
  --apl-base-typography-line-height-line-height38: var(--apl-typography-line-height-68);
  --apl-base-typography-line-height-line-height39: var(--apl-typography-line-height-70);
  --apl-base-typography-line-height-line-height4: var(--apl-typography-line-height-10);
  --apl-base-typography-line-height-line-height40: var(--apl-typography-line-height-72);
  --apl-base-typography-line-height-line-height41: var(--apl-typography-line-height-74);
  --apl-base-typography-line-height-line-height42: var(--apl-typography-line-height-76);
  --apl-base-typography-line-height-line-height43: var(--apl-typography-line-height-78);
  --apl-base-typography-line-height-line-height44: var(--apl-typography-line-height-80);
  --apl-base-typography-line-height-line-height45: var(--apl-typography-line-height-82);
  --apl-base-typography-line-height-line-height46: var(--apl-typography-line-height-84);
  --apl-base-typography-line-height-line-height47: var(--apl-typography-line-height-85);
  --apl-base-typography-line-height-line-height48: var(--apl-typography-line-height-86);
  --apl-base-typography-line-height-line-height49: var(--apl-typography-line-height-88);
  --apl-base-typography-line-height-line-height5: var(--apl-typography-line-height-12);
  --apl-base-typography-line-height-line-height50: var(--apl-typography-line-height-96);
  --apl-base-typography-line-height-line-height51: var(--apl-typography-line-height-132);
  --apl-base-typography-line-height-line-height6: var(--apl-typography-line-height-14);
  --apl-base-typography-line-height-line-height7: var(--apl-typography-line-height-15);
  --apl-base-typography-line-height-line-height8: var(--apl-typography-line-height-16);
  --apl-base-typography-line-height-line-height9: var(--apl-typography-line-height-18);
  --apl-color-black-soft-0: #0000000D;
  --apl-color-black-soft-10: #0000001A;
  --apl-color-black-soft-100: #000000;
  --apl-color-black-soft-20: #00000033;
  --apl-color-black-soft-30: #0000004D;
  --apl-color-black-soft-40: #00000066;
  --apl-color-black-soft-50: #00000080;
  --apl-color-black-soft-60: #00000099;
  --apl-color-black-soft-70: #000000B3;
  --apl-color-black-soft-80: #000000CC;
  --apl-color-black-soft-90: #000000E6;
  --apl-color-blue-ocean-0: #000000;
  --apl-color-blue-ocean-10: #001159;
  --apl-color-blue-ocean-100: #FFFFFF;
  --apl-color-blue-ocean-20: #00218C;
  --apl-color-blue-ocean-30: #0032C4;
  --apl-color-blue-ocean-40: #2E4EDC;
  --apl-color-blue-ocean-50: #4C6AF6;
  --apl-color-blue-ocean-60: #7087FF;
  --apl-color-blue-ocean-70: #95A6FF;
  --apl-color-blue-ocean-80: #B9C3FF;
  --apl-color-blue-ocean-90: #DEE1FF;
  --apl-color-blue-ocean-95: #F0EFFF;
  --apl-color-blue-ocean-99: #FEFBFF;
  --apl-color-gray-bluish-0: #000000;
  --apl-color-gray-bluish-10: #131C2B;
  --apl-color-gray-bluish-100: #FFFFFF;
  --apl-color-gray-bluish-20: #283141;
  --apl-color-gray-bluish-30: #3E4758;
  --apl-color-gray-bluish-40: #565F71;
  --apl-color-gray-bluish-50: #6E778A;
  --apl-color-gray-bluish-60: #8891A4;
  --apl-color-gray-bluish-70: #A2ABC0;
  --apl-color-gray-bluish-80: #BEC7DB;
  --apl-color-gray-bluish-90: #DAE3F8;
  --apl-color-gray-bluish-95: #ECF0FF;
  --apl-color-gray-bluish-99: #FDFBFF;
  --apl-color-gray-smoke-0: #000000;
  --apl-color-gray-smoke-10: #1C1B1C;
  --apl-color-gray-smoke-100: #FFFFFF;
  --apl-color-gray-smoke-20: #313031;
  --apl-color-gray-smoke-30: #474647;
  --apl-color-gray-smoke-40: #5F5E5F;
  --apl-color-gray-smoke-50: #787777;
  --apl-color-gray-smoke-60: #929091;
  --apl-color-gray-smoke-70: #ADABAB;
  --apl-color-gray-smoke-80: #C8C6C6;
  --apl-color-gray-smoke-90: #E5E2E2;
  --apl-color-gray-smoke-95: #F3F0F0;
  --apl-color-gray-smoke-99: #F8F7F7;
  --apl-color-green-matcha-0: #000000;
  --apl-color-green-matcha-10: #233D18;
  --apl-color-green-matcha-100: #FFFFFF;
  --apl-color-green-matcha-20: #2F5220;
  --apl-color-green-matcha-30: #477A2F;
  --apl-color-green-matcha-40: #5EA33F;
  --apl-color-green-matcha-50: #76CC4F;
  --apl-color-green-matcha-60: #91D672;
  --apl-color-green-matcha-70: #ADE095;
  --apl-color-green-matcha-80: #C8EBB9;
  --apl-color-green-matcha-90: #D6F0CA;
  --apl-color-green-matcha-95: #E4F5DC;
  --apl-color-green-matcha-99: #F1FAED;
  --apl-color-green-pine-0: #000000;
  --apl-color-green-pine-10: #002109;
  --apl-color-green-pine-100: #FFFFFF;
  --apl-color-green-pine-20: #003915;
  --apl-color-green-pine-30: #005321;
  --apl-color-green-pine-40: #016E2E;
  --apl-color-green-pine-50: #2C8745;
  --apl-color-green-pine-60: #49A25C;
  --apl-color-green-pine-70: #64BE74;
  --apl-color-green-pine-80: #7FDA8E;
  --apl-color-green-pine-90: #9BF7A7;
  --apl-color-green-pine-95: #C5FFC8;
  --apl-color-green-pine-99: #F6FFF2;
  --apl-color-lilac-soft-0: #000000;
  --apl-color-lilac-soft-10: #3F2745;
  --apl-color-lilac-soft-100: #FFFFFF;
  --apl-color-lilac-soft-20: #5E4264;
  --apl-color-lilac-soft-30: #8C6C94;
  --apl-color-lilac-soft-40: #AB87B3;
  --apl-color-lilac-soft-50: #CAA3D3;
  --apl-color-lilac-soft-60: #D5B5DC;
  --apl-color-lilac-soft-70: #DFC8E5;
  --apl-color-lilac-soft-80: #EADAED;
  --apl-color-lilac-soft-90: #EFE3F2;
  --apl-color-lilac-soft-95: #F4EDF6;
  --apl-color-lilac-soft-99: #FAF6FB;
  --apl-color-red-cherry-0: #000000;
  --apl-color-red-cherry-10: #410001;
  --apl-color-red-cherry-100: #FFFFFF;
  --apl-color-red-cherry-20: #690003;
  --apl-color-red-cherry-30: #930006;
  --apl-color-red-cherry-40: #C0000B;
  --apl-color-red-cherry-50: #E9211E;
  --apl-color-red-cherry-60: #FF5546;
  --apl-color-red-cherry-70: #FF8A7B;
  --apl-color-red-cherry-80: #FFB4AA;
  --apl-color-red-cherry-90: #FFDAD5;
  --apl-color-red-cherry-95: #FFEDEA;
  --apl-color-red-cherry-99: #FFF4F3;
  --apl-color-white-soft-0: #FFFFFF0D;
  --apl-color-white-soft-10: #FFFFFF1A;
  --apl-color-white-soft-100: #FFFFFF;
  --apl-color-white-soft-20: #FFFFFF33;
  --apl-color-white-soft-30: #FFFFFF4D;
  --apl-color-white-soft-40: #FFFFFF66;
  --apl-color-white-soft-50: #FFFFFF80;
  --apl-color-white-soft-60: #FFFFFF99;
  --apl-color-white-soft-70: #FFFFFFB3;
  --apl-color-white-soft-80: #FFFFFFCC;
  --apl-color-white-soft-90: #FFFFFFE6;
  --apl-color-yellow-peanut-0: #000000;
  --apl-color-yellow-peanut-10: #403300;
  --apl-color-yellow-peanut-100: #FFFFFF;
  --apl-color-yellow-peanut-20: #665200;
  --apl-color-yellow-peanut-30: #8C7000;
  --apl-color-yellow-peanut-40: #B38F00;
  --apl-color-yellow-peanut-50: #D9AF09;
  --apl-color-yellow-peanut-60: #FFD118;
  --apl-color-yellow-peanut-70: #FFD940;
  --apl-color-yellow-peanut-80: #FFE169;
  --apl-color-yellow-peanut-90: #FFE991;
  --apl-color-yellow-peanut-95: #FFF1BA;
  --apl-color-yellow-peanut-99: #FFFAE6;
  --apl-elevation-blur-10: 10px;
  --apl-elevation-blur-12: 12px;
  --apl-elevation-blur-14: 14px;
  --apl-elevation-blur-16: 16px;
  --apl-elevation-blur-18: 18px;
  --apl-elevation-blur-2: 2px;
  --apl-elevation-blur-20: 20px;
  --apl-elevation-blur-22: 22px;
  --apl-elevation-blur-24: 24px;
  --apl-elevation-blur-26: 26px;
  --apl-elevation-blur-28: 28px;
  --apl-elevation-blur-30: 30px;
  --apl-elevation-blur-32: 32px;
  --apl-elevation-blur-4: 4px;
  --apl-elevation-blur-6: 6px;
  --apl-elevation-blur-8: 8px;
  --apl-elevation-blur-none: 0px;
  --apl-elevation-spread-1: 1px;
  --apl-elevation-spread-10: 10px;
  --apl-elevation-spread-12: 12px;
  --apl-elevation-spread-14: 14px;
  --apl-elevation-spread-16: 16px;
  --apl-elevation-spread-18: 18px;
  --apl-elevation-spread-2: 2px;
  --apl-elevation-spread-20: 20px;
  --apl-elevation-spread-22: 22px;
  --apl-elevation-spread-24: 24px;
  --apl-elevation-spread-4: 4px;
  --apl-elevation-spread-6: 6px;
  --apl-elevation-spread-8: 8px;
  --apl-elevation-spread-none: 0px;
  --apl-elevation-x-axis-1: 1px;
  --apl-elevation-x-axis-10: 10px;
  --apl-elevation-x-axis-12: 12px;
  --apl-elevation-x-axis-14: 14px;
  --apl-elevation-x-axis-16: 16px;
  --apl-elevation-x-axis-18: 18px;
  --apl-elevation-x-axis-2: 2px;
  --apl-elevation-x-axis-20: 20px;
  --apl-elevation-x-axis-22: 22px;
  --apl-elevation-x-axis-24: 24px;
  --apl-elevation-x-axis-4: 4px;
  --apl-elevation-x-axis-6: 6px;
  --apl-elevation-x-axis-8: 8px;
  --apl-elevation-x-axis-none: 0px;
  --apl-elevation-y-axis-1: 1px;
  --apl-elevation-y-axis-10: 10px;
  --apl-elevation-y-axis-12: 12px;
  --apl-elevation-y-axis-14: 14px;
  --apl-elevation-y-axis-16: 16px;
  --apl-elevation-y-axis-18: 18px;
  --apl-elevation-y-axis-2: 2px;
  --apl-elevation-y-axis-20: 20px;
  --apl-elevation-y-axis-22: 22px;
  --apl-elevation-y-axis-24: 24px;
  --apl-elevation-y-axis-4: 4px;
  --apl-elevation-y-axis-6: 6px;
  --apl-elevation-y-axis-8: 8px;
  --apl-elevation-y-axis-none: 0px;
  --apl-radius-10: 10px;
  --apl-radius-12: 12px;
  --apl-radius-14: 14px;
  --apl-radius-16: 16px;
  --apl-radius-18: 18px;
  --apl-radius-2: 2px;
  --apl-radius-20: 20px;
  --apl-radius-22: 22px;
  --apl-radius-24: 24px;
  --apl-radius-26: 26px;
  --apl-radius-28: 28px;
  --apl-radius-30: 30px;
  --apl-radius-32: 32px;
  --apl-radius-34: 34px;
  --apl-radius-36: 36px;
  --apl-radius-38: 38px;
  --apl-radius-4: 4px;
  --apl-radius-40: 40px;
  --apl-radius-42: 42px;
  --apl-radius-44: 44px;
  --apl-radius-46: 46px;
  --apl-radius-48: 48px;
  --apl-radius-50: 50px;
  --apl-radius-52: 52px;
  --apl-radius-54: 54px;
  --apl-radius-56: 56px;
  --apl-radius-58: 58px;
  --apl-radius-6: 6px;
  --apl-radius-60: 60px;
  --apl-radius-62: 62px;
  --apl-radius-64: 64px;
  --apl-radius-66: 66px;
  --apl-radius-68: 68px;
  --apl-radius-70: 70px;
  --apl-radius-72: 72px;
  --apl-radius-74: 74px;
  --apl-radius-76: 76px;
  --apl-radius-78: 78px;
  --apl-radius-8: 8px;
  --apl-radius-80: 80px;
  --apl-radius-full: 9999px;
  --apl-radius-none: 0px;
  --apl-spacing-10: 10px;
  --apl-spacing-100: 100px;
  --apl-spacing-102: 102px;
  --apl-spacing-104: 104px;
  --apl-spacing-106: 106px;
  --apl-spacing-108: 108px;
  --apl-spacing-110: 110px;
  --apl-spacing-112: 112px;
  --apl-spacing-114: 114px;
  --apl-spacing-116: 116px;
  --apl-spacing-118: 118px;
  --apl-spacing-12: 12px;
  --apl-spacing-120: 120px;
  --apl-spacing-122: 122px;
  --apl-spacing-124: 124px;
  --apl-spacing-126: 126px;
  --apl-spacing-128: 128px;
  --apl-spacing-14: 14px;
  --apl-spacing-16: 16px;
  --apl-spacing-18: 18px;
  --apl-spacing-2: 2px;
  --apl-spacing-20: 20px;
  --apl-spacing-21: 21px;
  --apl-spacing-22: 22px;
  --apl-spacing-24: 24px;
  --apl-spacing-26: 26px;
  --apl-spacing-28: 28px;
  --apl-spacing-30: 30px;
  --apl-spacing-32: 32px;
  --apl-spacing-34: 34px;
  --apl-spacing-36: 36px;
  --apl-spacing-38: 38px;
  --apl-spacing-4: 4px;
  --apl-spacing-40: 40px;
  --apl-spacing-42: 42px;
  --apl-spacing-44: 44px;
  --apl-spacing-46: 46px;
  --apl-spacing-48: 48px;
  --apl-spacing-50: 50px;
  --apl-spacing-52: 52px;
  --apl-spacing-54: 54px;
  --apl-spacing-56: 56px;
  --apl-spacing-58: 58px;
  --apl-spacing-6: 6px;
  --apl-spacing-60: 60px;
  --apl-spacing-62: 62px;
  --apl-spacing-64: 64px;
  --apl-spacing-66: 66px;
  --apl-spacing-68: 68px;
  --apl-spacing-70: 70px;
  --apl-spacing-72: 72px;
  --apl-spacing-74: 74px;
  --apl-spacing-76: 76px;
  --apl-spacing-78: 78px;
  --apl-spacing-8: 8px;
  --apl-spacing-80: 80px;
  --apl-spacing-82: 82px;
  --apl-spacing-84: 84px;
  --apl-spacing-86: 86px;
  --apl-spacing-88: 88px;
  --apl-spacing-90: 90px;
  --apl-spacing-92: 92px;
  --apl-spacing-94: 94px;
  --apl-spacing-96: 96px;
  --apl-spacing-98: 98px;
  --apl-spacing-none: 0px;
  --apl-spacing-reverse-2: -2px;
  --apl-spacing-reverse-4: -4px;
  --apl-spacing-reverse-6: -6px;
  --apl-spacing-reverse-8: -8px;
  --apl-typography-font-family-ibm-plex-sans-thai: IBM Plex Sans Thai;
  --apl-typography-font-size-10: 10px;
  --apl-typography-font-size-12: 12px;
  --apl-typography-font-size-14: 14px;
  --apl-typography-font-size-15: 15px;
  --apl-typography-font-size-16: 16px;
  --apl-typography-font-size-18: 18px;
  --apl-typography-font-size-20: 20px;
  --apl-typography-font-size-21: 21px;
  --apl-typography-font-size-22: 22px;
  --apl-typography-font-size-24: 24px;
  --apl-typography-font-size-26: 26px;
  --apl-typography-font-size-27: 27px;
  --apl-typography-font-size-28: 28px;
  --apl-typography-font-size-30: 30px;
  --apl-typography-font-size-32: 32px;
  --apl-typography-font-size-33: 33px;
  --apl-typography-font-size-34: 34px;
  --apl-typography-font-size-36: 36px;
  --apl-typography-font-size-38: 38px;
  --apl-typography-font-size-4: 4px;
  --apl-typography-font-size-40: 40px;
  --apl-typography-font-size-42: 42px;
  --apl-typography-font-size-44: 44px;
  --apl-typography-font-size-45: 45px;
  --apl-typography-font-size-46: 46px;
  --apl-typography-font-size-47: 47px;
  --apl-typography-font-size-48: 48px;
  --apl-typography-font-size-50: 50px;
  --apl-typography-font-size-52: 52px;
  --apl-typography-font-size-54: 54px;
  --apl-typography-font-size-56: 56px;
  --apl-typography-font-size-57: 57px;
  --apl-typography-font-size-58: 58px;
  --apl-typography-font-size-6: 6px;
  --apl-typography-font-size-60: 60px;
  --apl-typography-font-size-62: 62px;
  --apl-typography-font-size-64: 64px;
  --apl-typography-font-size-66: 66px;
  --apl-typography-font-size-67-5: 67.5px;
  --apl-typography-font-size-68: 68px;
  --apl-typography-font-size-70: 70px;
  --apl-typography-font-size-72: 72px;
  --apl-typography-font-size-74: 74px;
  --apl-typography-font-size-76: 76px;
  --apl-typography-font-size-78: 78px;
  --apl-typography-font-size-8: 8px;
  --apl-typography-font-size-80: 80px;
  --apl-typography-font-size-82: 82px;
  --apl-typography-font-size-84: 84px;
  --apl-typography-font-size-85: 85.5px;
  --apl-typography-font-size-86: 86px;
  --apl-typography-font-size-88: 88px;
  --apl-typography-font-spacing-10: 10px;
  --apl-typography-font-spacing-12: 12px;
  --apl-typography-font-spacing-14: 14px;
  --apl-typography-font-spacing-16: 16px;
  --apl-typography-font-spacing-18: 18px;
  --apl-typography-font-spacing-20: 20px;
  --apl-typography-font-spacing-22: 22px;
  --apl-typography-font-spacing-24: 24px;
  --apl-typography-font-spacing-26: 26px;
  --apl-typography-font-spacing-28: 28px;
  --apl-typography-font-spacing-30: 30px;
  --apl-typography-font-spacing-32: 32px;
  --apl-typography-font-spacing-34: 34px;
  --apl-typography-font-spacing-36: 36px;
  --apl-typography-font-spacing-38: 38px;
  --apl-typography-font-spacing-4: 4px;
  --apl-typography-font-spacing-40: 40px;
  --apl-typography-font-spacing-42: 42px;
  --apl-typography-font-spacing-44: 44px;
  --apl-typography-font-spacing-46: 46px;
  --apl-typography-font-spacing-48: 48px;
  --apl-typography-font-spacing-50: 50px;
  --apl-typography-font-spacing-52: 52px;
  --apl-typography-font-spacing-54: 54px;
  --apl-typography-font-spacing-56: 56px;
  --apl-typography-font-spacing-58: 58px;
  --apl-typography-font-spacing-6: 6px;
  --apl-typography-font-spacing-60: 60px;
  --apl-typography-font-spacing-62: 62px;
  --apl-typography-font-spacing-64: 64px;
  --apl-typography-font-spacing-66: 66px;
  --apl-typography-font-spacing-68: 68px;
  --apl-typography-font-spacing-70: 70px;
  --apl-typography-font-spacing-72: 72px;
  --apl-typography-font-spacing-74: 74px;
  --apl-typography-font-spacing-76: 76px;
  --apl-typography-font-spacing-78: 78px;
  --apl-typography-font-spacing-8: 8px;
  --apl-typography-font-spacing-80: 80px;
  --apl-typography-font-spacing-82: 82px;
  --apl-typography-font-spacing-84: 84px;
  --apl-typography-font-spacing-86: 86px;
  --apl-typography-font-spacing-88: 88px;
  --apl-typography-font-weight-black-it: 900;
  --apl-typography-font-weight-black: 900;
  --apl-typography-font-weight-bold-it: 700;
  --apl-typography-font-weight-bold: 700;
  --apl-typography-font-weight-extra-light: 200;
  --apl-typography-font-weight-light: 300;
  --apl-typography-font-weight-medium-it: 500;
  --apl-typography-font-weight-medium: 500;
  --apl-typography-font-weight-regular: 400;
  --apl-typography-font-weight-semi-bold: 600;
  --apl-typography-font-weight-thin-it: 100;
  --apl-typography-font-weight-thin: 100;
  --apl-typography-line-height-10: 10px;
  --apl-typography-line-height-12: 12px;
  --apl-typography-line-height-132: 132px;
  --apl-typography-line-height-14: 14px;
  --apl-typography-line-height-15: 15px;
  --apl-typography-line-height-16: 16px;
  --apl-typography-line-height-18: 18px;
  --apl-typography-line-height-20: 20px;
  --apl-typography-line-height-21: 21px;
  --apl-typography-line-height-22: 22px;
  --apl-typography-line-height-24: 24px;
  --apl-typography-line-height-26: 26px;
  --apl-typography-line-height-27: 27px;
  --apl-typography-line-height-28: 28px;
  --apl-typography-line-height-30: 30px;
  --apl-typography-line-height-32: 32px;
  --apl-typography-line-height-33: 33px;
  --apl-typography-line-height-34: 34px;
  --apl-typography-line-height-36: 36px;
  --apl-typography-line-height-38: 38px;
  --apl-typography-line-height-4: 4px;
  --apl-typography-line-height-40: 40px;
  --apl-typography-line-height-42: 42px;
  --apl-typography-line-height-44: 44px;
  --apl-typography-line-height-46: 46px;
  --apl-typography-line-height-48: 48px;
  --apl-typography-line-height-50: 50px;
  --apl-typography-line-height-52: 52px;
  --apl-typography-line-height-54: 54px;
  --apl-typography-line-height-56: 56px;
  --apl-typography-line-height-58: 58px;
  --apl-typography-line-height-6: 6px;
  --apl-typography-line-height-60: 60px;
  --apl-typography-line-height-62: 62px;
  --apl-typography-line-height-64: 64px;
  --apl-typography-line-height-66: 66px;
  --apl-typography-line-height-67: 67.5px;
  --apl-typography-line-height-68: 68px;
  --apl-typography-line-height-70: 70px;
  --apl-typography-line-height-72: 72px;
  --apl-typography-line-height-74: 74px;
  --apl-typography-line-height-76: 76px;
  --apl-typography-line-height-78: 78px;
  --apl-typography-line-height-8: 8px;
  --apl-typography-line-height-80: 80px;
  --apl-typography-line-height-82: 82px;
  --apl-typography-line-height-84: 84px;
  --apl-typography-line-height-85: 85.5px;
  --apl-typography-line-height-86: 86px;
  --apl-typography-line-height-88: 88px;
  --apl-typography-line-height-96: 96px;
}