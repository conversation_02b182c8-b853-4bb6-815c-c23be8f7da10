.badgeRoot {
    display: flex;
    max-height: 24px;
    padding: var(--apl-base-spacing-space3, 4px) 8px;
    align-items: center;
    gap: 8px;
    border-radius: 4px;
    background: var(--apl-chip-background);
    color: var(--apl-chip-color);

    white-space: nowrap;
    z-index: auto;
}

.badgeIndicator {
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    transform: translate(50%, -50%);
    transform-origin: 100% 0%;
}

.badgeWrapper {
    position: relative;
}

.badgeDefault {
    --apl-chip-background: var(--apl-alias-color-secondary-secondary-container, #ECF0FF);
    --apl-chip-color: var(--apl-alias-color-secondary-on-secondary-container, #3E4758);
}

.badgeProcess {
    --apl-chip-background: var(--apl-alias-color-tertiary-tertiary-container, #F0EFFF);
    --apl-chip-color: var(--apl-alias-color-tertiary-on-tertiary-container, #0032C4);
}

.badgeSuccess {
    --apl-chip-background: var(--apl-alias-color-success-success-container, #E4F5DC);
    --apl-chip-color: var(--apl-alias-color-success-on-success-container, #477A2F);
}

.badgeWarning {
    --apl-chip-background: var(--apl-alias-color-warning-warning-container, #FFFAE6);
    --apl-chip-color: var(--apl-alias-color-warning-on-warning-container, #8C7000);
}

.badgeError {
    --apl-chip-background: var(--apl-alias-color-error-error-container, #FFEDEA);
    --apl-chip-color: var(--apl-alias-color-error-on-error-container, #930006);
}

.badgeIcon {
    display: flex;
    align-items: center;
    & svg {
        width: 14px;
    }
}