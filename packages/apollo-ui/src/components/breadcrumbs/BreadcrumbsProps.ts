import { HTMLAttributes } from "react"

export type BreadcrumbsProps = {
  /**
   * The content of the component.
   */
  children?: React.ReactNode
  className?: string


  separator?: React.ReactNode
  /**
   * If max items is exceeded, the number of items to show after the ellipsis.
   * @default 1
   */
  itemsAfterCollapse?: number
  /**
   * If max items is exceeded, the number of items to show before the ellipsis.
   * @default 1
   */
  itemsBeforeCollapse?: number
  /**
   * Specifies the maximum number of breadcrumbs to display. When there are more
   * than the maximum number, only the first `itemsBeforeCollapse` and last `itemsAfterCollapse`
   * will be shown, with an ellipsis in between.
   * @default 7
   */
  maxItems?: number
  ref?: React.Ref<HTMLElement>
} & HTMLAttributes<HTMLElement>
