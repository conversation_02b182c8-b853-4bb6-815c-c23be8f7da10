.breadcrumbsList {
    display: flex;
    align-items: center;
    align-content: center;
    gap: 8px var(--apl-alias-spacing-gap-gap5, 8px);
    flex-wrap: wrap;

}

.breadcrumbsRoot {
    composes: apl-typography-body-small from "../../base.module.css";

    & :global(.ApolloTypography--root) {
        font-size: inherit;
    }

    & button {
        font-size: inherit;
        padding: 0;
        --apl-icon-button-size: 14px
    }
}

.breadcrumbsItem {
    display: flex;
    padding: 4px;
    align-items: flex-start;
    gap: 10px;
    color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

    &:hover {
        border-radius: 4px;
        background-color: var(--apl-alias-color-primary-text-only-background-hovered, #E5E2E2);
    }
}

.breadcrumbsEllipsis {
    display: flex;
    padding: 4px;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    color: var(--apl-alias-color-primary-primary, #016E2E);
}

.breadcrumbsSeparator {
    display: inline-block;
    color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);

    & svg {
        width: 12px;
    }
}

.breadcrumbsLastItem {
    color: var(--apl-alias-color-primary-primary, #016E2E);
}

.breadcrumbsDisabled {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    cursor: not-allowed;
    pointer-events: none;
}