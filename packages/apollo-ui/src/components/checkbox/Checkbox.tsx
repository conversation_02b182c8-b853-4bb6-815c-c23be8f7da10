import { useCallback } from "react"
import { Checkbox as BaseUICheckbox } from "@base-ui-components/react/checkbox"
import classNames from "classnames"

import { Typography } from "../typography"
import styles from "./checkbox.module.css"
import type { CheckboxProps } from "./CheckboxProps"

export function Checkbox({
  className,
  indeterminate,
  id,
  label,
  onChange,
  onCheckedChange,
  ref,
  wrapperRef,
  rootRef,
  ...inputProps
}: CheckboxProps) {
  const handleChange = useCallback(
    (checked: boolean, event: Event) => {
      onChange?.(event, checked)
      onCheckedChange?.(checked, event)
    },
    [onChange, onCheckedChange]
  )

  return (
    <label
      className={classNames(
        "ApolloCheckbox-root",
        styles.checkbox,
        styles.checkboxLabelRoot,
        className
      )}
      ref={rootRef}
      data-disabled={inputProps?.disabled}
    >
      <BaseUICheckbox.Root
        id={id}
        inputRef={ref}
        ref={wrapperRef}
        className={classNames("ApolloCheckbox-checkbox", styles.checkboxRoot, {
          [styles.checkboxWithLabel]: !!label,
        })}
        indeterminate={indeterminate}
        {...inputProps}
        onCheckedChange={handleChange}
      >
        <BaseUICheckbox.Indicator className={styles.checkboxIndicator}>
          {indeterminate ? (
            <div className={styles.indeterminateIcon} />
          ) : (
            <div className={styles.checkIcon} />
          )}
        </BaseUICheckbox.Indicator>
      </BaseUICheckbox.Root>
      {label && (
        <Typography
          level="bodyLarge"
          className={classNames(
            "ApolloCheckbox-label",
            {
              [styles.checkboxLabelDisabled]: inputProps?.disabled && !inputProps?.activeLabel,
            },
            styles.label
          )}
        >
          {label}
        </Typography>
      )}
    </label>
  )
}
