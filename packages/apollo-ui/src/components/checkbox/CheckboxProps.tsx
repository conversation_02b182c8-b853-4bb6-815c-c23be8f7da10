import type { <PERSON>actN<PERSON>, Ref } from "react"
import { Checkbox as Base<PERSON><PERSON>heckbox } from "@base-ui-components/react/checkbox"

export type CheckboxProps = {
  label?: ReactNode
  activeLabel?: boolean
  ref?: Ref<HTMLInputElement>
  rootRef?: Ref<HTMLLabelElement>
  wrapperRef?: Ref<HTMLButtonElement>
  onChange?: (event: Event, checked: boolean) => void
} & BaseUICheckbox.Root.Props
