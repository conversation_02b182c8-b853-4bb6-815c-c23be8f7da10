import React, {
  cloneElement,
  forwardRef,
  isValidElement,
  ReactElement,
} from "react"
import { createPortal } from "react-dom"

import { BasePortalComponentProps, PortalProps } from "./PortalProps"
import { useThemeScope } from "../../hooks/useThemeScope"


export const Portal = forwardRef<HTMLElement, PortalProps & { baseComponent?: ReactElement }>(
  ({ children, container, baseComponent }, ref) => {
    const [scopeClass, placeholderRef] = useThemeScope<HTMLSpanElement>()

    const wrapperClassName = ["portal-theme", scopeClass]
      .filter(Boolean)
      .join(" ")
    const portalContent: React.ReactNode = (
      <div className={wrapperClassName} ref={ref as React.Ref<HTMLDivElement>}>
        {children}
      </div>
    )

    if (baseComponent && isValidElement(baseComponent)) {
      const element = baseComponent as ReactElement<BasePortalComponentProps>
      return (
        <React.Fragment>
          <span
            ref={placeholderRef}
            style={{ display: "none" }}
            aria-hidden="true"
          />
          {cloneElement(element, {
            children: portalContent,
          })}
        </React.Fragment>
      )
    }

    return (
      <React.Fragment>
        <span
          ref={placeholderRef}
          style={{ display: "none" }}
          aria-hidden="true"
        />
        {createPortal(portalContent, container || document.body)}
      </React.Fragment>
    )
  }
)
