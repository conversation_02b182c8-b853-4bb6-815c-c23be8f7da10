import type { HTMLAttributes, Ref } from "react"
import type { TextareaAutosizeProps as BaseTextareaAutosizeProps } from "@mui/base/TextareaAutosize"

import { FieldProps } from "../field"

export type BaseTextareaProps = {
  fullWidth?: boolean
  size?: "medium" | "small"
  error?: boolean
  rootRef?: Ref<HTMLDivElement>
  rootProps?: TextareaRootProps
  fieldProps?: FieldProps
  ref?: Ref<HTMLTextAreaElement>
} & Pick<FieldProps, "label" | "helperText" | "error" | "required" | "labelDecorator" | "helperTextDecorator" | "hasCharacterCount">

export type NormalTextareaProps = Omit<BaseTextareaAutosizeProps, "size"> &
  HTMLAttributes<HTMLTextAreaElement> & {
    rows?: number
  }

export type TextareaRootProps = HTMLAttributes<HTMLDivElement>

export type TextareaProps = NormalTextareaProps & BaseTextareaProps
