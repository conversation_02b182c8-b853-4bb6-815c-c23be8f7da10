.toastWrapper {
  position: fixed;
  z-index: 1;
  width: 100%;
  margin: 0 auto;
}

.toastWrapperTopCenter {
  top: 1rem;
  right: 0;
  left: 0;
  bottom: auto;
}

.toastWrapperTopRight {
  top: 1rem;
  right: 1rem;
  left: auto;
  bottom: auto;
}

.toastWrapperTopLeft {
  top: 1rem;
  right: auto;
  left: 1rem;
  bottom: auto;
}

.toastWrapperBottomCenter {
  top: auto;
  right: 0;
  left: 0;
  bottom: 1rem;
}

.toastWrapperBottomRight {
  top: auto;
  right: 1rem;
  left: auto;
  bottom: 1rem;

}

.toastWrapperBottomLeft {
  top: auto;
  right: auto;
  left: 1rem;
  bottom: 1rem;
}


.toastRoot {
  --gap: 0.75rem;
  --offset-y: calc(var(--toast-offset-y) + (var(--toast-index) * var(--gap)) + var(--toast-swipe-movement-y));
  position: absolute;
  min-width: fit-content;
  margin: 0 auto;
  -webkit-user-select: none;
  user-select: none;
  transition:
    transform 0.5s cubic-bezier(0.22, 1, 0.36, 1),
    opacity 0.5s;
  cursor: default;
  z-index: calc(1000 - var(--toast-index));

  &::after {
    bottom: 100%;
  }

  &[data-expanded] {
    transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--offset-y)));
  }

  &[data-starting-style],
  &[data-ending-style] {
    &[data-swipe-direction='up'] {
      transform: translateY(-150%);
    }

    &[data-swipe-direction='left'] {
      transform: translateX(-150%) translateY(var(--offset-y));
    }

    &[data-swipe-direction='right'] {
      transform: translateX(150%) translateY(var(--offset-y));
    }

    &[data-swipe-direction='down'] {
      transform: translateY(150%);
    }
  }


  &[data-limited] {
    opacity: 0;
  }

  &[data-ending-style] {
    opacity: 0;

    &[data-swipe-direction='up'] {
      transform: translateY(calc(var(--toast-swipe-movement-y) - 150%));
    }

    &[data-swipe-direction='left'] {
      transform: translateX(calc(var(--toast-swipe-movement-x) - 150%)) translateY(var(--offset-y));
    }

    &[data-swipe-direction='right'] {
      transform: translateX(calc(var(--toast-swipe-movement-x) + 150%)) translateY(var(--offset-y));
    }

    &[data-swipe-direction='down'] {
      transform: translateY(calc(var(--toast-swipe-movement-y) + 150%));
    }
  }

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    left: 0;
    height: calc(var(--gap) + 1px);
  }
}

.toastRootTopLeft {
  top: 0;
  left: 0;
  --offset-y: calc(var(--toast-offset-y) + (var(--toast-index) * var(--gap)) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * 20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(-150%);
  }
}

.toastRootTopCenter {
  top: 0;
  left: 0;
  right: 0;
  --offset-y: calc(var(--toast-offset-y) + (var(--toast-index) * var(--gap)) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * 20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(-150%);
  }
}

.toastRootTopRight {
  top: 0;
  right: 0;
  --offset-y: calc(var(--toast-offset-y) + (var(--toast-index) * var(--gap)) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * 20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(-150%);
  }
}

.toastRootBottomLeft {
  bottom: 0;
  left: 0;
  --offset-y: calc(var(--toast-offset-y) * -1 + (var(--toast-index) * var(--gap) * -1) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * -20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(150%);
  }
}

.toastRootBottomCenter {
  bottom: 0;
  left: 0;
  right: 0;
  --offset-y: calc(var(--toast-offset-y) * -1 + (var(--toast-index) * var(--gap) * -1) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * -20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(150%);
  }
}

.toastRootBottomRight {
  bottom: 0;
  right: 0;
  --offset-y: calc(var(--toast-offset-y) * -1 + (var(--toast-index) * var(--gap) * -1) + var(--toast-swipe-movement-y));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * -20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(150%);
  }
}

.toastRootDefaultWidth {
  width: fit-content;
}

.toastRootFullWidth {
  width: calc(100% - 2rem)
}