import { RefObject, useLayoutEffect, useRef, useState } from "react"

export function useThemeScope<T extends HTMLElement>(
  elementRef?: RefObject<T>
) {
  const internalRef = elementRef || useRef<T>(null)
  const [scopeClass, setScopeClass] = useState("")

  useLayoutEffect(() => {
    const element = internalRef.current
    if (!element) return

    const parentThemeEl = element.closest<HTMLElement>("[data-apl-theme]")

    const className = parentThemeEl?.getAttribute("data-apl-theme") || ""
    setScopeClass(className)
  }, [])

  return [scopeClass, internalRef] as const
}