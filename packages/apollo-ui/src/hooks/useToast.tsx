import { Toast as BaseToast } from "@base-ui-components/react"

import { ToastObjectProps } from "../components"
export const useToast = () => {
  const toastManager = BaseToast.useToastManager()

  const toasts = toastManager.toasts as ToastObjectProps[]

  const add = (
    options: Omit<ToastObjectProps, "id" | "animation" | "height"> & {
      id?: string
    }
  ) => {
    toastManager.add(options)
  }

  const update = (
    toastId: string,
    options: Omit<ToastObjectProps, "id" | "animation" | "height"> & {
      id?: string
    }
  ) => {
    toastManager.update(toastId, options)
  }

  return {
    toasts,
    close: toastManager.close,
    add,
    update,
  }
}
