import { ComponentType, PropsWithChildren, ReactNode } from "react"
import { ThemeProps } from "../../theme/types"
import { ToastProviderProps } from "../toast/ToastProviderType"

export type ApolloProviderProps = PropsWithChildren<{
  children: ReactNode
  themeProps?: ThemeProps
  toastProps?: ToastProviderProps
}>

export type ApolloProviderType = ComponentType<ApolloProviderProps>

export type ApolloProviderConfig = {
  component: ComponentType<PropsWithChildren<{}>>
  propName: string
}