import { PropsWithChildren } from "react"
import { Toast as BaseToast } from "@base-ui-components/react/toast"

import { ToastObjectProps } from "../../components"

export type ToastProviderProps = BaseToast.Provider.Props
export type ToastProviderType = PropsWithChildren<ToastProviderProps>

export type ToastContextType = {
  toasts: ToastObjectProps[]
  close: (toastId: string) => void
  add: (
    options: Omit<ToastObjectProps, "id" | "animation" | "height"> & {
      id?: string
    }
  ) => void
  update: (
    toastId: string,
    options: Omit<ToastObjectProps, "id" | "animation" | "height"> & {
      id?: string
    }
  ) => void
}
