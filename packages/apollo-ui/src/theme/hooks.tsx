import { useLayoutEffect } from "react"

export const useInjectStyles = (styleId: string, css: string): void => {
  useLayoutEffect(() => {
    // This effect creates the <style> tag on mount and removes it on unmount.
    // It only re-runs if the `styleId` changes.
    if (!css) return
    let styleElement = document.getElementById(styleId)
    if (!styleElement) {
      styleElement = document.createElement("style")
      styleElement.id = styleId
      styleElement.setAttribute("data-apl-theme", styleId)
      document.head.appendChild(styleElement)
    }

    if (styleElement.innerHTML !== css) {
      styleElement.innerHTML = css
    }

    return () => {
      const styleElement = document.getElementById(styleId)
      if (styleElement) {
        styleElement.remove()
      }
    }
  }, [styleId, css])
}