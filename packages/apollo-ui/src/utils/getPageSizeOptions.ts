export function getPageSizeOptions(
  start: number,
  step: number,
  total: number
): number[] {
  const options: number[] = []
  let previousPages: number | null = null
  for (let size = start; size <= total; size += step) {
    const pages = Math.ceil(total / size)
    if (!previousPages || previousPages != pages) {
      options.push(size)
      previousPages = pages
    }
  }
  // Ensure total count is included if not already
  if (options[options.length - 1] !== total) {
    options.push(total)
  }
  return options
}
