import * as react from 'react';
import { RefAttributes, SVGProps, SVGElementType, ComponentType, ForwardRefExoticComponent } from 'react';

type IconCollectionNode = [
    elementName: SVGElementType,
    attrs: Record<string, string>
][];
type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;
type ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;
interface IconCollectionProps extends ComponentAttributes {
    size?: string | number;
    absoluteStrokeWidth?: boolean;
}
type IconCollection = ForwardRefExoticComponent<IconCollectionProps>;
declare const createIcon: (iconName: string, iconNode: IconCollectionNode) => ComponentType<IconCollectionProps>;

declare const Alert: react.ComponentType<IconCollectionProps>;

declare const Appstore: react.ComponentType<IconCollectionProps>;

declare const AreaChart: react.ComponentType<IconCollectionProps>;

declare const ArrowDown: react.ComponentType<IconCollectionProps>;

declare const ArrowLeft: react.ComponentType<IconCollectionProps>;

declare const ArrowRight: react.ComponentType<IconCollectionProps>;

declare const ArrowUp: react.ComponentType<IconCollectionProps>;

declare const ArrowsAlt: react.ComponentType<IconCollectionProps>;

declare const Audio: react.ComponentType<IconCollectionProps>;

declare const AudioMuted: react.ComponentType<IconCollectionProps>;

declare const Audit: react.ComponentType<IconCollectionProps>;

declare const Bank: react.ComponentType<IconCollectionProps>;

declare const BarChart: react.ComponentType<IconCollectionProps>;

declare const Barcode: react.ComponentType<IconCollectionProps>;

declare const Bars: react.ComponentType<IconCollectionProps>;

declare const Bell: react.ComponentType<IconCollectionProps>;

declare const Bulb: react.ComponentType<IconCollectionProps>;

declare const Calendar: react.ComponentType<IconCollectionProps>;

declare const Camera: react.ComponentType<IconCollectionProps>;

declare const Car: react.ComponentType<IconCollectionProps>;

declare const CaretDown: react.ComponentType<IconCollectionProps>;

declare const CaretLeft: react.ComponentType<IconCollectionProps>;

declare const CaretRight: react.ComponentType<IconCollectionProps>;

declare const CaretUp: react.ComponentType<IconCollectionProps>;

declare const CarryOut: react.ComponentType<IconCollectionProps>;

declare const Check: react.ComponentType<IconCollectionProps>;

declare const CheckCircle: react.ComponentType<IconCollectionProps>;

declare const Clear: react.ComponentType<IconCollectionProps>;

declare const ClockCircle: react.ComponentType<IconCollectionProps>;

declare const Close: react.ComponentType<IconCollectionProps>;

declare const CloseCircle: react.ComponentType<IconCollectionProps>;

declare const CloudDownload: react.ComponentType<IconCollectionProps>;

declare const CloudSync: react.ComponentType<IconCollectionProps>;

declare const CloudUpload: react.ComponentType<IconCollectionProps>;

declare const Comment: react.ComponentType<IconCollectionProps>;

declare const Compress: react.ComponentType<IconCollectionProps>;

declare const Contacts: react.ComponentType<IconCollectionProps>;

declare const Container: react.ComponentType<IconCollectionProps>;

declare const Copy: react.ComponentType<IconCollectionProps>;

declare const Count: react.ComponentType<IconCollectionProps>;

declare const CreditCard: react.ComponentType<IconCollectionProps>;

declare const Dash: react.ComponentType<IconCollectionProps>;

declare const Dashboard: react.ComponentType<IconCollectionProps>;

declare const DeleteOutlined: react.ComponentType<IconCollectionProps>;

declare const Diff: react.ComponentType<IconCollectionProps>;

declare const Disconnect: react.ComponentType<IconCollectionProps>;

declare const DotChart: react.ComponentType<IconCollectionProps>;

declare const DoubleLeft: react.ComponentType<IconCollectionProps>;

declare const DoubleRight: react.ComponentType<IconCollectionProps>;

declare const Down: react.ComponentType<IconCollectionProps>;

declare const DownCircle: react.ComponentType<IconCollectionProps>;

declare const Download: react.ComponentType<IconCollectionProps>;

declare const Edit: react.ComponentType<IconCollectionProps>;

declare const Ellipsis: react.ComponentType<IconCollectionProps>;

declare const Environment: react.ComponentType<IconCollectionProps>;

declare const Exception: react.ComponentType<IconCollectionProps>;

declare const Exclamation: react.ComponentType<IconCollectionProps>;

declare const ExclamationCircle: react.ComponentType<IconCollectionProps>;

declare const Expand: react.ComponentType<IconCollectionProps>;

declare const ExpandAlt: react.ComponentType<IconCollectionProps>;

declare const Export: react.ComponentType<IconCollectionProps>;

declare const Eye: react.ComponentType<IconCollectionProps>;

declare const EyeInvisible: react.ComponentType<IconCollectionProps>;

declare const File: react.ComponentType<IconCollectionProps>;

declare const FileAdd: react.ComponentType<IconCollectionProps>;

declare const FileDone: react.ComponentType<IconCollectionProps>;

declare const FileDownload: react.ComponentType<IconCollectionProps>;

declare const FileExcel: react.ComponentType<IconCollectionProps>;

declare const FileExclamation: react.ComponentType<IconCollectionProps>;

declare const FileImage: react.ComponentType<IconCollectionProps>;

declare const FileSearch: react.ComponentType<IconCollectionProps>;

declare const FileSync: react.ComponentType<IconCollectionProps>;

declare const FileText: react.ComponentType<IconCollectionProps>;

declare const Filter: react.ComponentType<IconCollectionProps>;

declare const Fire: react.ComponentType<IconCollectionProps>;

declare const Flag: react.ComponentType<IconCollectionProps>;

declare const Folder: react.ComponentType<IconCollectionProps>;

declare const FolderAdd: react.ComponentType<IconCollectionProps>;

declare const FolderOpen: react.ComponentType<IconCollectionProps>;

declare const Form: react.ComponentType<IconCollectionProps>;

declare const Fullscreen: react.ComponentType<IconCollectionProps>;

declare const FullscreenExit: react.ComponentType<IconCollectionProps>;

declare const Fund: react.ComponentType<IconCollectionProps>;

declare const FundView: react.ComponentType<IconCollectionProps>;

declare const Gift: react.ComponentType<IconCollectionProps>;

declare const Heart: react.ComponentType<IconCollectionProps>;

declare const History: react.ComponentType<IconCollectionProps>;

declare const Home: react.ComponentType<IconCollectionProps>;

declare const Hourglass: react.ComponentType<IconCollectionProps>;

declare const Idcard: react.ComponentType<IconCollectionProps>;

declare const Import: react.ComponentType<IconCollectionProps>;

declare const Inbox: react.ComponentType<IconCollectionProps>;

declare const Info: react.ComponentType<IconCollectionProps>;

declare const InfoCircle: react.ComponentType<IconCollectionProps>;

declare const Left: react.ComponentType<IconCollectionProps>;

declare const LeftCircle: react.ComponentType<IconCollectionProps>;

declare const Like: react.ComponentType<IconCollectionProps>;

declare const LineChart: react.ComponentType<IconCollectionProps>;

declare const Link: react.ComponentType<IconCollectionProps>;

declare const Loading: react.ComponentType<IconCollectionProps>;

declare const Loading3Quarters: react.ComponentType<IconCollectionProps>;

declare const Lock: react.ComponentType<IconCollectionProps>;

declare const Login: react.ComponentType<IconCollectionProps>;

declare const Logout: react.ComponentType<IconCollectionProps>;

declare const Mail: react.ComponentType<IconCollectionProps>;

declare const Menu: react.ComponentType<IconCollectionProps>;

declare const Message: react.ComponentType<IconCollectionProps>;

declare const MinusCircle: react.ComponentType<IconCollectionProps>;

declare const MinusSquare: react.ComponentType<IconCollectionProps>;

declare const Mobile: react.ComponentType<IconCollectionProps>;

declare const More: react.ComponentType<IconCollectionProps>;

declare const Notification: react.ComponentType<IconCollectionProps>;

declare const PaperClip: react.ComponentType<IconCollectionProps>;

declare const Phone: react.ComponentType<IconCollectionProps>;

declare const Picture: react.ComponentType<IconCollectionProps>;

declare const PieChart: react.ComponentType<IconCollectionProps>;

declare const Plus: react.ComponentType<IconCollectionProps>;

declare const PlusCircle: react.ComponentType<IconCollectionProps>;

declare const Poweroff: react.ComponentType<IconCollectionProps>;

declare const Profile2: react.ComponentType<IconCollectionProps>;

declare const Pushpin: react.ComponentType<IconCollectionProps>;

declare const Qrcode: react.ComponentType<IconCollectionProps>;

declare const Question: react.ComponentType<IconCollectionProps>;

declare const QuestionCircle: react.ComponentType<IconCollectionProps>;

declare const Read: react.ComponentType<IconCollectionProps>;

declare const Receive: react.ComponentType<IconCollectionProps>;

declare const Redo: react.ComponentType<IconCollectionProps>;

declare const Rest: react.ComponentType<IconCollectionProps>;

declare const Retweet: react.ComponentType<IconCollectionProps>;

declare const Right: react.ComponentType<IconCollectionProps>;

declare const RightCircle: react.ComponentType<IconCollectionProps>;

declare const RotateLeft: react.ComponentType<IconCollectionProps>;

declare const RotateRight: react.ComponentType<IconCollectionProps>;

declare const Safety: react.ComponentType<IconCollectionProps>;

declare const Save: react.ComponentType<IconCollectionProps>;

declare const Scan: react.ComponentType<IconCollectionProps>;

declare const Schedule: react.ComponentType<IconCollectionProps>;

declare const Scissor: react.ComponentType<IconCollectionProps>;

declare const Search: react.ComponentType<IconCollectionProps>;

declare const Send: react.ComponentType<IconCollectionProps>;

declare const Setting: react.ComponentType<IconCollectionProps>;

declare const Shake: react.ComponentType<IconCollectionProps>;

declare const Shop: react.ComponentType<IconCollectionProps>;

declare const Shopping: react.ComponentType<IconCollectionProps>;

declare const ShoppingCart: react.ComponentType<IconCollectionProps>;

declare const Shrink: react.ComponentType<IconCollectionProps>;

declare const SmallDash: react.ComponentType<IconCollectionProps>;

declare const Smile: react.ComponentType<IconCollectionProps>;

declare const Snippets: react.ComponentType<IconCollectionProps>;

declare const Solution: react.ComponentType<IconCollectionProps>;

declare const SortAscending: react.ComponentType<IconCollectionProps>;

declare const SortDescending: react.ComponentType<IconCollectionProps>;

declare const Sound: react.ComponentType<IconCollectionProps>;

declare const Star: react.ComponentType<IconCollectionProps>;

declare const Stop: react.ComponentType<IconCollectionProps>;

declare const Sync: react.ComponentType<IconCollectionProps>;

declare const Tag: react.ComponentType<IconCollectionProps>;

declare const Tags: react.ComponentType<IconCollectionProps>;

declare const Team: react.ComponentType<IconCollectionProps>;

declare const ToTop: react.ComponentType<IconCollectionProps>;

declare const Tool: react.ComponentType<IconCollectionProps>;

declare const Transfer: react.ComponentType<IconCollectionProps>;

declare const Undo: react.ComponentType<IconCollectionProps>;

declare const Unlock: react.ComponentType<IconCollectionProps>;

declare const Up: react.ComponentType<IconCollectionProps>;

declare const UpCircle: react.ComponentType<IconCollectionProps>;

declare const Upload: react.ComponentType<IconCollectionProps>;

declare const User: react.ComponentType<IconCollectionProps>;

declare const UserAdd: react.ComponentType<IconCollectionProps>;

declare const UserDelete: react.ComponentType<IconCollectionProps>;

declare const UserSwitch: react.ComponentType<IconCollectionProps>;

declare const VideoCamera: react.ComponentType<IconCollectionProps>;

declare const VideoCameraAdd: react.ComponentType<IconCollectionProps>;

declare const Wallet: react.ComponentType<IconCollectionProps>;

declare const Warning: react.ComponentType<IconCollectionProps>;

declare const Wifi: react.ComponentType<IconCollectionProps>;

declare const ZoomIn: react.ComponentType<IconCollectionProps>;

declare const ZoomOut: react.ComponentType<IconCollectionProps>;

declare const index_Alert: typeof Alert;
declare const index_Appstore: typeof Appstore;
declare const index_AreaChart: typeof AreaChart;
declare const index_ArrowDown: typeof ArrowDown;
declare const index_ArrowLeft: typeof ArrowLeft;
declare const index_ArrowRight: typeof ArrowRight;
declare const index_ArrowUp: typeof ArrowUp;
declare const index_ArrowsAlt: typeof ArrowsAlt;
declare const index_Audio: typeof Audio;
declare const index_AudioMuted: typeof AudioMuted;
declare const index_Audit: typeof Audit;
declare const index_Bank: typeof Bank;
declare const index_BarChart: typeof BarChart;
declare const index_Barcode: typeof Barcode;
declare const index_Bars: typeof Bars;
declare const index_Bell: typeof Bell;
declare const index_Bulb: typeof Bulb;
declare const index_Calendar: typeof Calendar;
declare const index_Camera: typeof Camera;
declare const index_Car: typeof Car;
declare const index_CaretDown: typeof CaretDown;
declare const index_CaretLeft: typeof CaretLeft;
declare const index_CaretRight: typeof CaretRight;
declare const index_CaretUp: typeof CaretUp;
declare const index_CarryOut: typeof CarryOut;
declare const index_Check: typeof Check;
declare const index_CheckCircle: typeof CheckCircle;
declare const index_Clear: typeof Clear;
declare const index_ClockCircle: typeof ClockCircle;
declare const index_Close: typeof Close;
declare const index_CloseCircle: typeof CloseCircle;
declare const index_CloudDownload: typeof CloudDownload;
declare const index_CloudSync: typeof CloudSync;
declare const index_CloudUpload: typeof CloudUpload;
declare const index_Comment: typeof Comment;
declare const index_Compress: typeof Compress;
declare const index_Contacts: typeof Contacts;
declare const index_Container: typeof Container;
declare const index_Copy: typeof Copy;
declare const index_Count: typeof Count;
declare const index_CreditCard: typeof CreditCard;
declare const index_Dash: typeof Dash;
declare const index_Dashboard: typeof Dashboard;
declare const index_DeleteOutlined: typeof DeleteOutlined;
declare const index_Diff: typeof Diff;
declare const index_Disconnect: typeof Disconnect;
declare const index_DotChart: typeof DotChart;
declare const index_DoubleLeft: typeof DoubleLeft;
declare const index_DoubleRight: typeof DoubleRight;
declare const index_Down: typeof Down;
declare const index_DownCircle: typeof DownCircle;
declare const index_Download: typeof Download;
declare const index_Edit: typeof Edit;
declare const index_Ellipsis: typeof Ellipsis;
declare const index_Environment: typeof Environment;
declare const index_Exception: typeof Exception;
declare const index_Exclamation: typeof Exclamation;
declare const index_ExclamationCircle: typeof ExclamationCircle;
declare const index_Expand: typeof Expand;
declare const index_ExpandAlt: typeof ExpandAlt;
declare const index_Export: typeof Export;
declare const index_Eye: typeof Eye;
declare const index_EyeInvisible: typeof EyeInvisible;
declare const index_File: typeof File;
declare const index_FileAdd: typeof FileAdd;
declare const index_FileDone: typeof FileDone;
declare const index_FileDownload: typeof FileDownload;
declare const index_FileExcel: typeof FileExcel;
declare const index_FileExclamation: typeof FileExclamation;
declare const index_FileImage: typeof FileImage;
declare const index_FileSearch: typeof FileSearch;
declare const index_FileSync: typeof FileSync;
declare const index_FileText: typeof FileText;
declare const index_Filter: typeof Filter;
declare const index_Fire: typeof Fire;
declare const index_Flag: typeof Flag;
declare const index_Folder: typeof Folder;
declare const index_FolderAdd: typeof FolderAdd;
declare const index_FolderOpen: typeof FolderOpen;
declare const index_Form: typeof Form;
declare const index_Fullscreen: typeof Fullscreen;
declare const index_FullscreenExit: typeof FullscreenExit;
declare const index_Fund: typeof Fund;
declare const index_FundView: typeof FundView;
declare const index_Gift: typeof Gift;
declare const index_Heart: typeof Heart;
declare const index_History: typeof History;
declare const index_Home: typeof Home;
declare const index_Hourglass: typeof Hourglass;
declare const index_Idcard: typeof Idcard;
declare const index_Import: typeof Import;
declare const index_Inbox: typeof Inbox;
declare const index_Info: typeof Info;
declare const index_InfoCircle: typeof InfoCircle;
declare const index_Left: typeof Left;
declare const index_LeftCircle: typeof LeftCircle;
declare const index_Like: typeof Like;
declare const index_LineChart: typeof LineChart;
declare const index_Link: typeof Link;
declare const index_Loading: typeof Loading;
declare const index_Loading3Quarters: typeof Loading3Quarters;
declare const index_Lock: typeof Lock;
declare const index_Login: typeof Login;
declare const index_Logout: typeof Logout;
declare const index_Mail: typeof Mail;
declare const index_Menu: typeof Menu;
declare const index_Message: typeof Message;
declare const index_MinusCircle: typeof MinusCircle;
declare const index_MinusSquare: typeof MinusSquare;
declare const index_Mobile: typeof Mobile;
declare const index_More: typeof More;
declare const index_Notification: typeof Notification;
declare const index_PaperClip: typeof PaperClip;
declare const index_Phone: typeof Phone;
declare const index_Picture: typeof Picture;
declare const index_PieChart: typeof PieChart;
declare const index_Plus: typeof Plus;
declare const index_PlusCircle: typeof PlusCircle;
declare const index_Poweroff: typeof Poweroff;
declare const index_Profile2: typeof Profile2;
declare const index_Pushpin: typeof Pushpin;
declare const index_Qrcode: typeof Qrcode;
declare const index_Question: typeof Question;
declare const index_QuestionCircle: typeof QuestionCircle;
declare const index_Read: typeof Read;
declare const index_Receive: typeof Receive;
declare const index_Redo: typeof Redo;
declare const index_Rest: typeof Rest;
declare const index_Retweet: typeof Retweet;
declare const index_Right: typeof Right;
declare const index_RightCircle: typeof RightCircle;
declare const index_RotateLeft: typeof RotateLeft;
declare const index_RotateRight: typeof RotateRight;
declare const index_Safety: typeof Safety;
declare const index_Save: typeof Save;
declare const index_Scan: typeof Scan;
declare const index_Schedule: typeof Schedule;
declare const index_Scissor: typeof Scissor;
declare const index_Search: typeof Search;
declare const index_Send: typeof Send;
declare const index_Setting: typeof Setting;
declare const index_Shake: typeof Shake;
declare const index_Shop: typeof Shop;
declare const index_Shopping: typeof Shopping;
declare const index_ShoppingCart: typeof ShoppingCart;
declare const index_Shrink: typeof Shrink;
declare const index_SmallDash: typeof SmallDash;
declare const index_Smile: typeof Smile;
declare const index_Snippets: typeof Snippets;
declare const index_Solution: typeof Solution;
declare const index_SortAscending: typeof SortAscending;
declare const index_SortDescending: typeof SortDescending;
declare const index_Sound: typeof Sound;
declare const index_Star: typeof Star;
declare const index_Stop: typeof Stop;
declare const index_Sync: typeof Sync;
declare const index_Tag: typeof Tag;
declare const index_Tags: typeof Tags;
declare const index_Team: typeof Team;
declare const index_ToTop: typeof ToTop;
declare const index_Tool: typeof Tool;
declare const index_Transfer: typeof Transfer;
declare const index_Undo: typeof Undo;
declare const index_Unlock: typeof Unlock;
declare const index_Up: typeof Up;
declare const index_UpCircle: typeof UpCircle;
declare const index_Upload: typeof Upload;
declare const index_User: typeof User;
declare const index_UserAdd: typeof UserAdd;
declare const index_UserDelete: typeof UserDelete;
declare const index_UserSwitch: typeof UserSwitch;
declare const index_VideoCamera: typeof VideoCamera;
declare const index_VideoCameraAdd: typeof VideoCameraAdd;
declare const index_Wallet: typeof Wallet;
declare const index_Warning: typeof Warning;
declare const index_Wifi: typeof Wifi;
declare const index_ZoomIn: typeof ZoomIn;
declare const index_ZoomOut: typeof ZoomOut;
declare namespace index {
  export { index_Alert as Alert, index_Appstore as Appstore, index_AreaChart as AreaChart, index_ArrowDown as ArrowDown, index_ArrowLeft as ArrowLeft, index_ArrowRight as ArrowRight, index_ArrowUp as ArrowUp, index_ArrowsAlt as ArrowsAlt, index_Audio as Audio, index_AudioMuted as AudioMuted, index_Audit as Audit, index_Bank as Bank, index_BarChart as BarChart, index_Barcode as Barcode, index_Bars as Bars, index_Bell as Bell, index_Bulb as Bulb, index_Calendar as Calendar, index_Camera as Camera, index_Car as Car, index_CaretDown as CaretDown, index_CaretLeft as CaretLeft, index_CaretRight as CaretRight, index_CaretUp as CaretUp, index_CarryOut as CarryOut, index_Check as Check, index_CheckCircle as CheckCircle, index_Clear as Clear, index_ClockCircle as ClockCircle, index_Close as Close, index_CloseCircle as CloseCircle, index_CloudDownload as CloudDownload, index_CloudSync as CloudSync, index_CloudUpload as CloudUpload, index_Comment as Comment, index_Compress as Compress, index_Contacts as Contacts, index_Container as Container, index_Copy as Copy, index_Count as Count, index_CreditCard as CreditCard, index_Dash as Dash, index_Dashboard as Dashboard, index_DeleteOutlined as DeleteOutlined, index_Diff as Diff, index_Disconnect as Disconnect, index_DotChart as DotChart, index_DoubleLeft as DoubleLeft, index_DoubleRight as DoubleRight, index_Down as Down, index_DownCircle as DownCircle, index_Download as Download, index_Edit as Edit, index_Ellipsis as Ellipsis, index_Environment as Environment, index_Exception as Exception, index_Exclamation as Exclamation, index_ExclamationCircle as ExclamationCircle, index_Expand as Expand, index_ExpandAlt as ExpandAlt, index_Export as Export, index_Eye as Eye, index_EyeInvisible as EyeInvisible, index_File as File, index_FileAdd as FileAdd, index_FileDone as FileDone, index_FileDownload as FileDownload, index_FileExcel as FileExcel, index_FileExclamation as FileExclamation, index_FileImage as FileImage, index_FileSearch as FileSearch, index_FileSync as FileSync, index_FileText as FileText, index_Filter as Filter, index_Fire as Fire, index_Flag as Flag, index_Folder as Folder, index_FolderAdd as FolderAdd, index_FolderOpen as FolderOpen, index_Form as Form, index_Fullscreen as Fullscreen, index_FullscreenExit as FullscreenExit, index_Fund as Fund, index_FundView as FundView, index_Gift as Gift, index_Heart as Heart, index_History as History, index_Home as Home, index_Hourglass as Hourglass, index_Idcard as Idcard, index_Import as Import, index_Inbox as Inbox, index_Info as Info, index_InfoCircle as InfoCircle, index_Left as Left, index_LeftCircle as LeftCircle, index_Like as Like, index_LineChart as LineChart, index_Link as Link, index_Loading as Loading, index_Loading3Quarters as Loading3Quarters, index_Lock as Lock, index_Login as Login, index_Logout as Logout, index_Mail as Mail, index_Menu as Menu, index_Message as Message, index_MinusCircle as MinusCircle, index_MinusSquare as MinusSquare, index_Mobile as Mobile, index_More as More, index_Notification as Notification, index_PaperClip as PaperClip, index_Phone as Phone, index_Picture as Picture, index_PieChart as PieChart, index_Plus as Plus, index_PlusCircle as PlusCircle, index_Poweroff as Poweroff, index_Profile2 as Profile2, index_Pushpin as Pushpin, index_Qrcode as Qrcode, index_Question as Question, index_QuestionCircle as QuestionCircle, index_Read as Read, index_Receive as Receive, index_Redo as Redo, index_Rest as Rest, index_Retweet as Retweet, index_Right as Right, index_RightCircle as RightCircle, index_RotateLeft as RotateLeft, index_RotateRight as RotateRight, index_Safety as Safety, index_Save as Save, index_Scan as Scan, index_Schedule as Schedule, index_Scissor as Scissor, index_Search as Search, index_Send as Send, index_Setting as Setting, index_Shake as Shake, index_Shop as Shop, index_Shopping as Shopping, index_ShoppingCart as ShoppingCart, index_Shrink as Shrink, index_SmallDash as SmallDash, index_Smile as Smile, index_Snippets as Snippets, index_Solution as Solution, index_SortAscending as SortAscending, index_SortDescending as SortDescending, index_Sound as Sound, index_Star as Star, index_Stop as Stop, index_Sync as Sync, index_Tag as Tag, index_Tags as Tags, index_Team as Team, index_ToTop as ToTop, index_Tool as Tool, index_Transfer as Transfer, index_Undo as Undo, index_Unlock as Unlock, index_Up as Up, index_UpCircle as UpCircle, index_Upload as Upload, index_User as User, index_UserAdd as UserAdd, index_UserDelete as UserDelete, index_UserSwitch as UserSwitch, index_VideoCamera as VideoCamera, index_VideoCameraAdd as VideoCameraAdd, index_Wallet as Wallet, index_Warning as Warning, index_Wifi as Wifi, index_ZoomIn as ZoomIn, index_ZoomOut as ZoomOut };
}

export { Alert, Appstore, AreaChart, ArrowDown, ArrowLeft, ArrowRight, ArrowUp, ArrowsAlt, Audio, AudioMuted, Audit, Bank, BarChart, Barcode, Bars, Bell, Bulb, Calendar, Camera, Car, CaretDown, CaretLeft, CaretRight, CaretUp, CarryOut, Check, CheckCircle, Clear, ClockCircle, Close, CloseCircle, CloudDownload, CloudSync, CloudUpload, Comment, Compress, Contacts, Container, Copy, Count, CreditCard, Dash, Dashboard, DeleteOutlined, Diff, Disconnect, DotChart, DoubleLeft, DoubleRight, Down, DownCircle, Download, Edit, Ellipsis, Environment, Exception, Exclamation, ExclamationCircle, Expand, ExpandAlt, Export, Eye, EyeInvisible, File, FileAdd, FileDone, FileDownload, FileExcel, FileExclamation, FileImage, FileSearch, FileSync, FileText, Filter, Fire, Flag, Folder, FolderAdd, FolderOpen, Form, Fullscreen, FullscreenExit, Fund, FundView, Gift, Heart, History, Home, Hourglass, type IconCollection, type IconCollectionNode, type IconCollectionProps, Idcard, Import, Inbox, Info, InfoCircle, Left, LeftCircle, Like, LineChart, Link, Loading, Loading3Quarters, Lock, Login, Logout, Mail, Menu, Message, MinusCircle, MinusSquare, Mobile, More, Notification, PaperClip, Phone, Picture, PieChart, Plus, PlusCircle, Poweroff, Profile2, Pushpin, Qrcode, Question, QuestionCircle, Read, Receive, Redo, Rest, Retweet, Right, RightCircle, RotateLeft, RotateRight, Safety, Save, Scan, Schedule, Scissor, Search, Send, Setting, Shake, Shop, Shopping, ShoppingCart, Shrink, SmallDash, Smile, Snippets, Solution, SortAscending, SortDescending, Sound, Star, Stop, Sync, Tag, Tags, Team, ToTop, Tool, Transfer, Undo, Unlock, Up, UpCircle, Upload, User, UserAdd, UserDelete, UserSwitch, VideoCamera, VideoCameraAdd, Wallet, Warning, Wifi, ZoomIn, ZoomOut, createIcon, index as icons };
