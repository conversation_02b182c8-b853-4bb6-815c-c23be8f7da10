declare const apolloTheme: {
    tokens: {
        "cjx-typography-caption-font-size": string;
        "cjx-typography-caption-font-family": string;
        "cjx-typography-body2-font-size": string;
        "cjx-typography-body2-font-weight": string;
        "cjx-typography-body2-font-family": string;
        "cjx-typography-body1-font-size": string;
        "cjx-typography-body1-font-weight": string;
        "cjx-typography-body1-font-family": string;
        "cjx-typography-h5-font-size": string;
        "cjx-typography-h5-font-weight": string;
        "cjx-typography-h5-font-family": string;
        "cjx-typography-h4-font-size": string;
        "cjx-typography-h4-font-weight": string;
        "cjx-typography-h4-font-family": string;
        "cjx-typography-h3-font-size": string;
        "cjx-typography-h3-font-weight": string;
        "cjx-typography-h3-font-family": string;
        "cjx-typography-h2-font-size": string;
        "cjx-typography-h2-font-weight": string;
        "cjx-typography-h2-font-family": string;
        "cjx-typography-h1-font-size": string;
        "cjx-typography-h1-font-weight": string;
        "cjx-typography-h1-font-family": string;
    };
    colors: {
        "cjx-colors-border-focus": string;
        "cjx-colors-border-danger-accented": string;
        "cjx-colors-border-danger-subdued": string;
        "cjx-colors-border-danger-default": string;
        "cjx-colors-border-warning-accented": string;
        "cjx-colors-border-warning-subdued": string;
        "cjx-colors-border-warning-default": string;
        "cjx-colors-border-success-accented": string;
        "cjx-colors-border-success-subdued": string;
        "cjx-colors-border-success-default": string;
        "cjx-colors-border-process-accented": string;
        "cjx-colors-border-process-subdued": string;
        "cjx-colors-border-process-default": string;
        "cjx-colors-border-primary-accented": string;
        "cjx-colors-border-primary-subdued": string;
        "cjx-colors-border-primary-default": string;
        "cjx-colors-border-inversed": string;
        "cjx-colors-border-accented": string;
        "cjx-colors-border-subdued": string;
        "cjx-colors-border-disabled": string;
        "cjx-colors-border-default": string;
        "cjx-colors-content-subdued": string;
        "cjx-colors-content-danger-subdued": string;
        "cjx-colors-content-danger-default": string;
        "cjx-colors-content-warning-subdued": string;
        "cjx-colors-content-warning-default": string;
        "cjx-colors-content-success-subdued": string;
        "cjx-colors-content-success-default": string;
        "cjx-colors-content-process-subdued": string;
        "cjx-colors-content-process-default": string;
        "cjx-colors-content-primary-subdued": string;
        "cjx-colors-content-primary-default": string;
        "cjx-colors-content-onactioninversed": string;
        "cjx-colors-content-onaction": string;
        "cjx-colors-content-placeholder": string;
        "cjx-colors-content-description": string;
        "cjx-colors-content-disabled": string;
        "cjx-colors-content-inversed": string;
        "cjx-colors-content-default": string;
        "cjx-colors-surface-action-secondary": string;
        "cjx-colors-surface-action-delete-disabled": string;
        "cjx-colors-surface-action-delete-active": string;
        "cjx-colors-surface-action-delete-hover": string;
        "cjx-colors-surface-action-delete-default": string;
        "cjx-colors-surface-action-primary-disabled": string;
        "cjx-colors-surface-action-primary-active": string;
        "cjx-colors-surface-action-primary-hover": string;
        "cjx-colors-surface-action-primary-default": string;
        "cjx-colors-surface-static-default5": string;
        "cjx-colors-surface-static-default4": string;
        "cjx-colors-surface-static-danger-disabled": string;
        "cjx-colors-surface-static-danger-active": string;
        "cjx-colors-surface-static-danger-hover": string;
        "cjx-colors-surface-static-danger-default": string;
        "cjx-colors-surface-static-warning-disabled": string;
        "cjx-colors-surface-static-warning-active": string;
        "cjx-colors-surface-static-warning-hover": string;
        "cjx-colors-surface-static-warning-default": string;
        "cjx-colors-surface-static-success-disabled": string;
        "cjx-colors-surface-static-success-active": string;
        "cjx-colors-surface-static-success-hover": string;
        "cjx-colors-surface-static-success-default": string;
        "cjx-colors-surface-static-process-disabled": string;
        "cjx-colors-surface-static-process-active": string;
        "cjx-colors-surface-static-process-hover": string;
        "cjx-colors-surface-static-process-default": string;
        "cjx-colors-surface-static-default3": string;
        "cjx-colors-surface-static-default2": string;
        "cjx-colors-surface-static-default1": string;
        "cjx-colors-surface-static-ui-delete": string;
        "cjx-colors-surface-static-ui-primary": string;
        "cjx-colors-surface-static-ui-disabled": string;
        "cjx-colors-surface-static-ui-active": string;
        "cjx-colors-surface-static-ui-hover": string;
        "cjx-colors-surface-static-ui-default": string;
        "cjx-colors-process-95": string;
        "cjx-colors-process-90": string;
        "cjx-colors-process-80": string;
        "cjx-colors-process-70": string;
        "cjx-colors-process-60": string;
        "cjx-colors-process-50": string;
        "cjx-colors-process-40": string;
        "cjx-colors-process-30": string;
        "cjx-colors-process-20": string;
        "cjx-colors-process-10": string;
        "cjx-colors-success-95": string;
        "cjx-colors-success-90": string;
        "cjx-colors-success-80": string;
        "cjx-colors-success-70": string;
        "cjx-colors-success-60": string;
        "cjx-colors-success-50": string;
        "cjx-colors-success-40": string;
        "cjx-colors-success-30": string;
        "cjx-colors-success-20": string;
        "cjx-colors-success-10": string;
        "cjx-colors-warning-95": string;
        "cjx-colors-warning-90": string;
        "cjx-colors-warning-80": string;
        "cjx-colors-warning-70": string;
        "cjx-colors-warning-60": string;
        "cjx-colors-warning-50": string;
        "cjx-colors-warning-40": string;
        "cjx-colors-warning-30": string;
        "cjx-colors-warning-20": string;
        "cjx-colors-warning-10": string;
        "cjx-colors-danger-95": string;
        "cjx-colors-danger-90": string;
        "cjx-colors-danger-80": string;
        "cjx-colors-danger-70": string;
        "cjx-colors-danger-60": string;
        "cjx-colors-danger-50": string;
        "cjx-colors-danger-40": string;
        "cjx-colors-danger-30": string;
        "cjx-colors-danger-20": string;
        "cjx-colors-danger-10": string;
        "cjx-colors-gray-100": string;
        "cjx-colors-gray-90": string;
        "cjx-colors-gray-80": string;
        "cjx-colors-gray-70": string;
        "cjx-colors-gray-60": string;
        "cjx-colors-gray-50": string;
        "cjx-colors-gray-40": string;
        "cjx-colors-gray-30": string;
        "cjx-colors-gray-20": string;
        "cjx-colors-gray-10": string;
        "cjx-colors-gray-5": string;
        "cjx-colors-gray-0": string;
        "cjx-colors-brand-95": string;
        "cjx-colors-brand-90": string;
        "cjx-colors-brand-80": string;
        "cjx-colors-brand-70": string;
        "cjx-colors-brand-60": string;
        "cjx-colors-brand-50": string;
        "cjx-colors-brand-40": string;
        "cjx-colors-brand-30": string;
        "cjx-colors-brand-20": string;
        "cjx-colors-brand-10": string;
    };
    lightColors: {
        "cjx-colors-border-focus": string;
        "cjx-colors-border-danger-accented": string;
        "cjx-colors-border-danger-subdued": string;
        "cjx-colors-border-danger-default": string;
        "cjx-colors-border-warning-accented": string;
        "cjx-colors-border-warning-subdued": string;
        "cjx-colors-border-warning-default": string;
        "cjx-colors-border-success-accented": string;
        "cjx-colors-border-success-subdued": string;
        "cjx-colors-border-success-default": string;
        "cjx-colors-border-process-accented": string;
        "cjx-colors-border-process-subdued": string;
        "cjx-colors-border-process-default": string;
        "cjx-colors-border-primary-accented": string;
        "cjx-colors-border-primary-subdued": string;
        "cjx-colors-border-primary-default": string;
        "cjx-colors-border-inversed": string;
        "cjx-colors-border-accented": string;
        "cjx-colors-border-subdued": string;
        "cjx-colors-border-disabled": string;
        "cjx-colors-border-default": string;
        "cjx-colors-content-subdued": string;
        "cjx-colors-content-danger-subdued": string;
        "cjx-colors-content-danger-default": string;
        "cjx-colors-content-warning-subdued": string;
        "cjx-colors-content-warning-default": string;
        "cjx-colors-content-success-subdued": string;
        "cjx-colors-content-success-default": string;
        "cjx-colors-content-process-subdued": string;
        "cjx-colors-content-process-default": string;
        "cjx-colors-content-primary-subdued": string;
        "cjx-colors-content-primary-default": string;
        "cjx-colors-content-onactioninversed": string;
        "cjx-colors-content-onaction": string;
        "cjx-colors-content-placeholder": string;
        "cjx-colors-content-description": string;
        "cjx-colors-content-disabled": string;
        "cjx-colors-content-inversed": string;
        "cjx-colors-content-default": string;
        "cjx-colors-surface-action-secondary": string;
        "cjx-colors-surface-action-delete-disabled": string;
        "cjx-colors-surface-action-delete-active": string;
        "cjx-colors-surface-action-delete-hover": string;
        "cjx-colors-surface-action-delete-default": string;
        "cjx-colors-surface-action-primary-disabled": string;
        "cjx-colors-surface-action-primary-active": string;
        "cjx-colors-surface-action-primary-hover": string;
        "cjx-colors-surface-action-primary-default": string;
        "cjx-colors-surface-static-default5": string;
        "cjx-colors-surface-static-default4": string;
        "cjx-colors-surface-static-danger-disabled": string;
        "cjx-colors-surface-static-danger-active": string;
        "cjx-colors-surface-static-danger-hover": string;
        "cjx-colors-surface-static-danger-default": string;
        "cjx-colors-surface-static-warning-disabled": string;
        "cjx-colors-surface-static-warning-active": string;
        "cjx-colors-surface-static-warning-hover": string;
        "cjx-colors-surface-static-warning-default": string;
        "cjx-colors-surface-static-success-disabled": string;
        "cjx-colors-surface-static-success-active": string;
        "cjx-colors-surface-static-success-hover": string;
        "cjx-colors-surface-static-success-default": string;
        "cjx-colors-surface-static-process-disabled": string;
        "cjx-colors-surface-static-process-active": string;
        "cjx-colors-surface-static-process-hover": string;
        "cjx-colors-surface-static-process-default": string;
        "cjx-colors-surface-static-default3": string;
        "cjx-colors-surface-static-default2": string;
        "cjx-colors-surface-static-default1": string;
        "cjx-colors-surface-static-ui-delete": string;
        "cjx-colors-surface-static-ui-primary": string;
        "cjx-colors-surface-static-ui-disabled": string;
        "cjx-colors-surface-static-ui-active": string;
        "cjx-colors-surface-static-ui-hover": string;
        "cjx-colors-surface-static-ui-default": string;
        "cjx-colors-process-95": string;
        "cjx-colors-process-90": string;
        "cjx-colors-process-80": string;
        "cjx-colors-process-70": string;
        "cjx-colors-process-60": string;
        "cjx-colors-process-50": string;
        "cjx-colors-process-40": string;
        "cjx-colors-process-30": string;
        "cjx-colors-process-20": string;
        "cjx-colors-process-10": string;
        "cjx-colors-success-95": string;
        "cjx-colors-success-90": string;
        "cjx-colors-success-80": string;
        "cjx-colors-success-70": string;
        "cjx-colors-success-60": string;
        "cjx-colors-success-50": string;
        "cjx-colors-success-40": string;
        "cjx-colors-success-30": string;
        "cjx-colors-success-20": string;
        "cjx-colors-success-10": string;
        "cjx-colors-warning-95": string;
        "cjx-colors-warning-90": string;
        "cjx-colors-warning-80": string;
        "cjx-colors-warning-70": string;
        "cjx-colors-warning-60": string;
        "cjx-colors-warning-50": string;
        "cjx-colors-warning-40": string;
        "cjx-colors-warning-30": string;
        "cjx-colors-warning-20": string;
        "cjx-colors-warning-10": string;
        "cjx-colors-danger-95": string;
        "cjx-colors-danger-90": string;
        "cjx-colors-danger-80": string;
        "cjx-colors-danger-70": string;
        "cjx-colors-danger-60": string;
        "cjx-colors-danger-50": string;
        "cjx-colors-danger-40": string;
        "cjx-colors-danger-30": string;
        "cjx-colors-danger-20": string;
        "cjx-colors-danger-10": string;
        "cjx-colors-gray-100": string;
        "cjx-colors-gray-90": string;
        "cjx-colors-gray-80": string;
        "cjx-colors-gray-70": string;
        "cjx-colors-gray-60": string;
        "cjx-colors-gray-50": string;
        "cjx-colors-gray-40": string;
        "cjx-colors-gray-30": string;
        "cjx-colors-gray-20": string;
        "cjx-colors-gray-10": string;
        "cjx-colors-gray-5": string;
        "cjx-colors-gray-0": string;
        "cjx-colors-brand-95": string;
        "cjx-colors-brand-90": string;
        "cjx-colors-brand-80": string;
        "cjx-colors-brand-70": string;
        "cjx-colors-brand-60": string;
        "cjx-colors-brand-50": string;
        "cjx-colors-brand-40": string;
        "cjx-colors-brand-30": string;
        "cjx-colors-brand-20": string;
        "cjx-colors-brand-10": string;
    };
    darkColors: {
        "cjx-colors-border-focus": string;
        "cjx-colors-border-danger-accented": string;
        "cjx-colors-border-danger-subdued": string;
        "cjx-colors-border-danger-default": string;
        "cjx-colors-border-warning-accented": string;
        "cjx-colors-border-warning-subdued": string;
        "cjx-colors-border-warning-default": string;
        "cjx-colors-border-success-accented": string;
        "cjx-colors-border-success-subdued": string;
        "cjx-colors-border-success-default": string;
        "cjx-colors-border-process-accented": string;
        "cjx-colors-border-process-subdued": string;
        "cjx-colors-border-process-default": string;
        "cjx-colors-border-primary-accented": string;
        "cjx-colors-border-primary-subdued": string;
        "cjx-colors-border-primary-default": string;
        "cjx-colors-border-inversed": string;
        "cjx-colors-border-accented": string;
        "cjx-colors-border-subdued": string;
        "cjx-colors-border-disabled": string;
        "cjx-colors-border-default": string;
        "cjx-colors-content-subdued": string;
        "cjx-colors-content-danger-subdued": string;
        "cjx-colors-content-danger-default": string;
        "cjx-colors-content-warning-subdued": string;
        "cjx-colors-content-warning-default": string;
        "cjx-colors-content-success-subdued": string;
        "cjx-colors-content-success-default": string;
        "cjx-colors-content-process-subdued": string;
        "cjx-colors-content-process-default": string;
        "cjx-colors-content-primary-subdued": string;
        "cjx-colors-content-primary-default": string;
        "cjx-colors-content-onactioninversed": string;
        "cjx-colors-content-onaction": string;
        "cjx-colors-content-placeholder": string;
        "cjx-colors-content-description": string;
        "cjx-colors-content-disabled": string;
        "cjx-colors-content-inversed": string;
        "cjx-colors-content-default": string;
        "cjx-colors-surface-action-secondary": string;
        "cjx-colors-surface-action-delete-disabled": string;
        "cjx-colors-surface-action-delete-active": string;
        "cjx-colors-surface-action-delete-hover": string;
        "cjx-colors-surface-action-delete-default": string;
        "cjx-colors-surface-action-primary-disabled": string;
        "cjx-colors-surface-action-primary-active": string;
        "cjx-colors-surface-action-primary-hover": string;
        "cjx-colors-surface-action-primary-default": string;
        "cjx-colors-surface-static-default5": string;
        "cjx-colors-surface-static-default4": string;
        "cjx-colors-surface-static-danger-disabled": string;
        "cjx-colors-surface-static-danger-active": string;
        "cjx-colors-surface-static-danger-hover": string;
        "cjx-colors-surface-static-danger-default": string;
        "cjx-colors-surface-static-warning-disabled": string;
        "cjx-colors-surface-static-warning-active": string;
        "cjx-colors-surface-static-warning-hover": string;
        "cjx-colors-surface-static-warning-default": string;
        "cjx-colors-surface-static-success-disabled": string;
        "cjx-colors-surface-static-success-active": string;
        "cjx-colors-surface-static-success-hover": string;
        "cjx-colors-surface-static-success-default": string;
        "cjx-colors-surface-static-process-disabled": string;
        "cjx-colors-surface-static-process-active": string;
        "cjx-colors-surface-static-process-hover": string;
        "cjx-colors-surface-static-process-default": string;
        "cjx-colors-surface-static-default3": string;
        "cjx-colors-surface-static-default2": string;
        "cjx-colors-surface-static-default1": string;
        "cjx-colors-surface-static-ui-delete": string;
        "cjx-colors-surface-static-ui-primary": string;
        "cjx-colors-surface-static-ui-disabled": string;
        "cjx-colors-surface-static-ui-active": string;
        "cjx-colors-surface-static-ui-hover": string;
        "cjx-colors-surface-static-ui-default": string;
        "cjx-colors-process-95": string;
        "cjx-colors-process-90": string;
        "cjx-colors-process-80": string;
        "cjx-colors-process-70": string;
        "cjx-colors-process-60": string;
        "cjx-colors-process-50": string;
        "cjx-colors-process-40": string;
        "cjx-colors-process-30": string;
        "cjx-colors-process-20": string;
        "cjx-colors-process-10": string;
        "cjx-colors-success-95": string;
        "cjx-colors-success-90": string;
        "cjx-colors-success-80": string;
        "cjx-colors-success-70": string;
        "cjx-colors-success-60": string;
        "cjx-colors-success-50": string;
        "cjx-colors-success-40": string;
        "cjx-colors-success-30": string;
        "cjx-colors-success-20": string;
        "cjx-colors-success-10": string;
        "cjx-colors-warning-95": string;
        "cjx-colors-warning-90": string;
        "cjx-colors-warning-80": string;
        "cjx-colors-warning-70": string;
        "cjx-colors-warning-60": string;
        "cjx-colors-warning-50": string;
        "cjx-colors-warning-40": string;
        "cjx-colors-warning-30": string;
        "cjx-colors-warning-20": string;
        "cjx-colors-warning-10": string;
        "cjx-colors-danger-95": string;
        "cjx-colors-danger-90": string;
        "cjx-colors-danger-80": string;
        "cjx-colors-danger-70": string;
        "cjx-colors-danger-60": string;
        "cjx-colors-danger-50": string;
        "cjx-colors-danger-40": string;
        "cjx-colors-danger-30": string;
        "cjx-colors-danger-20": string;
        "cjx-colors-danger-10": string;
        "cjx-colors-gray-100": string;
        "cjx-colors-gray-90": string;
        "cjx-colors-gray-80": string;
        "cjx-colors-gray-70": string;
        "cjx-colors-gray-60": string;
        "cjx-colors-gray-50": string;
        "cjx-colors-gray-40": string;
        "cjx-colors-gray-30": string;
        "cjx-colors-gray-20": string;
        "cjx-colors-gray-10": string;
        "cjx-colors-gray-5": string;
        "cjx-colors-gray-0": string;
        "cjx-colors-brand-95": string;
        "cjx-colors-brand-90": string;
        "cjx-colors-brand-80": string;
        "cjx-colors-brand-70": string;
        "cjx-colors-brand-60": string;
        "cjx-colors-brand-50": string;
        "cjx-colors-brand-40": string;
        "cjx-colors-brand-30": string;
        "cjx-colors-brand-20": string;
        "cjx-colors-brand-10": string;
    };
};
type ApolloTheme = typeof apolloTheme;

declare const apolloTailwindConfig: {
    typography: {
        h1: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        h2: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        h3: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        h4: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        h5: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        body1: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        body2: {
            fontFamily: string;
            fontWeight: string;
            fontSize: string;
        };
        caption: {
            fontFamily: string;
            fontSize: string;
        };
    };
    colors: {
        brand: {
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "95": string;
        };
        gray: {
            "0": string;
            "5": string;
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "100": string;
        };
        danger: {
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "95": string;
        };
        warning: {
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "95": string;
        };
        success: {
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "95": string;
        };
        process: {
            "10": string;
            "20": string;
            "30": string;
            "40": string;
            "50": string;
            "60": string;
            "70": string;
            "80": string;
            "90": string;
            "95": string;
        };
        surface: {
            static: {
                ui: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                    primary: string;
                    delete: string;
                };
                default1: string;
                default2: string;
                default3: string;
                process: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                success: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                warning: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                danger: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                default4: string;
                default5: string;
            };
            action: {
                primary: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                delete: {
                    default: string;
                    hover: string;
                    active: string;
                    disabled: string;
                };
                secondary: string;
            };
        };
        content: {
            default: string;
            inversed: string;
            disabled: string;
            description: string;
            placeholder: string;
            onaction: string;
            onactioninversed: string;
            primary: {
                default: string;
                subdued: string;
            };
            process: {
                default: string;
                subdued: string;
            };
            success: {
                default: string;
                subdued: string;
            };
            warning: {
                default: string;
                subdued: string;
            };
            danger: {
                default: string;
                subdued: string;
            };
            subdued: string;
        };
        border: {
            default: string;
            disabled: string;
            subdued: string;
            accented: string;
            inversed: string;
            primary: {
                default: string;
                subdued: string;
                accented: string;
            };
            process: {
                default: string;
                subdued: string;
                accented: string;
            };
            success: {
                default: string;
                subdued: string;
                accented: string;
            };
            warning: {
                default: string;
                subdued: string;
                accented: string;
            };
            danger: {
                default: string;
                subdued: string;
                accented: string;
            };
            focus: string;
        };
    };
};

declare const typographyVariant: {
    ".typography-h1": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-h2": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-h3": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-h4": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-h5": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-body1": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-body2": {
        fontFamily: string;
        fontWeight: string;
        fontSize: string;
    };
    ".typography-caption": {
        fontFamily: string;
        fontSize: string;
    };
};

/**
 * @description converts the nested theme object to an array of style property string
 *
 * @param config
 * @param isVariable whether to add `--` prefix or not. Default `false`
 *
 * @returns `'property-name:value;'[]` or `'--css-variable-name:value;'[]`
 */
declare function createStyleProperties<T extends object>(config: T, isVariable?: boolean): string[];
declare function createTypographyAliasClasses(configs: Record<string, string>): string[];

export { type ApolloTheme, apolloTailwindConfig, apolloTheme, createStyleProperties, createTypographyAliasClasses, typographyVariant };
