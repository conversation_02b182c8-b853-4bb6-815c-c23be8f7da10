# Storybook Migration Progress

This file tracks the progress of migrating component documentation from Docusaurus to Storybook stories.

## Current Status

- ✅ = Completed
- 🚧 = In Progress
- ❌ = Not Started

## Legacy Design System (@design-systems/apollo-ui)

### Input Components

- [x] Input - ✅ Completed (already existed)
- [x] Checkbox - ✅ Completed (already existed)
- [x] Radio - ✅ Completed (9 stories)
- [x] Switch - ✅ Completed (8 stories)
- [x] Select - ✅ Completed (8 stories)
- [x] Autocomplete - ✅ Completed (9 stories)
- [x] DateInput - ✅ Completed (10 stories)
- [x] DatePicker - ✅ Completed (9 stories) - Date selection with locales, eras, ranges, and view modes
- [x] UploadBox - ✅ Completed (8 stories)
- [x] FormControl - ✅ Completed (8 stories)
- [x] IconButton - ✅ Completed (9 stories) - Icon-only buttons with sizes, variants, and toolbar examples

### General Components

- [x] Button - ✅ Completed (18 stories)
- [x] Typography - ✅ Completed (8 stories)

### Navigation Components (4 completed)

- [x] **Tabs** (7 stories) - Tab navigation with multiple panels, controlled state, icons, badges, scrolling
  - Basic tabs, full width, controlled, with icons/badges, scrollable, content types, nested
- [x] **Accordion** (7 stories) - Collapsible content sections with state management, variants, styling
  - Basic accordion, multiple accordions, controlled state, variants/styling, icon positioning, complex content, FAQ example
- [x] **Breadcrumbs** (7 stories) - Navigation hierarchy with collapsible items, separators, content types
  - Basic breadcrumbs, with icons, collapsed ellipsis, custom separators, content types, color variants, real-world examples
- [x] **Pagination** (7 stories) - Page navigation with configurable display, states, real-world usage
  - Basic pagination, controlled state, configurations, button visibility, disabled states, table example, search results

### Data Display Components (2 completed)

- [x] **Chip** (8 stories) - Compact elements for tags, filters, status indicators with deletable functionality
  - Basic chip, color variants, border radius, with decorators, deletable chips, text truncation, disabled states, filter/tag examples

## Data Display Components (5/X)

- [x] **Icon** ✅ (8 stories) - Flexible icon display with size variants, color themes, library showcase, and custom SVG support
- [x] **ProductCard** ✅ (8 stories) - E-commerce product display with images, pricing, ratings, and interactive features
- [x] **Badge** ✅ (8 stories) - Status indicators and labels with color variants, icons, and wrapper functionality
- [x] **Typography** ✅ (9 stories) - Text display with legacy and Material Design 3 levels, alignments, and colors
- [ ] **Card** - General purpose card container
- [ ] **Avatar** - User profile pictures and initials
- [ ] **Image** - Enhanced image display with loading states
- [ ] **Divider** - Visual separation elements
- [ ] **Skeleton** - Loading state placeholders
- [ ] **Progress** - Progress indicators and bars
- [ ] **Tag** - Categorization labels
- [ ] **Tooltip** - Contextual information overlays

## Utility Components (3/X)

- [x] **Portal** ✅ (8 stories) - Render content outside component tree for modals, tooltips, and overlays
- [x] **SortingIcon** ✅ (8 stories) - Sortable column indicators with ascending, descending, and default states
- [x] **FloatButton** ✅ (9 stories) - Floating action button with expand/collapse, scroll integration, and icon positioning
- [ ] **Image** - Enhanced image display with loading states
- [ ] **Divider** - Visual separation elements
- [ ] **Skeleton** - Loading state placeholders
- [ ] **Progress** - Progress indicators and bars
- [ ] **Tag** - Categorization labels
- [ ] **Tooltip** - Contextual information overlays
  - Basic icon, sizes, colors, variants, icon library, interactive examples, contextual usage, custom SVG icons

## Summary

- **Total Components Completed**: 30
- **Total Stories Created**: 250+
- **Categories**: Input Components (11), General Components (2), Feedback Components (3), Navigation Components (4), Data Display Components (5), Utility Components (5)

### Feedback Components

- [x] Alert - ✅ Completed (8 stories)
- [x] Modal - ✅ Completed (8 stories)
- [x] Toast - ✅ Completed (8 stories)

## @apollo/ui Components (New Design System)

Located at: `packages/apollo-ui/src/components/`

### Layout & Navigation

- [ ] ❌ accordion
- [x] ✅ breadcrumbs (Already has story)
- [ ] ❌ navbar
- [ ] ❌ tabs
- [ ] ❌ capsule-tab
- [ ] ❌ drawer
- [ ] ❌ modal
- [ ] ❌ pagination

### Inputs & Forms

- [x] ✅ button (Already has story)
- [ ] ❌ input
- [ ] ❌ textarea
- [ ] ❌ checkbox
- [ ] ❌ radio
- [ ] ❌ switch
- [ ] ❌ select
- [ ] ❌ autocomplete
- [ ] ❌ date-picker
- [ ] ❌ upload-box
- [ ] ❌ field

### Data Display

- [ ] ❌ badge
- [ ] ❌ chip
- [ ] ❌ product-card
- [ ] ❌ typography

### Feedback

- [ ] ❌ alert

### Utilities

- [ ] ❌ icon-button
- [ ] ❌ float-button
- [ ] ❌ sorting-icon
- [ ] ❌ menu-item
- [ ] ❌ portal

## @design-systems/apollo-ui Components (Legacy Design System)

Located at: `packages/ui/src/components/`

### Layout & Navigation

- [ ] ❌ Accordion
- [ ] ❌ Breadcrumbs
- [ ] ❌ CapsuleTab
- [ ] ❌ Drawer
- [ ] ❌ Modal
- [ ] ❌ NavBar
- [ ] ❌ Pagination
- [ ] ❌ Sidebar
- [ ] ❌ Tabs

### Inputs & Forms

- [x] ✅ Button (Already has story)
- [x] ✅ Input (Completed - comprehensive story with all variants)
- [x] ✅ Checkbox (Completed - comprehensive story with all states and interactive examples)
- [ ] ❌ Radio
- [ ] ❌ Switch
- [ ] ❌ Select
- [ ] ❌ Autocomplete
- [ ] ❌ DateInput
- [ ] ❌ DatePicker
- [ ] ❌ FormControl
- [ ] ❌ UploadBox

### Data Display

- [ ] ❌ Chip
- [ ] ❌ NotificationBadge
- [ ] ❌ ProductCard
- [ ] ❌ Typography
- [ ] ❌ Icon

### Feedback

- [ ] ❌ Alert
- [ ] ❌ Toast
- [ ] ❌ EmptyState

### Utilities

- [ ] ❌ IconButton
- [ ] ❌ FloatButton
- [ ] ❌ SortingIcon
- [ ] ❌ MenuItem
- [x] ✅ MenuOption (9 stories)
- [x] ✅ MenuList (10 stories)
- [ ] ❌ Option
- [ ] ❌ Divider
- [ ] ❌ ScrollTriggerElement

## Next Steps

1. Start with @apollo/ui components (priority)
2. Begin with layout and navigation components
3. Read existing documentation from `apps/docs/docs/Components/`
4. Create comprehensive MDX stories with:
   - Component overview
   - Props documentation
   - Usage examples
   - Best practices
5. Test each story using Playwright

## Notes

- Storybook is running on localhost:6006
- Legacy documentation is on port 3000
- Both design systems are still in use across different projects
- Stories location: `apps/apollo-docs/src/stories/`
  - `apollo-ui/` for new design system
  - `legacy/` for legacy design system
